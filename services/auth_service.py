"""Authentication service for user registration and login"""
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, Dict, Any, Tuple
import secrets
import uuid
import logging
from jose import JWTError, jwt

from models.user import User, UserSession
from schemas.auth import (
    UserRegistrationRequest, 
    UserLoginRequest, 
    UserAuthResponse,
    UserProfileResponse
)

logger = logging.getLogger(__name__)

# JWT Configuration - In production, use environment variables
SECRET_KEY = "your-secret-key-change-in-production"  # TODO: Move to config
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
REFRESH_TOKEN_EXPIRE_DAYS = 30


class AuthenticationService:
    """Service for user authentication and session management"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def register_user(self, registration_data: UserRegistrationRequest) -> Tuple[bool, str, Optional[User]]:
        """
        Register a new user
        
        Returns:
            Tuple of (success, message, user_object)
        """
        try:
            # Check if user already exists
            existing_user = self.db.query(User).filter(User.email == registration_data.email).first()
            if existing_user:
                return False, "User with this email already exists", None
            
            # Generate unique user ID
            user_id = f"user_{uuid.uuid4().hex[:12]}"
            
            # Create new user
            new_user = User(
                user_id=user_id,
                email=registration_data.email,
                name=registration_data.name,
                first_name=registration_data.first_name,
                last_name=registration_data.last_name,
                is_active=True,
                is_verified=False,  # Require email verification
                subscription_tier='free'
            )
            
            # Set password
            new_user.set_password(registration_data.password)
            
            # Generate email verification token
            verification_token = new_user.generate_email_verification_token()
            
            # Save to database
            self.db.add(new_user)
            self.db.commit()
            self.db.refresh(new_user)
            
            logger.info(f"User registered successfully: {new_user.email}")
            
            # TODO: Send verification email with verification_token
            
            return True, "User registered successfully. Please check your email for verification.", new_user
            
        except IntegrityError as e:
            self.db.rollback()
            logger.error(f"Database integrity error during registration: {e}")
            return False, "Registration failed due to data conflict", None
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error during user registration: {e}")
            return False, "Registration failed due to server error", None
    
    def authenticate_user(self, login_data: UserLoginRequest, ip_address: str = None, user_agent: str = None) -> Tuple[bool, str, Optional[UserAuthResponse]]:
        """
        Authenticate user and create session
        
        Returns:
            Tuple of (success, message, auth_response)
        """
        try:
            # Find user by email
            user = self.db.query(User).filter(
                User.email == login_data.email,
                User.is_active == True
            ).first()
            
            if not user:
                return False, "Invalid email or password", None
            
            # Check if account is locked
            if user.is_locked():
                return False, "Account is temporarily locked due to failed login attempts", None
            
            # Verify password
            if not user.verify_password(login_data.password):
                user.increment_failed_login()
                self.db.commit()
                return False, "Invalid email or password", None
            
            # Check if email is verified (optional - can be disabled for development)
            # if not user.is_verified:
            #     return False, "Please verify your email before logging in", None
            
            # Reset failed login attempts
            user.reset_failed_login()
            
            # Create session tokens
            access_token = self._create_access_token(user.user_id)
            refresh_token = self._create_refresh_token(user.user_id)
            
            # Create session record
            session = UserSession(
                user_id=user.user_id,
                session_token=access_token,
                refresh_token=refresh_token,
                device_info=login_data.device_info,
                ip_address=ip_address,
                user_agent=user_agent,
                expires_at=datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS if login_data.remember_me else 1)
            )
            
            self.db.add(session)
            self.db.commit()
            
            # Create response
            user_profile = UserProfileResponse.from_orm(user)
            auth_response = UserAuthResponse(
                access_token=access_token,
                refresh_token=refresh_token,
                token_type="bearer",
                expires_in=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
                user=user_profile
            )
            
            logger.info(f"User authenticated successfully: {user.email}")
            return True, "Login successful", auth_response
            
        except Exception as e:
            logger.error(f"Error during user authentication: {e}")
            return False, "Authentication failed due to server error", None
    
    def refresh_token(self, refresh_token: str) -> Tuple[bool, str, Optional[UserAuthResponse]]:
        """
        Refresh access token using refresh token
        
        Returns:
            Tuple of (success, message, auth_response)
        """
        try:
            # Find active session with refresh token
            session = self.db.query(UserSession).filter(
                UserSession.refresh_token == refresh_token,
                UserSession.is_active == True
            ).first()
            
            if not session or session.is_expired():
                return False, "Invalid or expired refresh token", None
            
            # Get user
            user = self.db.query(User).filter(
                User.user_id == session.user_id,
                User.is_active == True
            ).first()
            
            if not user:
                return False, "User not found or inactive", None
            
            # Create new tokens
            new_access_token = self._create_access_token(user.user_id)
            new_refresh_token = self._create_refresh_token(user.user_id)
            
            # Update session
            session.session_token = new_access_token
            session.refresh_token = new_refresh_token
            session.update_activity()
            
            self.db.commit()
            
            # Create response
            user_profile = UserProfileResponse.from_orm(user)
            auth_response = UserAuthResponse(
                access_token=new_access_token,
                refresh_token=new_refresh_token,
                token_type="bearer",
                expires_in=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
                user=user_profile
            )
            
            return True, "Token refreshed successfully", auth_response
            
        except Exception as e:
            logger.error(f"Error during token refresh: {e}")
            return False, "Token refresh failed", None
    
    def logout_user(self, access_token: str) -> Tuple[bool, str]:
        """
        Logout user by invalidating session
        
        Returns:
            Tuple of (success, message)
        """
        try:
            # Find and deactivate session
            session = self.db.query(UserSession).filter(
                UserSession.session_token == access_token,
                UserSession.is_active == True
            ).first()
            
            if session:
                session.is_active = False
                self.db.commit()
                logger.info(f"User logged out: {session.user_id}")
            
            return True, "Logged out successfully"
            
        except Exception as e:
            logger.error(f"Error during logout: {e}")
            return False, "Logout failed"
    
    def get_current_user(self, access_token: str) -> Optional[User]:
        """Get current user from access token"""
        try:
            # Decode JWT token
            payload = jwt.decode(access_token, SECRET_KEY, algorithms=[ALGORITHM])
            user_id = payload.get("sub")
            
            if not user_id:
                return None
            
            # Find user
            user = self.db.query(User).filter(
                User.user_id == user_id,
                User.is_active == True
            ).first()
            
            return user
            
        except JWTError:
            return None
    
    def _create_access_token(self, user_id: str) -> str:
        """Create JWT access token"""
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        payload = {
            "sub": user_id,
            "exp": expire,
            "type": "access"
        }
        return jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)
    
    def _create_refresh_token(self, user_id: str) -> str:
        """Create JWT refresh token"""
        expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        payload = {
            "sub": user_id,
            "exp": expire,
            "type": "refresh"
        }
        return jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)
