"""Enterprise AI Service for advanced analytics and predictive modeling.

This service provides sophisticated AI capabilities for enterprise deployments
including predictive analytics, intelligent optimization, automated insights,
and machine learning-powered recommendations for organizational management.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
import pickle
import os
from collections import defaultdict

# Advanced ML libraries
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, IsolationForest
from sklearn.linear_model import LinearRegression, LogisticRegression
from sklearn.cluster import KMeans, DBSCAN
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_squared_error, accuracy_score, silhouette_score
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.decomposition import PCA
import joblib

from models.enterprise import EnterpriseOrganization, Department, EnterpriseUser, UserLicense, OrganizationAnalytics
from models.progress_tracking import StudySession, PracticeTestResult, LearningGoal, Achievement
from models.certification import Certification

logger = logging.getLogger(__name__)


class EnterpriseAIService:
    """Advanced AI service for enterprise analytics and predictive modeling."""
    
    def __init__(self, db: Session):
        self.db = db
        self.models_dir = "enterprise_ai_models"
        self._ensure_models_directory()
        self._load_or_initialize_models()
    
    def _ensure_models_directory(self):
        """Ensure the models directory exists."""
        if not os.path.exists(self.models_dir):
            os.makedirs(self.models_dir)
    
    def _load_or_initialize_models(self):
        """Load existing AI models or initialize new ones."""
        try:
            # Predictive models
            self.user_success_predictor = self._load_model('user_success_predictor.pkl')
            self.churn_predictor = self._load_model('churn_predictor.pkl')
            self.performance_forecaster = self._load_model('performance_forecaster.pkl')
            self.license_optimizer = self._load_model('license_optimizer.pkl')
            
            # Clustering models
            self.user_segmentation_model = self._load_model('user_segmentation.pkl')
            self.organization_classifier = self._load_model('organization_classifier.pkl')
            
            # Anomaly detection
            self.anomaly_detector = self._load_model('anomaly_detector.pkl')
            
            # Optimization models
            self.resource_optimizer = self._load_model('resource_optimizer.pkl')
            self.cost_predictor = self._load_model('cost_predictor.pkl')
            
            logger.info("Successfully loaded existing AI models")
        
        except Exception as e:
            logger.info(f"Initializing new AI models: {e}")
            self._initialize_new_models()
    
    def _initialize_new_models(self):
        """Initialize new AI models with default configurations."""
        # Predictive models
        self.user_success_predictor = RandomForestRegressor(n_estimators=100, random_state=42)
        self.churn_predictor = GradientBoostingRegressor(n_estimators=100, random_state=42)
        self.performance_forecaster = RandomForestRegressor(n_estimators=80, random_state=42)
        self.license_optimizer = LinearRegression()
        
        # Clustering models
        self.user_segmentation_model = KMeans(n_clusters=5, random_state=42)
        self.organization_classifier = KMeans(n_clusters=4, random_state=42)
        
        # Anomaly detection
        self.anomaly_detector = IsolationForest(contamination=0.1, random_state=42)
        
        # Optimization models
        self.resource_optimizer = RandomForestRegressor(n_estimators=50, random_state=42)
        self.cost_predictor = GradientBoostingRegressor(n_estimators=80, random_state=42)
        
        # Preprocessing tools
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        
        logger.info("Initialized new AI models")
    
    def _load_model(self, filename: str):
        """Load a model from disk."""
        filepath = os.path.join(self.models_dir, filename)
        return joblib.load(filepath)
    
    def _save_model(self, model, filename: str):
        """Save a model to disk."""
        filepath = os.path.join(self.models_dir, filename)
        joblib.dump(model, filepath)
    
    # Predictive Analytics
    
    def predict_user_success_probability(self, org_id: int, user_features: Dict[str, Any]) -> Dict[str, Any]:
        """Predict the probability of user success in certification."""
        try:
            # Extract features for prediction
            features = self._extract_user_features(user_features)
            
            # Make prediction
            success_probability = self.user_success_predictor.predict([features])[0]
            success_probability = max(0.0, min(1.0, success_probability))  # Clamp to [0,1]
            
            # Calculate confidence interval
            confidence = self._calculate_prediction_confidence(features, 'user_success')
            
            # Generate insights
            insights = self._generate_success_insights(features, success_probability)
            
            return {
                'success_probability': success_probability,
                'confidence_score': confidence,
                'risk_level': self._categorize_risk(success_probability),
                'insights': insights,
                'recommendations': self._generate_success_recommendations(features, success_probability)
            }
        
        except Exception as e:
            logger.error(f"Error predicting user success: {e}")
            return {'error': str(e)}
    
    def predict_churn_risk(self, org_id: int, user_id: str) -> Dict[str, Any]:
        """Predict the risk of user churn."""
        try:
            # Gather user engagement data
            user_data = self._gather_user_engagement_data(org_id, user_id)
            
            if not user_data:
                return {'error': 'Insufficient data for churn prediction'}
            
            # Extract features
            features = self._extract_churn_features(user_data)
            
            # Make prediction
            churn_risk = self.churn_predictor.predict([features])[0]
            churn_risk = max(0.0, min(1.0, churn_risk))
            
            # Calculate time to churn
            time_to_churn = self._estimate_time_to_churn(features, churn_risk)
            
            # Generate intervention recommendations
            interventions = self._recommend_churn_interventions(features, churn_risk)
            
            return {
                'churn_risk': churn_risk,
                'risk_category': self._categorize_churn_risk(churn_risk),
                'estimated_time_to_churn_days': time_to_churn,
                'key_risk_factors': self._identify_churn_risk_factors(features),
                'intervention_recommendations': interventions,
                'confidence_score': self._calculate_prediction_confidence(features, 'churn')
            }
        
        except Exception as e:
            logger.error(f"Error predicting churn risk: {e}")
            return {'error': str(e)}
    
    def forecast_organization_performance(self, org_id: int, forecast_days: int = 90) -> Dict[str, Any]:
        """Forecast organization performance metrics."""
        try:
            # Gather historical performance data
            historical_data = self._gather_organization_performance_data(org_id)
            
            if len(historical_data) < 30:  # Need at least 30 days of data
                return {'error': 'Insufficient historical data for forecasting'}
            
            # Prepare time series data
            df = pd.DataFrame(historical_data)
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date')
            
            # Generate forecasts for key metrics
            forecasts = {}
            metrics = ['active_users', 'study_hours', 'completion_rate', 'satisfaction_score']
            
            for metric in metrics:
                if metric in df.columns:
                    forecast = self._forecast_metric(df, metric, forecast_days)
                    forecasts[metric] = forecast
            
            # Calculate forecast confidence
            confidence = self._calculate_forecast_confidence(df, forecasts)
            
            # Generate insights and recommendations
            insights = self._generate_performance_insights(forecasts)
            recommendations = self._generate_performance_recommendations(forecasts)
            
            return {
                'forecasts': forecasts,
                'confidence_score': confidence,
                'insights': insights,
                'recommendations': recommendations,
                'forecast_period_days': forecast_days,
                'generated_at': datetime.now().isoformat()
            }
        
        except Exception as e:
            logger.error(f"Error forecasting organization performance: {e}")
            return {'error': str(e)}
    
    # Intelligent Optimization
    
    def optimize_license_allocation(self, org_id: int) -> Dict[str, Any]:
        """Optimize license allocation across users and departments."""
        try:
            # Gather license usage data
            usage_data = self._gather_license_usage_data(org_id)
            
            if not usage_data:
                return {'error': 'No license usage data available'}
            
            # Analyze current allocation efficiency
            current_efficiency = self._calculate_allocation_efficiency(usage_data)
            
            # Generate optimization recommendations
            optimizations = self._generate_license_optimizations(usage_data)
            
            # Calculate potential savings
            potential_savings = self._calculate_potential_savings(usage_data, optimizations)
            
            # Predict optimal allocation
            optimal_allocation = self._predict_optimal_allocation(usage_data)
            
            return {
                'current_efficiency': current_efficiency,
                'optimization_recommendations': optimizations,
                'potential_cost_savings': potential_savings,
                'optimal_allocation': optimal_allocation,
                'implementation_priority': self._prioritize_optimizations(optimizations),
                'roi_estimate': self._estimate_optimization_roi(potential_savings, optimizations)
            }
        
        except Exception as e:
            logger.error(f"Error optimizing license allocation: {e}")
            return {'error': str(e)}
    
    def optimize_resource_allocation(self, org_id: int) -> Dict[str, Any]:
        """Optimize resource allocation across departments and users."""
        try:
            # Gather resource usage data
            resource_data = self._gather_resource_data(org_id)
            
            # Analyze current resource utilization
            utilization_analysis = self._analyze_resource_utilization(resource_data)
            
            # Generate optimization strategies
            optimization_strategies = self._generate_resource_optimizations(resource_data)
            
            # Calculate efficiency improvements
            efficiency_gains = self._calculate_efficiency_gains(resource_data, optimization_strategies)
            
            return {
                'current_utilization': utilization_analysis,
                'optimization_strategies': optimization_strategies,
                'efficiency_improvements': efficiency_gains,
                'implementation_roadmap': self._create_optimization_roadmap(optimization_strategies),
                'success_metrics': self._define_optimization_metrics(optimization_strategies)
            }
        
        except Exception as e:
            logger.error(f"Error optimizing resource allocation: {e}")
            return {'error': str(e)}
    
    # Automated Insights
    
    def generate_automated_insights(self, org_id: int) -> Dict[str, Any]:
        """Generate automated insights using AI analysis."""
        try:
            insights = {
                'performance_insights': self._generate_performance_insights_ai(org_id),
                'user_behavior_insights': self._generate_user_behavior_insights(org_id),
                'efficiency_insights': self._generate_efficiency_insights(org_id),
                'cost_optimization_insights': self._generate_cost_insights(org_id),
                'predictive_insights': self._generate_predictive_insights(org_id),
                'anomaly_insights': self._detect_anomalies(org_id)
            }
            
            # Prioritize insights by impact
            prioritized_insights = self._prioritize_insights(insights)
            
            # Generate action recommendations
            action_recommendations = self._generate_action_recommendations(prioritized_insights)
            
            return {
                'insights': prioritized_insights,
                'action_recommendations': action_recommendations,
                'insight_summary': self._create_insight_summary(prioritized_insights),
                'confidence_scores': self._calculate_insight_confidence(prioritized_insights),
                'generated_at': datetime.now().isoformat()
            }
        
        except Exception as e:
            logger.error(f"Error generating automated insights: {e}")
            return {'error': str(e)}
    
    def detect_anomalies(self, org_id: int) -> Dict[str, Any]:
        """Detect anomalies in organization data using AI."""
        try:
            # Gather data for anomaly detection
            data = self._gather_anomaly_detection_data(org_id)
            
            if len(data) < 50:  # Need sufficient data for anomaly detection
                return {'error': 'Insufficient data for anomaly detection'}
            
            # Prepare features
            features = self._prepare_anomaly_features(data)
            
            # Detect anomalies
            anomaly_scores = self.anomaly_detector.decision_function(features)
            anomalies = self.anomaly_detector.predict(features)
            
            # Identify anomalous records
            anomalous_records = []
            for i, (score, is_anomaly) in enumerate(zip(anomaly_scores, anomalies)):
                if is_anomaly == -1:  # Anomaly detected
                    anomalous_records.append({
                        'record_index': i,
                        'anomaly_score': float(score),
                        'data': data[i],
                        'severity': self._categorize_anomaly_severity(score)
                    })
            
            # Generate explanations
            explanations = self._explain_anomalies(anomalous_records, features)
            
            # Recommend actions
            recommendations = self._recommend_anomaly_actions(anomalous_records)
            
            return {
                'anomalies_detected': len(anomalous_records),
                'anomalous_records': anomalous_records,
                'explanations': explanations,
                'recommendations': recommendations,
                'detection_confidence': self._calculate_anomaly_confidence(anomaly_scores)
            }
        
        except Exception as e:
            logger.error(f"Error detecting anomalies: {e}")
            return {'error': str(e)}
    
    # User Segmentation and Clustering
    
    def segment_users(self, org_id: int) -> Dict[str, Any]:
        """Segment users using AI clustering algorithms."""
        try:
            # Gather user data for segmentation
            user_data = self._gather_user_segmentation_data(org_id)
            
            if len(user_data) < 10:  # Need minimum users for segmentation
                return {'error': 'Insufficient users for segmentation'}
            
            # Prepare features
            features = self._prepare_segmentation_features(user_data)
            
            # Perform clustering
            clusters = self.user_segmentation_model.fit_predict(features)
            
            # Analyze segments
            segments = self._analyze_user_segments(user_data, clusters)
            
            # Generate segment profiles
            segment_profiles = self._create_segment_profiles(segments)
            
            # Recommend strategies for each segment
            segment_strategies = self._recommend_segment_strategies(segment_profiles)
            
            return {
                'total_segments': len(segment_profiles),
                'segment_profiles': segment_profiles,
                'segment_strategies': segment_strategies,
                'segmentation_quality': self._evaluate_segmentation_quality(features, clusters),
                'user_assignments': self._create_user_segment_assignments(user_data, clusters)
            }
        
        except Exception as e:
            logger.error(f"Error segmenting users: {e}")
            return {'error': str(e)}
    
    # Model Training and Management
    
    def train_enterprise_models(self, org_id: Optional[int] = None) -> Dict[str, Any]:
        """Train or retrain enterprise AI models with latest data."""
        try:
            logger.info(f"Training enterprise AI models for organization {org_id}")
            
            training_results = {}
            
            # Train predictive models
            training_results['user_success_predictor'] = self._train_user_success_model(org_id)
            training_results['churn_predictor'] = self._train_churn_model(org_id)
            training_results['performance_forecaster'] = self._train_performance_model(org_id)
            
            # Train optimization models
            training_results['license_optimizer'] = self._train_license_optimization_model(org_id)
            training_results['resource_optimizer'] = self._train_resource_optimization_model(org_id)
            
            # Train clustering models
            training_results['user_segmentation'] = self._train_segmentation_model(org_id)
            training_results['anomaly_detector'] = self._train_anomaly_detection_model(org_id)
            
            # Save trained models
            self._save_all_models()
            
            # Calculate overall training success
            overall_success = self._calculate_training_success(training_results)
            
            return {
                'training_results': training_results,
                'overall_success_rate': overall_success,
                'models_updated': list(training_results.keys()),
                'training_date': datetime.now().isoformat(),
                'next_training_recommended': (datetime.now() + timedelta(days=7)).isoformat()
            }
        
        except Exception as e:
            logger.error(f"Error training enterprise models: {e}")
            return {'error': str(e)}
    
    def _save_all_models(self):
        """Save all trained models to disk."""
        models_to_save = {
            'user_success_predictor.pkl': self.user_success_predictor,
            'churn_predictor.pkl': self.churn_predictor,
            'performance_forecaster.pkl': self.performance_forecaster,
            'license_optimizer.pkl': self.license_optimizer,
            'user_segmentation.pkl': self.user_segmentation_model,
            'organization_classifier.pkl': self.organization_classifier,
            'anomaly_detector.pkl': self.anomaly_detector,
            'resource_optimizer.pkl': self.resource_optimizer,
            'cost_predictor.pkl': self.cost_predictor
        }
        
        for filename, model in models_to_save.items():
            try:
                self._save_model(model, filename)
                logger.info(f"Saved model: {filename}")
            except Exception as e:
                logger.error(f"Error saving model {filename}: {e}")
    
    # Helper Methods (Implementation stubs - would be fully implemented)
    
    def _extract_user_features(self, user_features: Dict[str, Any]) -> List[float]:
        """Extract numerical features for ML models."""
        # Implementation would extract and normalize features
        return [0.5, 0.7, 0.3, 0.8, 0.6]  # Placeholder
    
    def _calculate_prediction_confidence(self, features: List[float], model_type: str) -> float:
        """Calculate confidence score for predictions."""
        # Implementation would calculate confidence based on model uncertainty
        return 0.85  # Placeholder
    
    def _categorize_risk(self, probability: float) -> str:
        """Categorize risk level based on probability."""
        if probability >= 0.8:
            return "Low Risk"
        elif probability >= 0.6:
            return "Medium Risk"
        elif probability >= 0.4:
            return "High Risk"
        else:
            return "Very High Risk"
    
    def _generate_success_insights(self, features: List[float], probability: float) -> List[str]:
        """Generate insights about success probability."""
        insights = []
        if probability > 0.8:
            insights.append("User shows strong indicators for certification success")
        elif probability < 0.4:
            insights.append("User may need additional support to succeed")
        return insights
    
    def _generate_success_recommendations(self, features: List[float], probability: float) -> List[str]:
        """Generate recommendations to improve success probability."""
        recommendations = []
        if probability < 0.6:
            recommendations.append("Increase study time and consistency")
            recommendations.append("Focus on weak knowledge areas")
        return recommendations

    def _gather_user_engagement_data(self, org_id: int, user_id: str) -> Optional[Dict[str, Any]]:
        """Gather user engagement data for churn prediction."""
        try:
            # Get user from organization
            user = self.db.query(EnterpriseUser).filter(
                EnterpriseUser.organization_id == org_id,
                EnterpriseUser.user_id == user_id
            ).first()

            if not user:
                return None

            # Get study sessions
            sessions = self.db.query(StudySession).filter(
                StudySession.user_id == user_id
            ).order_by(StudySession.started_at.desc()).limit(100).all()

            # Get test results
            test_results = self.db.query(PracticeTestResult).filter(
                PracticeTestResult.user_id == user_id
            ).order_by(PracticeTestResult.created_at.desc()).limit(50).all()

            # Calculate engagement metrics
            recent_sessions = [s for s in sessions if s.started_at and s.started_at > datetime.now() - timedelta(days=30)]

            return {
                'user_id': user_id,
                'total_sessions': len(sessions),
                'recent_sessions': len(recent_sessions),
                'avg_session_duration': np.mean([s.duration_minutes for s in sessions if s.duration_minutes]) if sessions else 0,
                'last_login': user.last_login,
                'days_since_last_login': (datetime.now() - user.last_login).days if user.last_login else 999,
                'test_count': len(test_results),
                'avg_test_score': np.mean([t.percentage for t in test_results]) if test_results else 0,
                'account_age_days': (datetime.now() - user.created_at).days if user.created_at else 0
            }

        except Exception as e:
            logger.error(f"Error gathering user engagement data: {e}")
            return None

    def _extract_churn_features(self, user_data: Dict[str, Any]) -> List[float]:
        """Extract features for churn prediction."""
        return [
            user_data.get('days_since_last_login', 999) / 30.0,  # Normalized
            user_data.get('recent_sessions', 0) / 10.0,  # Normalized
            user_data.get('avg_session_duration', 0) / 60.0,  # Normalized
            user_data.get('avg_test_score', 0) / 100.0,  # Normalized
            min(user_data.get('account_age_days', 0) / 365.0, 1.0)  # Normalized
        ]

    def _estimate_time_to_churn(self, features: List[float], churn_risk: float) -> int:
        """Estimate time to churn in days."""
        if churn_risk > 0.8:
            return 7  # High risk - likely to churn within a week
        elif churn_risk > 0.6:
            return 30  # Medium risk - likely to churn within a month
        elif churn_risk > 0.4:
            return 90  # Low-medium risk - likely to churn within 3 months
        else:
            return 365  # Low risk - unlikely to churn within a year

    def _categorize_churn_risk(self, churn_risk: float) -> str:
        """Categorize churn risk level."""
        if churn_risk >= 0.8:
            return "Critical"
        elif churn_risk >= 0.6:
            return "High"
        elif churn_risk >= 0.4:
            return "Medium"
        elif churn_risk >= 0.2:
            return "Low"
        else:
            return "Very Low"

    def _identify_churn_risk_factors(self, features: List[float]) -> List[str]:
        """Identify key risk factors for churn."""
        risk_factors = []

        if features[0] > 0.5:  # Days since last login
            risk_factors.append("Infrequent login activity")
        if features[1] < 0.3:  # Recent sessions
            risk_factors.append("Low recent engagement")
        if features[2] < 0.5:  # Session duration
            risk_factors.append("Short study sessions")
        if features[3] < 0.6:  # Test scores
            risk_factors.append("Below average performance")

        return risk_factors

    def _recommend_churn_interventions(self, features: List[float], churn_risk: float) -> List[str]:
        """Recommend interventions to reduce churn risk."""
        interventions = []

        if churn_risk > 0.6:
            interventions.append("Send personalized re-engagement email")
            interventions.append("Offer one-on-one support session")

        if features[0] > 0.5:  # Infrequent login
            interventions.append("Send reminder notifications")

        if features[3] < 0.6:  # Low performance
            interventions.append("Provide additional learning resources")
            interventions.append("Suggest easier study materials")

        return interventions

    def _gather_organization_performance_data(self, org_id: int) -> List[Dict[str, Any]]:
        """Gather historical performance data for forecasting."""
        try:
            # Get analytics data for the last 6 months
            end_date = datetime.now()
            start_date = end_date - timedelta(days=180)

            analytics = self.db.query(OrganizationAnalytics).filter(
                OrganizationAnalytics.organization_id == org_id,
                OrganizationAnalytics.period_start >= start_date
            ).order_by(OrganizationAnalytics.period_start).all()

            performance_data = []
            for record in analytics:
                performance_data.append({
                    'date': record.period_start.strftime('%Y-%m-%d'),
                    'active_users': record.active_users,
                    'study_hours': record.total_study_hours,
                    'completion_rate': record.certification_completion_rate or 0,
                    'satisfaction_score': record.user_satisfaction_score or 0
                })

            return performance_data

        except Exception as e:
            logger.error(f"Error gathering performance data: {e}")
            return []

    def _forecast_metric(self, df: pd.DataFrame, metric: str, forecast_days: int) -> Dict[str, Any]:
        """Forecast a specific metric using time series analysis."""
        try:
            # Simple linear trend forecasting (in production, would use more sophisticated methods)
            values = df[metric].values
            dates = pd.to_datetime(df['date'])

            # Calculate trend
            x = np.arange(len(values))
            slope, intercept = np.polyfit(x, values, 1)

            # Generate forecast
            future_x = np.arange(len(values), len(values) + forecast_days)
            forecast_values = slope * future_x + intercept

            # Generate future dates
            last_date = dates.iloc[-1]
            future_dates = [last_date + timedelta(days=i+1) for i in range(forecast_days)]

            return {
                'metric': metric,
                'forecast_values': forecast_values.tolist(),
                'forecast_dates': [d.strftime('%Y-%m-%d') for d in future_dates],
                'trend': 'increasing' if slope > 0 else 'decreasing',
                'trend_strength': abs(slope)
            }

        except Exception as e:
            logger.error(f"Error forecasting metric {metric}: {e}")
            return {'error': str(e)}

    def _calculate_forecast_confidence(self, df: pd.DataFrame, forecasts: Dict[str, Any]) -> float:
        """Calculate confidence in forecasts based on historical data quality."""
        # Simple confidence calculation based on data consistency
        confidence_scores = []

        for metric in ['active_users', 'study_hours', 'completion_rate']:
            if metric in df.columns:
                values = df[metric].values
                if len(values) > 1:
                    # Calculate coefficient of variation
                    cv = np.std(values) / np.mean(values) if np.mean(values) > 0 else 1
                    # Lower variation = higher confidence
                    confidence = max(0.1, 1.0 - cv)
                    confidence_scores.append(confidence)

        return np.mean(confidence_scores) if confidence_scores else 0.5

    def _generate_performance_insights(self, forecasts: Dict[str, Any]) -> List[str]:
        """Generate insights from performance forecasts."""
        insights = []

        for metric, forecast in forecasts.items():
            if 'trend' in forecast:
                if forecast['trend'] == 'increasing':
                    insights.append(f"{metric.replace('_', ' ').title()} is projected to increase")
                else:
                    insights.append(f"{metric.replace('_', ' ').title()} is projected to decrease")

        return insights

    def _generate_performance_recommendations(self, forecasts: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on performance forecasts."""
        recommendations = []

        for metric, forecast in forecasts.items():
            if 'trend' in forecast and forecast['trend'] == 'decreasing':
                if metric == 'active_users':
                    recommendations.append("Implement user engagement initiatives")
                elif metric == 'study_hours':
                    recommendations.append("Promote study habit formation programs")
                elif metric == 'completion_rate':
                    recommendations.append("Review and improve learning materials")

        return recommendations

    def _gather_license_usage_data(self, org_id: int) -> List[Dict[str, Any]]:
        """Gather license usage data for optimization."""
        try:
            licenses = self.db.query(UserLicense).filter(
                UserLicense.organization_id == org_id
            ).all()

            usage_data = []
            for license in licenses:
                # Get user activity
                user = self.db.query(EnterpriseUser).filter(
                    EnterpriseUser.user_id == license.user_id
                ).first()

                if user:
                    # Calculate usage metrics
                    days_since_last_use = (datetime.now() - license.last_used_date).days if license.last_used_date else 999

                    usage_data.append({
                        'license_id': license.id,
                        'user_id': license.user_id,
                        'license_type': license.license_type,
                        'status': license.status.value,
                        'days_since_last_use': days_since_last_use,
                        'user_role': user.role.value,
                        'department_id': user.department_id,
                        'usage_frequency': self._calculate_usage_frequency(license)
                    })

            return usage_data

        except Exception as e:
            logger.error(f"Error gathering license usage data: {e}")
            return []

    def _calculate_usage_frequency(self, license: UserLicense) -> float:
        """Calculate license usage frequency."""
        # Simplified calculation - in production would analyze detailed usage logs
        if license.last_used_date:
            days_since_assigned = (datetime.now() - license.assigned_date).days
            days_since_last_use = (datetime.now() - license.last_used_date).days

            if days_since_assigned > 0:
                return max(0.0, 1.0 - (days_since_last_use / days_since_assigned))

        return 0.0
