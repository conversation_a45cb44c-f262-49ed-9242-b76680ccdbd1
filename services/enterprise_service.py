"""Enterprise service for organizational management and analytics.

This service provides comprehensive functionality for managing enterprise
deployments including organizations, users, departments, licensing, and analytics.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
import pandas as pd
import numpy as np
from collections import defaultdict

from models.enterprise import (
    EnterpriseOrganization, Department, EnterpriseUser, UserLicense, 
    OrganizationAnalytics, OrganizationType, SubscriptionTier, UserRole, LicenseStatus
)
from models.progress_tracking import StudySession, PracticeTestResult, LearningGoal, Achievement
from models.certification import Certification

logger = logging.getLogger(__name__)


class EnterpriseService:
    """Service for enterprise organization management and analytics."""
    
    def __init__(self, db: Session):
        self.db = db
    
    # Organization Management
    
    def create_organization(self, org_data: Dict[str, Any]) -> EnterpriseOrganization:
        """Create a new enterprise organization."""
        try:
            # Generate slug from name if not provided
            if 'slug' not in org_data:
                org_data['slug'] = self._generate_slug(org_data['name'])
            
            organization = EnterpriseOrganization(**org_data)
            self.db.add(organization)
            self.db.commit()
            self.db.refresh(organization)
            
            logger.info(f"Created organization: {organization.name} (ID: {organization.id})")
            return organization
        
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating organization: {e}")
            raise
    
    def get_organization(self, org_id: int) -> Optional[EnterpriseOrganization]:
        """Get organization by ID."""
        return self.db.query(EnterpriseOrganization).filter(
            EnterpriseOrganization.id == org_id,
            EnterpriseOrganization.deleted_at.is_(None)
        ).first()
    
    def get_organization_by_slug(self, slug: str) -> Optional[EnterpriseOrganization]:
        """Get organization by slug."""
        return self.db.query(EnterpriseOrganization).filter(
            EnterpriseOrganization.slug == slug,
            EnterpriseOrganization.deleted_at.is_(None)
        ).first()
    
    def list_organizations(
        self, 
        page: int = 1, 
        page_size: int = 20,
        filters: Optional[Dict[str, Any]] = None
    ) -> Tuple[List[EnterpriseOrganization], int]:
        """List organizations with pagination and filtering."""
        query = self.db.query(EnterpriseOrganization).filter(
            EnterpriseOrganization.deleted_at.is_(None)
        )
        
        # Apply filters
        if filters:
            if 'organization_type' in filters:
                query = query.filter(EnterpriseOrganization.organization_type == filters['organization_type'])
            if 'subscription_tier' in filters:
                query = query.filter(EnterpriseOrganization.subscription_tier == filters['subscription_tier'])
            if 'is_active' in filters:
                query = query.filter(EnterpriseOrganization.is_active == filters['is_active'])
            if 'search' in filters:
                search_term = f"%{filters['search']}%"
                query = query.filter(
                    or_(
                        EnterpriseOrganization.name.ilike(search_term),
                        EnterpriseOrganization.domain.ilike(search_term)
                    )
                )
        
        total_count = query.count()
        
        # Apply pagination
        offset = (page - 1) * page_size
        organizations = query.order_by(EnterpriseOrganization.name).offset(offset).limit(page_size).all()
        
        return organizations, total_count
    
    def update_organization(self, org_id: int, update_data: Dict[str, Any]) -> Optional[EnterpriseOrganization]:
        """Update organization."""
        try:
            organization = self.get_organization(org_id)
            if not organization:
                return None
            
            for key, value in update_data.items():
                if hasattr(organization, key):
                    setattr(organization, key, value)
            
            organization.updated_at = datetime.utcnow()
            self.db.commit()
            self.db.refresh(organization)
            
            logger.info(f"Updated organization: {organization.name} (ID: {organization.id})")
            return organization
        
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error updating organization {org_id}: {e}")
            raise
    
    def delete_organization(self, org_id: int) -> bool:
        """Soft delete organization."""
        try:
            organization = self.get_organization(org_id)
            if not organization:
                return False
            
            organization.deleted_at = datetime.utcnow()
            organization.is_active = False
            self.db.commit()
            
            logger.info(f"Deleted organization: {organization.name} (ID: {organization.id})")
            return True
        
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error deleting organization {org_id}: {e}")
            raise
    
    # User Management
    
    def create_user(self, user_data: Dict[str, Any]) -> EnterpriseUser:
        """Create a new enterprise user."""
        try:
            user = EnterpriseUser(**user_data)
            self.db.add(user)
            
            # Update organization license usage
            if user.organization_id:
                org = self.get_organization(user.organization_id)
                if org:
                    org.licenses_used += 1
            
            self.db.commit()
            self.db.refresh(user)
            
            logger.info(f"Created user: {user.email} (ID: {user.user_id})")
            return user
        
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating user: {e}")
            raise
    
    def get_user(self, user_id: str) -> Optional[EnterpriseUser]:
        """Get user by ID."""
        return self.db.query(EnterpriseUser).filter(
            EnterpriseUser.user_id == user_id,
            EnterpriseUser.deleted_at.is_(None)
        ).first()
    
    def list_organization_users(
        self, 
        org_id: int,
        page: int = 1,
        page_size: int = 50,
        filters: Optional[Dict[str, Any]] = None
    ) -> Tuple[List[EnterpriseUser], int]:
        """List users in an organization."""
        query = self.db.query(EnterpriseUser).filter(
            EnterpriseUser.organization_id == org_id,
            EnterpriseUser.deleted_at.is_(None)
        )
        
        # Apply filters
        if filters:
            if 'role' in filters:
                query = query.filter(EnterpriseUser.role == filters['role'])
            if 'department_id' in filters:
                query = query.filter(EnterpriseUser.department_id == filters['department_id'])
            if 'is_active' in filters:
                query = query.filter(EnterpriseUser.is_active == filters['is_active'])
            if 'search' in filters:
                search_term = f"%{filters['search']}%"
                query = query.filter(
                    or_(
                        EnterpriseUser.email.ilike(search_term),
                        EnterpriseUser.first_name.ilike(search_term),
                        EnterpriseUser.last_name.ilike(search_term)
                    )
                )
        
        total_count = query.count()
        
        # Apply pagination
        offset = (page - 1) * page_size
        users = query.order_by(EnterpriseUser.email).offset(offset).limit(page_size).all()
        
        return users, total_count
    
    def update_user(self, user_id: str, update_data: Dict[str, Any]) -> Optional[EnterpriseUser]:
        """Update user."""
        try:
            user = self.get_user(user_id)
            if not user:
                return None
            
            for key, value in update_data.items():
                if hasattr(user, key):
                    setattr(user, key, value)
            
            user.updated_at = datetime.utcnow()
            self.db.commit()
            self.db.refresh(user)
            
            logger.info(f"Updated user: {user.email} (ID: {user.user_id})")
            return user
        
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error updating user {user_id}: {e}")
            raise
    
    # Department Management
    
    def create_department(self, dept_data: Dict[str, Any]) -> Department:
        """Create a new department."""
        try:
            department = Department(**dept_data)
            self.db.add(department)
            self.db.commit()
            self.db.refresh(department)
            
            logger.info(f"Created department: {department.name} (ID: {department.id})")
            return department
        
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating department: {e}")
            raise
    
    def list_organization_departments(self, org_id: int) -> List[Department]:
        """List departments in an organization."""
        return self.db.query(Department).filter(
            Department.organization_id == org_id,
            Department.deleted_at.is_(None)
        ).order_by(Department.name).all()
    
    # License Management
    
    def assign_license(self, user_id: str, license_type: str, expiration_date: Optional[datetime] = None) -> UserLicense:
        """Assign a license to a user."""
        try:
            user = self.get_user(user_id)
            if not user:
                raise ValueError(f"User {user_id} not found")
            
            # Check if user already has a license
            existing_license = self.db.query(UserLicense).filter(
                UserLicense.user_id == user_id
            ).first()
            
            if existing_license:
                # Update existing license
                existing_license.license_type = license_type
                existing_license.status = LicenseStatus.ACTIVE
                existing_license.expiration_date = expiration_date
                existing_license.updated_at = datetime.utcnow()
                license_obj = existing_license
            else:
                # Create new license
                license_obj = UserLicense(
                    organization_id=user.organization_id,
                    user_id=user_id,
                    license_type=license_type,
                    status=LicenseStatus.ACTIVE,
                    assigned_date=datetime.utcnow(),
                    expiration_date=expiration_date
                )
                self.db.add(license_obj)
            
            self.db.commit()
            self.db.refresh(license_obj)
            
            logger.info(f"Assigned {license_type} license to user {user_id}")
            return license_obj
        
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error assigning license to user {user_id}: {e}")
            raise
    
    def get_organization_license_usage(self, org_id: int) -> Dict[str, Any]:
        """Get license usage statistics for an organization."""
        organization = self.get_organization(org_id)
        if not organization:
            return {}
        
        # Get license statistics
        total_licenses = organization.license_count
        active_licenses = self.db.query(UserLicense).filter(
            UserLicense.organization_id == org_id,
            UserLicense.status == LicenseStatus.ACTIVE
        ).count()
        
        expired_licenses = self.db.query(UserLicense).filter(
            UserLicense.organization_id == org_id,
            UserLicense.status == LicenseStatus.EXPIRED
        ).count()
        
        # License usage by type
        license_breakdown = self.db.query(
            UserLicense.license_type,
            func.count(UserLicense.id).label('count')
        ).filter(
            UserLicense.organization_id == org_id,
            UserLicense.status == LicenseStatus.ACTIVE
        ).group_by(UserLicense.license_type).all()
        
        return {
            'total_licenses': total_licenses,
            'active_licenses': active_licenses,
            'expired_licenses': expired_licenses,
            'available_licenses': max(0, total_licenses - active_licenses),
            'utilization_rate': (active_licenses / total_licenses * 100) if total_licenses > 0 else 0,
            'license_breakdown': {item.license_type: item.count for item in license_breakdown}
        }
    
    # Analytics and Reporting
    
    def generate_organization_analytics(
        self, 
        org_id: int, 
        period_type: str = 'monthly',
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Generate comprehensive analytics for an organization."""
        try:
            organization = self.get_organization(org_id)
            if not organization:
                return {}
            
            # Set default date range
            if not end_date:
                end_date = datetime.utcnow()
            if not start_date:
                if period_type == 'daily':
                    start_date = end_date - timedelta(days=1)
                elif period_type == 'weekly':
                    start_date = end_date - timedelta(weeks=1)
                elif period_type == 'monthly':
                    start_date = end_date - timedelta(days=30)
                elif period_type == 'quarterly':
                    start_date = end_date - timedelta(days=90)
                else:  # yearly
                    start_date = end_date - timedelta(days=365)
            
            # User metrics
            user_metrics = self._calculate_user_metrics(org_id, start_date, end_date)
            
            # Learning metrics
            learning_metrics = self._calculate_learning_metrics(org_id, start_date, end_date)
            
            # Engagement metrics
            engagement_metrics = self._calculate_engagement_metrics(org_id, start_date, end_date)
            
            # Performance metrics
            performance_metrics = self._calculate_performance_metrics(org_id, start_date, end_date)
            
            # Department breakdown
            department_breakdown = self._calculate_department_breakdown(org_id, start_date, end_date)
            
            # Certification breakdown
            certification_breakdown = self._calculate_certification_breakdown(org_id, start_date, end_date)
            
            analytics = {
                'organization_id': org_id,
                'period_type': period_type,
                'period_start': start_date.isoformat(),
                'period_end': end_date.isoformat(),
                'user_metrics': user_metrics,
                'learning_metrics': learning_metrics,
                'engagement_metrics': engagement_metrics,
                'performance_metrics': performance_metrics,
                'department_breakdown': department_breakdown,
                'certification_breakdown': certification_breakdown,
                'generated_at': datetime.utcnow().isoformat()
            }
            
            # Store analytics in database
            self._store_analytics(org_id, period_type, start_date, end_date, analytics)
            
            return analytics
        
        except Exception as e:
            logger.error(f"Error generating analytics for organization {org_id}: {e}")
            raise
    
    def get_organization_dashboard_data(self, org_id: int) -> Dict[str, Any]:
        """Get comprehensive dashboard data for an organization."""
        try:
            organization = self.get_organization(org_id)
            if not organization:
                return {}
            
            # Basic organization info
            dashboard_data = {
                'organization': organization.to_dict(),
                'license_usage': self.get_organization_license_usage(org_id),
                'recent_analytics': self.generate_organization_analytics(org_id, 'monthly'),
                'department_summary': self._get_department_summary(org_id),
                'user_summary': self._get_user_summary(org_id),
                'recent_activity': self._get_recent_activity(org_id),
                'alerts': self._get_organization_alerts(org_id)
            }
            
            return dashboard_data
        
        except Exception as e:
            logger.error(f"Error getting dashboard data for organization {org_id}: {e}")
            raise
    
    # Helper Methods
    
    def _generate_slug(self, name: str) -> str:
        """Generate a URL-friendly slug from organization name."""
        import re
        slug = re.sub(r'[^\w\s-]', '', name.lower())
        slug = re.sub(r'[-\s]+', '-', slug)
        return slug.strip('-')
    
    def _calculate_user_metrics(self, org_id: int, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Calculate user-related metrics."""
        # Total users
        total_users = self.db.query(EnterpriseUser).filter(
            EnterpriseUser.organization_id == org_id,
            EnterpriseUser.deleted_at.is_(None)
        ).count()
        
        # Active users (logged in during period)
        active_users = self.db.query(EnterpriseUser).filter(
            EnterpriseUser.organization_id == org_id,
            EnterpriseUser.last_login.between(start_date, end_date),
            EnterpriseUser.deleted_at.is_(None)
        ).count()
        
        # New users (created during period)
        new_users = self.db.query(EnterpriseUser).filter(
            EnterpriseUser.organization_id == org_id,
            EnterpriseUser.created_at.between(start_date, end_date),
            EnterpriseUser.deleted_at.is_(None)
        ).count()
        
        return {
            'total_users': total_users,
            'active_users': active_users,
            'new_users': new_users,
            'user_retention_rate': (active_users / total_users * 100) if total_users > 0 else 0
        }

    def _calculate_learning_metrics(self, org_id: int, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Calculate learning-related metrics."""
        # Get organization users
        user_ids = [user.user_id for user in self.db.query(EnterpriseUser).filter(
            EnterpriseUser.organization_id == org_id,
            EnterpriseUser.deleted_at.is_(None)
        ).all()]

        if not user_ids:
            return {
                'total_study_hours': 0,
                'average_study_hours_per_user': 0,
                'total_certifications_completed': 0,
                'certification_completion_rate': 0
            }

        # Total study hours
        study_sessions = self.db.query(StudySession).filter(
            StudySession.user_id.in_(user_ids),
            StudySession.started_at.between(start_date, end_date)
        ).all()

        total_study_minutes = sum(session.duration_minutes or 0 for session in study_sessions)
        total_study_hours = total_study_minutes / 60

        # Certifications completed
        total_certifications = 0
        for user in self.db.query(EnterpriseUser).filter(
            EnterpriseUser.organization_id == org_id,
            EnterpriseUser.deleted_at.is_(None)
        ).all():
            if user.certifications_completed:
                total_certifications += len(user.certifications_completed)

        return {
            'total_study_hours': total_study_hours,
            'average_study_hours_per_user': total_study_hours / len(user_ids) if user_ids else 0,
            'total_certifications_completed': total_certifications,
            'certification_completion_rate': 0  # Would need more complex calculation
        }

    def _calculate_engagement_metrics(self, org_id: int, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Calculate engagement-related metrics."""
        # Get organization users
        user_ids = [user.user_id for user in self.db.query(EnterpriseUser).filter(
            EnterpriseUser.organization_id == org_id,
            EnterpriseUser.deleted_at.is_(None)
        ).all()]

        if not user_ids:
            return {
                'total_sessions': 0,
                'average_session_duration': 0,
                'feature_usage': {}
            }

        # Study sessions
        study_sessions = self.db.query(StudySession).filter(
            StudySession.user_id.in_(user_ids),
            StudySession.started_at.between(start_date, end_date)
        ).all()

        total_sessions = len(study_sessions)
        average_duration = np.mean([s.duration_minutes for s in study_sessions if s.duration_minutes]) if study_sessions else 0

        # Feature usage (simplified)
        feature_usage = {
            'study_sessions': total_sessions,
            'practice_tests': self.db.query(PracticeTestResult).filter(
                PracticeTestResult.user_id.in_(user_ids),
                PracticeTestResult.created_at.between(start_date, end_date)
            ).count(),
            'learning_goals': self.db.query(LearningGoal).filter(
                LearningGoal.user_id.in_(user_ids),
                LearningGoal.created_at.between(start_date, end_date)
            ).count()
        }

        return {
            'total_sessions': total_sessions,
            'average_session_duration': average_duration,
            'feature_usage': feature_usage
        }

    def _calculate_performance_metrics(self, org_id: int, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Calculate performance-related metrics."""
        # Get organization users
        user_ids = [user.user_id for user in self.db.query(EnterpriseUser).filter(
            EnterpriseUser.organization_id == org_id,
            EnterpriseUser.deleted_at.is_(None)
        ).all()]

        if not user_ids:
            return {
                'average_test_scores': 0,
                'goal_completion_rate': 0,
                'user_satisfaction_score': 0
            }

        # Average test scores
        test_results = self.db.query(PracticeTestResult).filter(
            PracticeTestResult.user_id.in_(user_ids),
            PracticeTestResult.created_at.between(start_date, end_date)
        ).all()

        average_test_score = np.mean([result.percentage for result in test_results]) if test_results else 0

        # Goal completion rate
        goals = self.db.query(LearningGoal).filter(
            LearningGoal.user_id.in_(user_ids)
        ).all()

        completed_goals = sum(1 for goal in goals if goal.status == 'completed')
        goal_completion_rate = (completed_goals / len(goals) * 100) if goals else 0

        return {
            'average_test_scores': average_test_score,
            'goal_completion_rate': goal_completion_rate,
            'user_satisfaction_score': 0  # Would need feedback data
        }

    def _calculate_department_breakdown(self, org_id: int, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Calculate metrics broken down by department."""
        departments = self.list_organization_departments(org_id)
        breakdown = {}

        for dept in departments:
            dept_users = self.db.query(EnterpriseUser).filter(
                EnterpriseUser.department_id == dept.id,
                EnterpriseUser.deleted_at.is_(None)
            ).all()

            user_ids = [user.user_id for user in dept_users]

            if user_ids:
                # Study hours for department
                study_sessions = self.db.query(StudySession).filter(
                    StudySession.user_id.in_(user_ids),
                    StudySession.started_at.between(start_date, end_date)
                ).all()

                total_minutes = sum(session.duration_minutes or 0 for session in study_sessions)

                breakdown[dept.name] = {
                    'user_count': len(dept_users),
                    'total_study_hours': total_minutes / 60,
                    'average_study_hours_per_user': (total_minutes / 60) / len(dept_users) if dept_users else 0
                }

        return breakdown

    def _calculate_certification_breakdown(self, org_id: int, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Calculate metrics broken down by certification."""
        # Get all certifications
        certifications = self.db.query(Certification).all()
        breakdown = {}

        # Get organization users
        user_ids = [user.user_id for user in self.db.query(EnterpriseUser).filter(
            EnterpriseUser.organization_id == org_id,
            EnterpriseUser.deleted_at.is_(None)
        ).all()]

        for cert in certifications:
            # Test results for this certification
            test_results = self.db.query(PracticeTestResult).filter(
                PracticeTestResult.certification_id == cert.id,
                PracticeTestResult.user_id.in_(user_ids),
                PracticeTestResult.created_at.between(start_date, end_date)
            ).all()

            if test_results:
                average_score = np.mean([result.percentage for result in test_results])
                pass_rate = sum(1 for result in test_results if result.passed) / len(test_results) * 100

                breakdown[cert.name] = {
                    'test_count': len(test_results),
                    'average_score': average_score,
                    'pass_rate': pass_rate
                }

        return breakdown

    def _store_analytics(self, org_id: int, period_type: str, start_date: datetime, end_date: datetime, analytics: Dict[str, Any]):
        """Store analytics data in the database."""
        try:
            # Check if analytics already exist for this period
            existing = self.db.query(OrganizationAnalytics).filter(
                OrganizationAnalytics.organization_id == org_id,
                OrganizationAnalytics.period_type == period_type,
                OrganizationAnalytics.period_start == start_date,
                OrganizationAnalytics.period_end == end_date
            ).first()

            if existing:
                # Update existing analytics
                for key, value in analytics.items():
                    if hasattr(existing, key):
                        setattr(existing, key, value)
                existing.updated_at = datetime.utcnow()
            else:
                # Create new analytics record
                analytics_record = OrganizationAnalytics(
                    organization_id=org_id,
                    period_type=period_type,
                    period_start=start_date,
                    period_end=end_date,
                    **analytics.get('user_metrics', {}),
                    **analytics.get('learning_metrics', {}),
                    **analytics.get('engagement_metrics', {}),
                    **analytics.get('performance_metrics', {}),
                    department_breakdown=analytics.get('department_breakdown', {}),
                    certification_breakdown=analytics.get('certification_breakdown', {}),
                    feature_usage=analytics.get('engagement_metrics', {}).get('feature_usage', {})
                )
                self.db.add(analytics_record)

            self.db.commit()

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error storing analytics: {e}")

    def _get_department_summary(self, org_id: int) -> Dict[str, Any]:
        """Get department summary for dashboard."""
        departments = self.list_organization_departments(org_id)

        total_departments = len(departments)
        active_departments = sum(1 for dept in departments if dept.is_active)

        # Department with most users
        dept_user_counts = []
        for dept in departments:
            user_count = self.db.query(EnterpriseUser).filter(
                EnterpriseUser.department_id == dept.id,
                EnterpriseUser.deleted_at.is_(None)
            ).count()
            dept_user_counts.append((dept.name, user_count))

        largest_dept = max(dept_user_counts, key=lambda x: x[1]) if dept_user_counts else ("None", 0)

        return {
            'total_departments': total_departments,
            'active_departments': active_departments,
            'largest_department': largest_dept[0],
            'largest_department_users': largest_dept[1]
        }

    def _get_user_summary(self, org_id: int) -> Dict[str, Any]:
        """Get user summary for dashboard."""
        users = self.db.query(EnterpriseUser).filter(
            EnterpriseUser.organization_id == org_id,
            EnterpriseUser.deleted_at.is_(None)
        ).all()

        total_users = len(users)
        active_users = sum(1 for user in users if user.is_active)

        # Role distribution
        role_counts = defaultdict(int)
        for user in users:
            role_counts[user.role.value if user.role else 'unknown'] += 1

        # Recent logins (last 7 days)
        recent_login_cutoff = datetime.utcnow() - timedelta(days=7)
        recent_logins = sum(1 for user in users if user.last_login and user.last_login > recent_login_cutoff)

        return {
            'total_users': total_users,
            'active_users': active_users,
            'recent_logins': recent_logins,
            'role_distribution': dict(role_counts)
        }

    def _get_recent_activity(self, org_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent activity for dashboard."""
        # Get organization users
        user_ids = [user.user_id for user in self.db.query(EnterpriseUser).filter(
            EnterpriseUser.organization_id == org_id,
            EnterpriseUser.deleted_at.is_(None)
        ).all()]

        activities = []

        # Recent study sessions
        recent_sessions = self.db.query(StudySession).filter(
            StudySession.user_id.in_(user_ids)
        ).order_by(StudySession.started_at.desc()).limit(limit).all()

        for session in recent_sessions:
            activities.append({
                'type': 'study_session',
                'user_id': session.user_id,
                'description': f"Completed {session.session_type} session",
                'timestamp': session.started_at.isoformat() if session.started_at else None
            })

        # Recent test results
        recent_tests = self.db.query(PracticeTestResult).filter(
            PracticeTestResult.user_id.in_(user_ids)
        ).order_by(PracticeTestResult.created_at.desc()).limit(limit).all()

        for test in recent_tests:
            activities.append({
                'type': 'practice_test',
                'user_id': test.user_id,
                'description': f"Scored {test.percentage:.1f}% on practice test",
                'timestamp': test.created_at.isoformat() if test.created_at else None
            })

        # Sort by timestamp and return most recent
        activities.sort(key=lambda x: x['timestamp'] or '', reverse=True)
        return activities[:limit]

    def _get_organization_alerts(self, org_id: int) -> List[Dict[str, Any]]:
        """Get alerts and notifications for organization."""
        alerts = []
        organization = self.get_organization(org_id)

        if not organization:
            return alerts

        # License usage alerts
        license_usage = self.get_organization_license_usage(org_id)
        utilization = license_usage.get('utilization_rate', 0)

        if utilization > 90:
            alerts.append({
                'type': 'warning',
                'title': 'High License Usage',
                'message': f'License utilization is at {utilization:.1f}%. Consider upgrading your plan.',
                'priority': 'high'
            })
        elif utilization > 75:
            alerts.append({
                'type': 'info',
                'title': 'License Usage Notice',
                'message': f'License utilization is at {utilization:.1f}%.',
                'priority': 'medium'
            })

        # Subscription expiration alerts
        if organization.subscription_end_date:
            days_until_expiry = (organization.subscription_end_date - datetime.utcnow()).days
            if days_until_expiry <= 30:
                alerts.append({
                    'type': 'warning',
                    'title': 'Subscription Expiring Soon',
                    'message': f'Your subscription expires in {days_until_expiry} days.',
                    'priority': 'high'
                })

        # Trial expiration alerts
        if organization.is_trial and organization.trial_end_date:
            days_until_trial_end = (organization.trial_end_date - datetime.utcnow()).days
            if days_until_trial_end <= 7:
                alerts.append({
                    'type': 'warning',
                    'title': 'Trial Ending Soon',
                    'message': f'Your trial ends in {days_until_trial_end} days.',
                    'priority': 'high'
                })

        return alerts
