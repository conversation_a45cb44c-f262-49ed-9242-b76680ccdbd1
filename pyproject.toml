[project]
name = "repl-nix-workspace"
version = "0.1.0"
description = "Add your description here"
requires-python = ">=3.11"
dependencies = [
    # Web Framework & API
    "fastapi>=0.115.8",
    "uvicorn>=0.34.0",
    "python-multipart>=0.0.20",

    # Database
    "sqlalchemy>=2.0.38",
    "psycopg2-binary>=2.9.10",
    "alembic>=1.14.1",

    # Data Validation & Serialization
    "pydantic>=2.10.6",

    # Authentication & Security
    "python-jose[cryptography]>=3.4.0",
    "passlib[bcrypt]>=1.7.4",
    "authlib>=1.4.1",

    # HTTP Requests
    "requests>=2.32.3",

    # AI & ML
    "anthropic>=0.46.0",
    "openai>=1.63.2",
    "scikit-learn>=1.6.1",
    "numpy>=2.2.3",

    # Data Processing
    "beautifulsoup4>=4.13.3",
    "trafilatura>=2.0.0",
    "python-magic>=0.4.27",

    # Visualization & Reports
    "plotly>=6.0.0",
    "reportlab>=4.3.1",
    "pypdf2>=3.0.1",

    # Utilities
    "python-dotenv>=1.0.1",
    "twilio>=9.4.6",

    # Testing
    "pytest>=8.3.4",
    "pytest-cov>=6.0.0",
    "pytest-mock>=3.14.0",
]
