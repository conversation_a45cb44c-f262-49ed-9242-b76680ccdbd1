"""sync_database_schema

Revision ID: 930c9296b38e
Revises: add_tutorial_completed_001
Create Date: 2025-02-27 11:11:23.578742
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '930c9296b38e'
down_revision = 'add_tutorial_completed_001'
branch_labels = None
depends_on = None

def upgrade():
    # Check if learning_paths table exists before modifying it
    connection = op.get_bind()
    inspector = sa.inspect(connection)
    tables = inspector.get_table_names()

    if 'learning_paths' in tables:
        # First make user_experience_id nullable
        op.alter_column('learning_paths', 'user_experience_id',
            existing_type=sa.INTEGER(),
            nullable=True)

        # Then handle orphaned learning paths
        op.execute("""
            UPDATE learning_paths
            SET user_experience_id = NULL
            WHERE user_experience_id NOT IN (
                SELECT id FROM user_experiences
            )
        """)

        # Add foreign key for learning paths with on delete set null
        op.create_foreign_key(
            'fk_learning_paths_user_experience',
            'learning_paths',
            'user_experiences',
            ['user_experience_id'],
            ['id'],
            ondelete='SET NULL'
        )

    # Make tutorial_completed nullable
    op.alter_column('user_experiences', 'tutorial_completed',
        existing_type=sa.BOOLEAN(),
        nullable=True,
        existing_server_default=sa.text('false'))

    # Ensure 'is_deleted' columns are not nullable
    op.alter_column('organizations', 'is_deleted',
        existing_type=sa.BOOLEAN(),
        nullable=False,
        existing_server_default=sa.text('false'))

    op.alter_column('certifications', 'is_deleted',
        existing_type=sa.BOOLEAN(),
        nullable=False,
        existing_server_default=sa.text('false'))

    op.alter_column('certification_versions', 'is_deleted',
        existing_type=sa.BOOLEAN(),
        nullable=False,
        existing_server_default=sa.text('false'))

def downgrade():
    # Check if learning_paths table exists before modifying it
    connection = op.get_bind()
    inspector = sa.inspect(connection)
    tables = inspector.get_table_names()

    if 'learning_paths' in tables:
        # Remove foreign key from learning paths
        op.drop_constraint('fk_learning_paths_user_experience', 'learning_paths', type_='foreignkey')

    # Revert column modifications
    op.alter_column('certification_versions', 'is_deleted',
        existing_type=sa.BOOLEAN(),
        nullable=True,
        existing_server_default=sa.text('false'))

    op.alter_column('certifications', 'is_deleted',
        existing_type=sa.BOOLEAN(),
        nullable=True,
        existing_server_default=sa.text('false'))

    op.alter_column('organizations', 'is_deleted',
        existing_type=sa.BOOLEAN(),
        nullable=True,
        existing_server_default=sa.text('false'))

    op.alter_column('user_experiences', 'tutorial_completed',
        existing_type=sa.BOOLEAN(),
        nullable=False,
        existing_server_default=sa.text('false'))

        # Make user_experience_id not nullable again
        op.alter_column('learning_paths', 'user_experience_id',
            existing_type=sa.INTEGER(),
            nullable=False)