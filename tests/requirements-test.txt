# Testing Requirements for CertRatsAgent4
# Install with: pip install -r tests/requirements-test.txt

# Core Testing Framework
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
pytest-xdist>=3.3.0  # For parallel test execution

# FastAPI Testing
fastapi>=0.100.0
httpx>=0.24.0  # For async HTTP testing
starlette>=0.27.0

# Database Testing
sqlalchemy>=2.0.0
alembic>=1.11.0

# Mock and Fixtures
factory-boy>=3.3.0
faker>=19.0.0
responses>=0.23.0  # For mocking HTTP requests

# Coverage and Reporting
coverage>=7.2.0
pytest-html>=3.2.0  # HTML test reports
pytest-json-report>=1.5.0  # JSON test reports

# Performance Testing
pytest-benchmark>=4.0.0
pytest-timeout>=2.1.0

# Code Quality
flake8>=6.0.0
mypy>=1.4.0
black>=23.0.0
isort>=5.12.0

# Additional Testing Utilities
freezegun>=1.2.0  # For mocking datetime
parameterized>=0.9.0  # For parameterized tests
testcontainers>=3.7.0  # For integration testing with containers

# Development Dependencies
ipython>=8.14.0  # For debugging
pdbpp>=0.10.3  # Enhanced debugger
