"""Test configuration and fixtures"""
import pytest
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
import sys
import os
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Add project root to Python path
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

# Import Base and ensure all models are imported
from database import Base
import models  # This registers all models with Base.metadata

# Get database URL from environment
DATABASE_URL = os.environ.get('DATABASE_URL')
if not DATABASE_URL:
    raise ValueError("DATABASE_URL environment variable not set")

# Create test engine with pooling config
test_engine = create_engine(
    DATABASE_URL,
    pool_pre_ping=True,  # Enable connection health checks
    pool_size=10,        # Increase pool size for concurrent test connections
    max_overflow=20,     # Allow more overflow connections
    pool_timeout=30,     # Increase timeout for connection acquisition
    echo=True           # Log SQL queries for debugging
)

# Create session factory bound to test engine
TestingSessionLocal = sessionmaker(
    bind=test_engine,
    autocommit=False,
    autoflush=False,
    expire_on_commit=False  # Important for test scenarios
)

@pytest.fixture(scope="session", autouse=True)
def setup_test_database():
    """Create test database tables once for all tests"""
    try:
        logger.info("Creating test database tables")
        Base.metadata.create_all(bind=test_engine)
        logger.info("Created database tables successfully")
        yield
    finally:
        logger.info("Test session complete")

@pytest.fixture(autouse=True)
def cleanup_tables():
    """Clean up tables after each test"""
    yield  # Run the test
    logger.info("Cleaning up tables after test")
    session = TestingSessionLocal()
    try:
        # Get all tables in reverse order to handle dependencies
        tables = reversed(Base.metadata.sorted_tables)

        # Delete all data from each table
        for table in tables:
            logger.debug(f"Cleaning table: {table.name}")
            session.execute(text(f"DELETE FROM {table.name}"))

        session.commit()
        logger.info("Successfully cleaned up tables")
    except Exception as e:
        logger.error(f"Error cleaning up tables: {e}")
        session.rollback()
        raise
    finally:
        session.close()

@pytest.fixture
def db_session():
    """Provide test database session"""
    session = TestingSessionLocal()
    try:
        logger.info("Created new test database session")
        yield session
    finally:
        logger.info("Closing test database session")
        session.close()