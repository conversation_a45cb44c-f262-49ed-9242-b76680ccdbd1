"""Tests for model validation across all models"""
import pytest
from datetime import datetime
from sqlalchemy.exc import IntegrityError
from models.user_experience import UserExperience
from models.learning_path import LearningPath
from models.certification import Certification, Organization
from models.job import SecurityJob
from models.security_domain import SecurityDomain

def test_user_experience_validation(db_session):
    """Test UserExperience model validation"""
    # Test required fields - application-level validation
    with pytest.raises(ValueError, match="years_experience is required"):
        empty_user_exp = UserExperience()
        db_session.add(empty_user_exp)
        db_session.commit()
    db_session.rollback()

    # Test valid data
    current_time = datetime.utcnow()
    valid_user_exp = UserExperience(
        user_id="test_user",
        years_experience=5,
        user_role="Security Analyst",
        desired_role="Security Engineer",
        expertise_areas=["Network Security", "Cloud Security"],
        preferred_learning_style="Visual",
        study_time_available=10,
        created_at=current_time,
        updated_at=current_time
    )
    db_session.add(valid_user_exp)
    db_session.commit()
    assert valid_user_exp.id is not None

    # Test database constraint for negative years_experience
    # Create a valid object first, then modify to bypass application validation
    constraint_test = UserExperience(
        user_id="test_user_constraint",
        years_experience=5,  # Start with valid value
        user_role="Security Analyst",
        desired_role="Security Engineer",
        expertise_areas=["Network Security"],
        preferred_learning_style="Visual",
        study_time_available=10
    )
    # Bypass application validation by directly setting the attribute
    constraint_test.years_experience = -1  # This should fail the database constraint
    db_session.add(constraint_test)
    with pytest.raises(IntegrityError):
        db_session.commit()
    db_session.rollback()

def test_certification_validation(db_session):
    """Test Certification model validation"""
    # Create test organization first
    org = Organization(
        name="Test Org",
        country="United States",
        description="Test organization",
        created_at=datetime.utcnow()
    )
    db_session.add(org)
    db_session.commit()

    # Test required fields
    empty_cert = Certification()
    db_session.add(empty_cert)
    with pytest.raises(IntegrityError):
        db_session.commit()
    db_session.rollback()

    # Test valid certification
    valid_cert = Certification(
        name="Test Cert",
        category="Security",
        domain="Network Security",
        level="Entry Level",
        difficulty=1,
        cost=500,
        description="Test certification",
        validity_period=12,
        exam_code="TEST-001",
        organization_id=org.id,
        focus="Network",
        custom_hours=40
    )
    db_session.add(valid_cert)
    db_session.commit()
    assert valid_cert.id is not None

    # Test invalid difficulty level
    invalid_difficulty = Certification(
        name="Invalid Cert",
        category="Security",
        domain="Network Security",
        level="Entry Level",
        difficulty=6,  # Invalid: should be 1-5
        organization_id=org.id
    )
    db_session.add(invalid_difficulty)
    with pytest.raises(IntegrityError):
        db_session.commit()
    db_session.rollback()

def test_security_domain_validation(db_session):
    """Test SecurityDomain model validation"""
    # Test required fields
    empty_domain = SecurityDomain()
    db_session.add(empty_domain)
    with pytest.raises(IntegrityError):
        db_session.commit()
    db_session.rollback()

    # Test valid domain
    valid_domain = SecurityDomain(
        name="Application Security",
        description="Application security testing and practices",
        created_at=datetime.utcnow()
    )
    db_session.add(valid_domain)
    db_session.commit()
    assert valid_domain.id is not None

    # Test duplicate domain name
    duplicate_domain = SecurityDomain(
        name="Application Security",  # Duplicate name
        description="Different description"
    )
    db_session.add(duplicate_domain)
    with pytest.raises(IntegrityError):
        db_session.commit()
    db_session.rollback()

def test_learning_path_validation(db_session):
    """Test LearningPath model validation"""
    # Create test user experience first
    user_exp = UserExperience(
        user_id="test_user",
        years_experience=5,
        user_role="Security Analyst",
        desired_role="Security Engineer",
        expertise_areas=["Network Security"],
        preferred_learning_style="Visual",
        study_time_available=10,
        created_at=datetime.utcnow()
    )
    db_session.add(user_exp)
    db_session.commit()

    # Test required fields
    empty_path = LearningPath()
    db_session.add(empty_path)
    with pytest.raises(IntegrityError):
        db_session.commit()
    db_session.rollback()

    # Test valid learning path
    valid_path = LearningPath(
        user_experience_id=user_exp.id,
        name="Network Security Path",
        description="Path to become a network security expert",
        estimated_duration_weeks=12,
        difficulty_level="Intermediate",
        status="active",
        created_at=datetime.utcnow()
    )
    db_session.add(valid_path)
    db_session.commit()
    assert valid_path.id is not None

    # Test invalid duration
    invalid_duration = LearningPath(
        user_experience_id=user_exp.id,
        name="Invalid Path",
        description="Test path",
        estimated_duration_weeks=-1,  # Invalid negative weeks
        difficulty_level="Beginner",
        status="active"
    )
    db_session.add(invalid_duration)
    with pytest.raises(IntegrityError):
        db_session.commit()
    db_session.rollback()
