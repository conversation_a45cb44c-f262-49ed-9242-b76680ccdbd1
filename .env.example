# CertPathFinder Environment Configuration Template
# =================================================
# Copy this file to .env and update with your actual values

# =============================================================================
# CORE APPLICATION SETTINGS
# =============================================================================

# API Configuration
API_V1_PREFIX=/api/v1
PROJECT_NAME=CertPathFinder API
VERSION=1.0.0
DESCRIPTION=The Ultimate Cybersecurity Certification Journey Planner API

# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=False
ENVIRONMENT=development

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:8000,http://localhost:8080
CORS_ALLOW_CREDENTIALS=True
CORS_ALLOW_METHODS=*
CORS_ALLOW_HEADERS=*

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Primary Database (PostgreSQL recommended for production)
DATABASE_URL=postgresql://username:password@localhost:5432/certpathfinder

# Alternative: SQLite for development
# DATABASE_URL=sqlite:///./certpathfinder.db

# Database Pool Settings
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

REDIS_URL=redis://localhost:6379
REDIS_TTL=3600

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Generate a secure secret key (use: python -c "import secrets; print(secrets.token_urlsafe(32))")
SECRET_KEY=your-super-secure-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=60
REFRESH_TOKEN_EXPIRE_DAYS=7

# =============================================================================
# AI CONFIGURATION
# =============================================================================

# Anthropic API for Claude integration
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# OpenAI API for AI features
OPENAI_API_KEY=your-openai-api-key-here

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================

# Twilio Configuration
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================

# Maximum file size in bytes (10MB default)
MAX_FILE_SIZE=********

# Allowed file types (comma-separated)
ALLOWED_FILE_TYPES=pdf,doc,docx,txt

# =============================================================================
# RATE LIMITING
# =============================================================================

RATE_LIMIT_PER_MINUTE=60

# Email Configuration (Optional)
# ------------------------------
SMTP_TLS=True
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Email settings
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=CertPathFinder

# File Storage Configuration
# -------------------------
# AWS S3 Configuration (Optional)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
S3_BUCKET_NAME=certpathfinder-storage

# Local file storage path
UPLOAD_PATH=./uploads

# Logging Configuration
# --------------------
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# Rate Limiting
# ------------
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Cache Configuration
# ------------------
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# Monitoring and Analytics
# -----------------------
# Sentry DSN for error tracking (Optional)
SENTRY_DSN=your-sentry-dsn-here

# Google Analytics ID (Optional)
GOOGLE_ANALYTICS_ID=your-ga-id-here

# Feature Flags
# ------------
ENABLE_AI_FEATURES=True
ENABLE_ENTERPRISE_FEATURES=True
ENABLE_MOBILE_API=True
ENABLE_INTEGRATION_HUB=True

# Enterprise Configuration
# ------------------------
ENTERPRISE_LICENSE_KEY=your-enterprise-license-key
MAX_ORGANIZATIONS=100
MAX_USERS_PER_ORG=1000

# Integration Configuration
# -------------------------
# LDAP Configuration
LDAP_SERVER=ldap://your-ldap-server.com
LDAP_PORT=389
LDAP_USE_SSL=False
LDAP_BIND_DN=cn=admin,dc=example,dc=com
LDAP_BIND_PASSWORD=your-ldap-password
LDAP_SEARCH_BASE=dc=example,dc=com

# SSO Configuration
SSO_PROVIDER=saml
SSO_ENTITY_ID=certpathfinder
SSO_SSO_URL=https://your-sso-provider.com/sso
SSO_X509_CERT=your-x509-certificate

# LMS Integration
LMS_API_URL=https://your-lms.com/api
LMS_API_KEY=your-lms-api-key
LMS_SYNC_INTERVAL=3600

# HR System Integration
HR_API_URL=https://your-hr-system.com/api
HR_API_KEY=your-hr-api-key
HR_SYNC_INTERVAL=86400

# Development Configuration
# -------------------------
# Set to True for development
DEVELOPMENT_MODE=False

# Enable debug toolbar
DEBUG_TOOLBAR=False

# Reload on file changes
AUTO_RELOAD=False

# Testing Configuration
# --------------------
# Test database URL
TEST_DATABASE_URL=sqlite:///./test_certpathfinder.db

# Test Redis URL
TEST_REDIS_URL=redis://localhost:6379/1

# Disable external API calls in tests
TESTING_MODE=False

# Performance Configuration
# -------------------------
# Database connection pool
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10
DB_POOL_TIMEOUT=30

# Worker processes
WORKER_PROCESSES=4
WORKER_CONNECTIONS=1000

# Security Headers
# ---------------
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_FRAME_DENY=True
SECURE_CONTENT_TYPE_NOSNIFF=True
SECURE_BROWSER_XSS_FILTER=True

# Session Configuration
# --------------------
SESSION_COOKIE_SECURE=True
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax
SESSION_COOKIE_AGE=86400

# Backup Configuration
# -------------------
BACKUP_ENABLED=False
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=certpathfinder-backups

# Notification Configuration
# --------------------------
SLACK_WEBHOOK_URL=your-slack-webhook-url
DISCORD_WEBHOOK_URL=your-discord-webhook-url
TEAMS_WEBHOOK_URL=your-teams-webhook-url

# Mobile App Configuration
# ------------------------
MOBILE_API_VERSION=v1
MOBILE_PUSH_NOTIFICATIONS=True
FIREBASE_SERVER_KEY=your-firebase-server-key
APNS_CERTIFICATE_PATH=./certs/apns.pem

# Analytics Configuration
# ----------------------
ANALYTICS_ENABLED=True
ANALYTICS_RETENTION_DAYS=365
ANALYTICS_SAMPLING_RATE=1.0

# Compliance Configuration
# -----------------------
GDPR_COMPLIANCE=True
CCPA_COMPLIANCE=True
HIPAA_COMPLIANCE=False
SOC2_COMPLIANCE=True

# Maintenance Mode
# ---------------
MAINTENANCE_MODE=False
MAINTENANCE_MESSAGE=CertPathFinder is currently under maintenance. Please try again later.
