version: '3.8'

# Production-optimized Docker Compose configuration
services:
  # PostgreSQL Database with optimizations
  postgres:
    image: postgres:15-alpine
    container_name: certpathfinder_postgres_prod
    restart: unless-stopped
    environment:
      POSTGRES_DB: certpathfinder
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secure_password_change_me}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
      # Performance tuning
      POSTGRES_SHARED_BUFFERS: 256MB
      POSTGRES_EFFECTIVE_CACHE_SIZE: 1GB
      POSTGRES_MAINTENANCE_WORK_MEM: 64MB
      POSTGRES_CHECKPOINT_COMPLETION_TARGET: 0.9
      POSTGRES_WAL_BUFFERS: 16MB
      POSTGRES_DEFAULT_STATISTICS_TARGET: 100
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/postgres-init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - internal
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Redis with persistence and optimization
  redis:
    image: redis:7-alpine
    container_name: certpathfinder_redis_prod
    restart: unless-stopped
    command: >
      redis-server
      --appendonly yes
      --appendfsync everysec
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
      --tcp-keepalive 60
      --timeout 300
    volumes:
      - redis_data:/data
    networks:
      - internal
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'
        reservations:
          memory: 256M
          cpus: '0.1'

  # FastAPI Backend with production optimizations
  api:
    build:
      context: .
      dockerfile: Dockerfile.api.prod
      args:
        - ENVIRONMENT=production
    container_name: certpathfinder_api_prod
    restart: unless-stopped
    environment:
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-secure_password_change_me}@postgres:5432/certpathfinder
      - REDIS_URL=redis://redis:6379
      - ENVIRONMENT=production
      - DEBUG=false
      - SECRET_KEY=${SECRET_KEY:-change_this_secret_key_in_production}
      - CORS_ORIGINS=${CORS_ORIGINS:-https://yourdomain.com}
      - WORKERS=4
      - MAX_WORKERS=8
      - WORKER_CLASS=uvicorn.workers.UvicornWorker
      - LOG_LEVEL=info
      - ACCESS_LOG=true
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - internal
      - web
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  # React Frontend with Nginx
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - NODE_ENV=production
        - REACT_APP_API_BASE_URL=${REACT_APP_API_BASE_URL:-https://api.yourdomain.com}
        - REACT_APP_ENVIRONMENT=production
        - GENERATE_SOURCEMAP=false
    container_name: certpathfinder_frontend_prod
    restart: unless-stopped
    depends_on:
      - api
    networks:
      - web
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'

  # Celery Worker for background tasks
  worker:
    build:
      context: .
      dockerfile: Dockerfile.api.prod
    container_name: certpathfinder_worker_prod
    restart: unless-stopped
    environment:
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-secure_password_change_me}@postgres:5432/certpathfinder
      - REDIS_URL=redis://redis:6379
      - ENVIRONMENT=production
      - DEBUG=false
      - SECRET_KEY=${SECRET_KEY:-change_this_secret_key_in_production}
      - CELERY_BROKER_URL=redis://redis:6379
      - CELERY_RESULT_BACKEND=redis://redis:6379
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - internal
    command: celery -A api.celery_app worker --loglevel=info --concurrency=2
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Celery Beat for scheduled tasks
  scheduler:
    build:
      context: .
      dockerfile: Dockerfile.api.prod
    container_name: certpathfinder_scheduler_prod
    restart: unless-stopped
    environment:
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-secure_password_change_me}@postgres:5432/certpathfinder
      - REDIS_URL=redis://redis:6379
      - ENVIRONMENT=production
      - DEBUG=false
      - SECRET_KEY=${SECRET_KEY:-change_this_secret_key_in_production}
      - CELERY_BROKER_URL=redis://redis:6379
      - CELERY_RESULT_BACKEND=redis://redis:6379
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - internal
    command: celery -A api.celery_app beat --loglevel=info
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'

  # Nginx reverse proxy (optional - for production load balancing)
  nginx:
    image: nginx:alpine
    container_name: certpathfinder_nginx_prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_cache:/var/cache/nginx
    depends_on:
      - frontend
      - api
    networks:
      - web
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 5s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  nginx_cache:
    driver: local

networks:
  internal:
    driver: bridge
    internal: true
  web:
    driver: bridge

# Production deployment configuration
x-logging: &default-logging
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"

# Apply logging to all services
x-deploy-defaults: &deploy-defaults
  logging: *default-logging
  restart_policy:
    condition: on-failure
    delay: 5s
    max_attempts: 3
    window: 120s
