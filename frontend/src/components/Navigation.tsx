import React, { useState } from 'react';
import {
  ChevronDown,
  ChevronUp,
  Home,
  Shield,
  <PERSON><PERSON><PERSON>,
  Clock,
  DollarSign,
  BookOpen,
  Award,
  Users,
  FileText,
  Settings,
  Search,
  Timer,
  Briefcase,
  Building,
  Menu,
  X
} from 'lucide-react';
import { Link, useLocation } from 'react-router-dom';

interface NavigationItem {
  id: string;
  label: string;
  path: string;
}

interface NavigationCategory {
  id: string;
  title: string;
  icon: React.ReactNode;
  items: NavigationItem[];
}

const Navigation: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState<string>('main');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState<boolean>(false);
  const location = useLocation();
  
  const categories: NavigationCategory[] = [
    {
      id: 'main',
      title: 'Main',
      icon: <Home size={20} />,
      items: [
        { id: 'dashboard', label: 'Dashboard', path: '/' },
        { id: 'certifications', label: 'Certification Explorer', path: '/certifications' }
      ]
    },
    {
      id: 'tools',
      title: 'Tools',
      icon: <Settings size={20} />,
      items: [
        { id: 'study-timer', label: 'Study Timer', path: '/study-timer' },
        { id: 'cost-calculator', label: 'Cost Calculator', path: '/cost-calculator' },
        { id: 'career-path', label: 'Career Path Generator', path: '/career-path' }
      ]
    },
    {
      id: 'resources',
      title: 'Resources',
      icon: <BookOpen size={20} />,
      items: [
        { id: 'job-search', label: 'Job Search', path: '/job-search' },
        { id: 'user-journeys', label: 'User Journeys', path: '/user-journeys' },
        { id: 'faq', label: 'FAQ', path: '/faq' }
      ]
    },
    {
      id: 'enterprise',
      title: 'Enterprise',
      icon: <Building size={20} />,
      items: [
        { id: 'enterprise-dashboard', label: 'Enterprise Dashboard', path: '/enterprise' },
        { id: 'team-management', label: 'Team Management', path: '/enterprise/teams' },
        { id: 'analytics', label: 'Analytics', path: '/enterprise/analytics' }
      ]
    },
    {
      id: 'profile',
      title: 'Profile',
      icon: <Users size={20} />,
      items: [
        { id: 'profile', label: 'My Profile', path: '/profile' },
        { id: 'progress', label: 'My Progress', path: '/progress' },
        { id: 'achievements', label: 'Achievements', path: '/achievements' }
      ]
    }
  ];

  const isActiveItem = (path: string) => {
    return location.pathname === path;
  };

  const toggleCategory = (categoryId: string) => {
    setActiveCategory(activeCategory === categoryId ? '' : categoryId);
  };

  return (
    <>
      {/* Mobile Menu Button */}
      <button
        className="lg:hidden fixed top-4 left-4 z-50 p-2 bg-gray-900 text-white rounded-md"
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
      >
        {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
      </button>

      {/* Mobile Overlay */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Navigation Sidebar */}
      <div className={`
        fixed lg:static inset-y-0 left-0 z-40
        w-64 h-screen bg-gray-900 text-white flex flex-col
        transform transition-transform duration-300 ease-in-out
        ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        {/* App Title */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <div className="flex items-center">
            <Shield className="text-blue-400 mr-2" size={24} />
            <h1 className="text-xl font-bold">CertPathFinder</h1>
          </div>
          {/* Mobile close button */}
          <button
            className="lg:hidden p-1"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            <X size={20} />
          </button>
        </div>
      
      {/* User Profile */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
              <span className="font-bold text-sm">JD</span>
            </div>
            <div className="ml-2">
              <div className="font-medium text-sm">John Doe</div>
              <div className="text-xs text-gray-400">Security Analyst</div>
            </div>
          </div>
          <Link to="/profile">
            <Settings size={16} className="text-gray-400 cursor-pointer hover:text-white transition-colors" />
          </Link>
        </div>
      </div>
      
      {/* Search Box */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center bg-gray-800 rounded-md px-3 py-2">
          <Search size={16} className="text-gray-400 mr-2" />
          <input 
            className="bg-transparent border-none focus:outline-none text-sm w-full placeholder-gray-500"
            placeholder="Search certifications..." 
          />
        </div>
      </div>
      
      {/* Navigation Categories */}
      <div className="flex-1 overflow-y-auto py-2">
        {categories.map(category => (
          <div key={category.id} className="mb-2">
            <button
              onClick={() => toggleCategory(category.id)}
              className="flex items-center justify-between w-full px-4 py-2 text-left hover:bg-gray-800 transition-colors"
            >
              <div className="flex items-center">
                {category.icon}
                <span className="ml-2 font-medium">{category.title}</span>
              </div>
              {activeCategory === category.id ? 
                <ChevronUp size={16} /> : 
                <ChevronDown size={16} />
              }
            </button>
            
            {activeCategory === category.id && (
              <div className="mt-1">
                {category.items.map(item => (
                  <Link
                    key={item.id}
                    to={item.path}
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`w-full pl-10 pr-4 py-3 text-left text-sm flex items-center transition-colors touch-manipulation ${
                      isActiveItem(item.path)
                        ? 'bg-blue-600 text-white'
                        : 'hover:bg-gray-800 text-gray-300'
                    }`}
                  >
                    <span>{item.label}</span>
                  </Link>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
      
      {/* Language Selector */}
      <div className="p-4 border-t border-gray-700">
        <select className="w-full bg-gray-800 rounded p-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
          <option value="en">🇬🇧 English</option>
          <option value="de">🇩🇪 German</option>
          <option value="es">🇪🇸 Spanish</option>
          <option value="fr">🇫🇷 French</option>
          <option value="af">🇿🇦 Afrikaans</option>
          <option value="zu">🇿🇦 isiZulu</option>
          <option value="ro">🇷🇴 Romanian</option>
        </select>
      </div>
    </div>
  );
};

export default Navigation;
