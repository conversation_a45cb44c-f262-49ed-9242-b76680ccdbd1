/**
 * Skills Dashboard Component
 * Feature 1.1: Skills Vector Representation & Scoring - UI Design Phase
 * Displays user skill assessment results and analytics
 */
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart3, 
  TrendingUp, 
  Award,
  Brain,
  Target,
  Star,
  RefreshCw,
  Download,
  Share2,
  Eye
} from 'lucide-react';

// Types
interface SkillScore {
  skill_name: string;
  raw_score: number;
  confidence_weighted_score: number;
  certification_boost: number;
  final_score: number;
}

interface DomainScore {
  domain_id: string;
  domain_name: string;
  score: number;
  skill_count: number;
  top_skills: string[];
}

interface SkillsAssessmentResult {
  assessment_id: string;
  user_id: number;
  skill_scores: SkillScore[];
  domain_scores: Record<string, number>;
  overall_profile: {
    total_skills_assessed: number;
    average_skill_level: number;
    strongest_domain: string;
    certifications_count: number;
    assessment_completeness: number;
  };
  timestamp: string;
}

// Domain information for display
const DOMAIN_INFO: Record<string, { name: string; icon: string; color: string }> = {
  communication_network_security: { 
    name: 'Communication & Network Security', 
    icon: '🌐', 
    color: 'bg-blue-500' 
  },
  identity_access_management: { 
    name: 'Identity & Access Management', 
    icon: '🔐', 
    color: 'bg-green-500' 
  },
  security_architecture_engineering: { 
    name: 'Security Architecture & Engineering', 
    icon: '🏗️', 
    color: 'bg-purple-500' 
  },
  asset_security: { 
    name: 'Asset Security', 
    icon: '🛡️', 
    color: 'bg-orange-500' 
  },
  security_risk_management: { 
    name: 'Security & Risk Management', 
    icon: '⚖️', 
    color: 'bg-red-500' 
  },
  security_assessment_testing: { 
    name: 'Security Assessment & Testing', 
    icon: '🔍', 
    color: 'bg-yellow-500' 
  },
  software_security: { 
    name: 'Software Security', 
    icon: '💻', 
    color: 'bg-indigo-500' 
  },
  security_operations: { 
    name: 'Security Operations', 
    icon: '🚨', 
    color: 'bg-pink-500' 
  }
};

const SkillsDashboard: React.FC = () => {
  const [assessmentData, setAssessmentData] = useState<SkillsAssessmentResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    // Simulate loading assessment data
    const loadAssessmentData = async () => {
      setLoading(true);
      try {
        // This would be an actual API call
        // const response = await fetch('/api/v1/skills/profile/1');
        // const data = await response.json();
        
        // Mock data for demonstration
        const mockData: SkillsAssessmentResult = {
          assessment_id: 'assess_1_1640995200',
          user_id: 1,
          skill_scores: [
            {
              skill_name: 'network_protocols',
              raw_score: 0.75,
              confidence_weighted_score: 0.6,
              certification_boost: 0.1,
              final_score: 0.7
            },
            {
              skill_name: 'incident_response',
              raw_score: 0.5,
              confidence_weighted_score: 0.5,
              certification_boost: 0.1,
              final_score: 0.6
            },
            {
              skill_name: 'threat_modeling',
              raw_score: 0.25,
              confidence_weighted_score: 0.15,
              certification_boost: 0.0,
              final_score: 0.15
            }
          ],
          domain_scores: {
            communication_network_security: 0.7,
            identity_access_management: 0.3,
            security_architecture_engineering: 0.15,
            asset_security: 0.4,
            security_risk_management: 0.2,
            security_assessment_testing: 0.5,
            software_security: 0.3,
            security_operations: 0.6
          },
          overall_profile: {
            total_skills_assessed: 15,
            average_skill_level: 0.45,
            strongest_domain: 'communication_network_security',
            certifications_count: 2,
            assessment_completeness: 0.21
          },
          timestamp: new Date().toISOString()
        };
        
        setAssessmentData(mockData);
      } catch (error) {
        console.error('Failed to load assessment data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadAssessmentData();
  }, []);

  const getDomainScores = (): DomainScore[] => {
    if (!assessmentData) return [];
    
    return Object.entries(assessmentData.domain_scores).map(([domainId, score]) => ({
      domain_id: domainId,
      domain_name: DOMAIN_INFO[domainId]?.name || domainId,
      score,
      skill_count: assessmentData.skill_scores.filter(skill => 
        skill.skill_name.includes(domainId.split('_')[0])
      ).length,
      top_skills: assessmentData.skill_scores
        .filter(skill => skill.skill_name.includes(domainId.split('_')[0]))
        .sort((a, b) => b.final_score - a.final_score)
        .slice(0, 3)
        .map(skill => skill.skill_name)
    })).sort((a, b) => b.score - a.score);
  };

  const getSkillLevelLabel = (score: number): { label: string; color: string } => {
    if (score >= 0.8) return { label: 'Expert', color: 'bg-purple-100 text-purple-800' };
    if (score >= 0.6) return { label: 'Advanced', color: 'bg-orange-100 text-orange-800' };
    if (score >= 0.4) return { label: 'Intermediate', color: 'bg-green-100 text-green-800' };
    if (score >= 0.2) return { label: 'Basic', color: 'bg-blue-100 text-blue-800' };
    return { label: 'Beginner', color: 'bg-gray-100 text-gray-800' };
  };

  if (loading) {
    return (
      <div className="max-w-6xl mx-auto space-y-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              <span className="ml-4 text-gray-600">Loading your skills assessment...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!assessmentData) {
    return (
      <div className="max-w-6xl mx-auto space-y-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <Brain className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Assessment Data</h3>
              <p className="text-gray-600 mb-6">Take a skills assessment to see your results here.</p>
              <Button onClick={() => window.location.href = '/skills/assessment'}>
                <Target className="h-4 w-4 mr-2" />
                Take Assessment
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const domainScores = getDomainScores();
  const overallLevel = getSkillLevelLabel(assessmentData.overall_profile.average_skill_level);

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-6 w-6 text-blue-600" />
                Skills Assessment Results
              </CardTitle>
              <p className="text-gray-600 mt-1">
                Last updated: {new Date(assessmentData.timestamp).toLocaleDateString()}
              </p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retake Assessment
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button variant="outline" size="sm">
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Brain className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Overall Level</p>
                <Badge className={overallLevel.color}>{overallLevel.label}</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Target className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Skills Assessed</p>
                <p className="text-2xl font-bold">{assessmentData.overall_profile.total_skills_assessed}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Award className="h-6 w-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Certifications</p>
                <p className="text-2xl font-bold">{assessmentData.overall_profile.certifications_count}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Completeness</p>
                <p className="text-2xl font-bold">
                  {Math.round(assessmentData.overall_profile.assessment_completeness * 100)}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Results */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Domain Overview</TabsTrigger>
          <TabsTrigger value="skills">Individual Skills</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Domain Scores</CardTitle>
              <p className="text-gray-600">Your proficiency across the 8 cybersecurity domains</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {domainScores.map((domain) => {
                  const domainInfo = DOMAIN_INFO[domain.domain_id];
                  const scorePercent = Math.round(domain.score * 100);
                  
                  return (
                    <div key={domain.domain_id} className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <div className={`w-10 h-10 rounded-lg ${domainInfo?.color || 'bg-gray-500'} flex items-center justify-center text-white text-lg`}>
                          {domainInfo?.icon || '📊'}
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {domain.domain_name}
                          </p>
                          <span className="text-sm text-gray-500">{scorePercent}%</span>
                        </div>
                        <Progress value={scorePercent} className="h-2" />
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="skills" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Individual Skill Scores</CardTitle>
              <p className="text-gray-600">Detailed breakdown of your skill assessments</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {assessmentData.skill_scores
                  .sort((a, b) => b.final_score - a.final_score)
                  .map((skill) => {
                    const level = getSkillLevelLabel(skill.final_score);
                    return (
                      <div key={skill.skill_name} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium capitalize">
                            {skill.skill_name.replace(/_/g, ' ')}
                          </h4>
                          <Badge className={level.color}>{level.label}</Badge>
                        </div>
                        <div className="grid grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">Raw Score:</span>
                            <div className="font-medium">{Math.round(skill.raw_score * 100)}%</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Confidence:</span>
                            <div className="font-medium">{Math.round(skill.confidence_weighted_score * 100)}%</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Cert Boost:</span>
                            <div className="font-medium">+{Math.round(skill.certification_boost * 100)}%</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Final Score:</span>
                            <div className="font-medium text-blue-600">{Math.round(skill.final_score * 100)}%</div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Personalized Recommendations</CardTitle>
              <p className="text-gray-600">Based on your assessment results</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border rounded-lg p-4 bg-blue-50">
                  <h4 className="font-medium text-blue-900 mb-2">🎯 Focus Areas</h4>
                  <p className="text-blue-800">
                    Consider strengthening your Security Architecture & Engineering skills to complement your strong Network Security foundation.
                  </p>
                </div>
                <div className="border rounded-lg p-4 bg-green-50">
                  <h4 className="font-medium text-green-900 mb-2">📚 Recommended Learning</h4>
                  <p className="text-green-800">
                    Based on your profile, consider pursuing CISSP or SABSA certifications to advance your architecture skills.
                  </p>
                </div>
                <div className="border rounded-lg p-4 bg-orange-50">
                  <h4 className="font-medium text-orange-900 mb-2">🚀 Career Paths</h4>
                  <p className="text-orange-800">
                    Your skills align well with Security Engineer and Network Security Specialist roles.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SkillsDashboard;
