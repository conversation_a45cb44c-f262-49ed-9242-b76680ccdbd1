import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { Badge } from '../components/ui/badge';
import { Progress } from '../components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { 
  TrendingUp, 
  TrendingDown,
  BarChart3,
  PieChart,
  Globe,
  Users,
  DollarSign,
  Award,
  AlertCircle,
  CheckCircle,
  Target,
  Building
} from 'lucide-react';

interface MarketTrend {
  certification: string;
  demandChange: number;
  salaryTrend: number;
  jobOpenings: number;
  competitionLevel: string;
  growthProjection: number;
}

interface LocationData {
  location: string;
  averageSalary: number;
  jobCount: number;
  costOfLiving: number;
  demandLevel: string;
}

interface IndustryInsight {
  industry: string;
  topCertifications: string[];
  averageBudget: number;
  growthRate: number;
  keyTrends: string[];
}

interface MarketIntelligenceProps {
  className?: string;
}

const MarketIntelligence: React.FC<MarketIntelligenceProps> = ({ className }) => {
  const [selectedRegion, setSelectedRegion] = useState('global');
  const [selectedIndustry, setSelectedIndustry] = useState('technology');
  const [selectedTimeframe, setSelectedTimeframe] = useState('12months');
  const [marketData, setMarketData] = useState<{
    trends: MarketTrend[];
    locations: LocationData[];
    industries: IndustryInsight[];
  } | null>(null);
  const [loading, setLoading] = useState(false);

  const regions = [
    'global',
    'north_america',
    'europe',
    'asia_pacific',
    'latin_america'
  ];

  const industries = [
    'technology',
    'financial_services',
    'healthcare',
    'government',
    'manufacturing',
    'retail',
    'energy'
  ];

  const timeframes = [
    { value: '6months', label: '6 Months' },
    { value: '12months', label: '12 Months' },
    { value: '24months', label: '24 Months' },
    { value: '36months', label: '36 Months' }
  ];

  useEffect(() => {
    loadMarketData();
  }, [selectedRegion, selectedIndustry, selectedTimeframe]);

  const loadMarketData = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/v1/market-intelligence/analysis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          region: selectedRegion,
          industry: selectedIndustry,
          timeframe: selectedTimeframe,
          include_projections: true
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setMarketData(data);
      }
    } catch (error) {
      console.error('Error loading market data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    const sign = value >= 0 ? '+' : '';
    return `${sign}${Math.round(value)}%`;
  };

  const getTrendColor = (value: number) => {
    if (value >= 10) return 'text-green-600';
    if (value >= 0) return 'text-blue-600';
    if (value >= -10) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getTrendIcon = (value: number) => {
    return value >= 0 ? TrendingUp : TrendingDown;
  };

  const getDemandColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'high': return 'success';
      case 'medium': return 'warning';
      case 'low': return 'error';
      default: return 'secondary';
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Market Intelligence</h1>
          <p className="text-muted-foreground">
            Real-time market trends and intelligence for cybersecurity careers
          </p>
        </div>
      </div>

      {/* Filters */}
      <Card data-testid="market-filters">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Market Analysis Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Region</label>
              <Select value={selectedRegion} onValueChange={setSelectedRegion}>
                <SelectTrigger data-testid="region-select">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {regions.map((region) => (
                    <SelectItem key={region} value={region}>
                      {region.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Industry</label>
              <Select value={selectedIndustry} onValueChange={setSelectedIndustry}>
                <SelectTrigger data-testid="industry-select">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {industries.map((industry) => (
                    <SelectItem key={industry} value={industry}>
                      {industry.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Timeframe</label>
              <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
                <SelectTrigger data-testid="timeframe-select">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {timeframes.map((timeframe) => (
                    <SelectItem key={timeframe.value} value={timeframe.value}>
                      {timeframe.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Market Data */}
      {marketData && (
        <div className="space-y-6" data-testid="market-data">
          <Tabs defaultValue="trends" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="trends" data-testid="trends-tab">Certification Trends</TabsTrigger>
              <TabsTrigger value="locations" data-testid="locations-tab">Location Analysis</TabsTrigger>
              <TabsTrigger value="industries" data-testid="industries-tab">Industry Insights</TabsTrigger>
            </TabsList>

            <TabsContent value="trends" className="space-y-4" data-testid="trends-content">
              <div className="grid grid-cols-1 gap-4">
                {marketData.trends.map((trend, index) => (
                  <Card key={index} data-testid="trend-card">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <Award className="h-6 w-6 text-yellow-600" />
                          <h3 className="text-lg font-semibold">{trend.certification}</h3>
                        </div>
                        <Badge variant={getDemandColor(trend.competitionLevel)}>
                          {trend.competitionLevel} Competition
                        </Badge>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center">
                          <div className={`text-2xl font-bold flex items-center justify-center gap-1 ${getTrendColor(trend.demandChange)}`}>
                            {React.createElement(getTrendIcon(trend.demandChange), { className: "h-5 w-5" })}
                            {formatPercentage(trend.demandChange)}
                          </div>
                          <div className="text-sm text-muted-foreground">Demand Change</div>
                        </div>

                        <div className="text-center">
                          <div className={`text-2xl font-bold flex items-center justify-center gap-1 ${getTrendColor(trend.salaryTrend)}`}>
                            {React.createElement(getTrendIcon(trend.salaryTrend), { className: "h-5 w-5" })}
                            {formatPercentage(trend.salaryTrend)}
                          </div>
                          <div className="text-sm text-muted-foreground">Salary Trend</div>
                        </div>

                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-600">
                            {trend.jobOpenings.toLocaleString()}
                          </div>
                          <div className="text-sm text-muted-foreground">Job Openings</div>
                        </div>

                        <div className="text-center">
                          <div className={`text-2xl font-bold ${getTrendColor(trend.growthProjection)}`}>
                            {formatPercentage(trend.growthProjection)}
                          </div>
                          <div className="text-sm text-muted-foreground">Growth Projection</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="locations" className="space-y-4" data-testid="locations-content">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {marketData.locations.map((location, index) => (
                  <Card key={index} data-testid="location-card">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <Building className="h-6 w-6 text-blue-600" />
                          <h3 className="text-lg font-semibold">{location.location}</h3>
                        </div>
                        <Badge variant={getDemandColor(location.demandLevel)}>
                          {location.demandLevel} Demand
                        </Badge>
                      </div>

                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-muted-foreground">Average Salary</span>
                          <span className="font-bold text-green-600">
                            {formatCurrency(location.averageSalary)}
                          </span>
                        </div>

                        <div className="flex justify-between items-center">
                          <span className="text-sm text-muted-foreground">Job Count</span>
                          <span className="font-bold text-blue-600">
                            {location.jobCount.toLocaleString()}
                          </span>
                        </div>

                        <div className="flex justify-between items-center">
                          <span className="text-sm text-muted-foreground">Cost of Living Index</span>
                          <span className="font-bold">
                            {location.costOfLiving}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="industries" className="space-y-4" data-testid="industries-content">
              <div className="grid grid-cols-1 gap-4">
                {marketData.industries.map((industry, index) => (
                  <Card key={index} data-testid="industry-card">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <Target className="h-6 w-6 text-purple-600" />
                          <h3 className="text-lg font-semibold capitalize">
                            {industry.industry.replace('_', ' ')}
                          </h3>
                        </div>
                        <div className={`text-lg font-bold ${getTrendColor(industry.growthRate)}`}>
                          {formatPercentage(industry.growthRate)} Growth
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="font-medium mb-2">Top Certifications</h4>
                          <div className="space-y-2">
                            {industry.topCertifications.map((cert, certIndex) => (
                              <div key={certIndex} className="flex items-center gap-2">
                                <CheckCircle className="h-4 w-4 text-green-600" />
                                <span className="text-sm">{cert}</span>
                              </div>
                            ))}
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium mb-2">Key Insights</h4>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-sm text-muted-foreground">Average Training Budget</span>
                              <span className="font-bold text-green-600">
                                {formatCurrency(industry.averageBudget)}
                              </span>
                            </div>
                            <div className="space-y-1">
                              {industry.keyTrends.map((trend, trendIndex) => (
                                <div key={trendIndex} className="flex items-center gap-2">
                                  <AlertCircle className="h-4 w-4 text-blue-600" />
                                  <span className="text-sm">{trend}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      )}

      {loading && (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading market intelligence...</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default MarketIntelligence;
