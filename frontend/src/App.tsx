import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CircularProgress, Box } from '@mui/material';
import Navigation from './components/Navigation';
import './App.css';

// Lazy load components for better performance
const Dashboard = React.lazy(() => import('./pages/Dashboard'));
const CertificationExplorer = React.lazy(() => import('./pages/CertificationExplorer'));
const CostCalculator = React.lazy(() => import('./pages/CostCalculator'));
const UserProfile = React.lazy(() => import('./pages/UserProfile'));
const EnterpriseDashboard = React.lazy(() => import('./pages/EnterpriseDashboard'));
const JobSearch = React.lazy(() => import('./pages/JobSearch'));
const StudyTimer = React.lazy(() => import('./pages/StudyTimer'));

// Loading component
const LoadingSpinner = () => (
  <Box
    display="flex"
    justifyContent="center"
    alignItems="center"
    height="100vh"
  >
    <CircularProgress />
  </Box>
);

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <div className="flex h-screen bg-gray-100">
          <Navigation />
          <main className="flex-1 overflow-y-auto lg:ml-0 ml-0">
            <div className="lg:p-0 pt-16 lg:pt-0"> {/* Add top padding on mobile for menu button */}
              <Suspense fallback={<LoadingSpinner />}>
                <Routes>
                  <Route path="/" element={<Dashboard />} />
                  <Route path="/certifications" element={<CertificationExplorer />} />
                  <Route path="/cost-calculator" element={<CostCalculator />} />
                  <Route path="/profile" element={<UserProfile />} />
                  <Route path="/study-timer" element={<StudyTimer />} />
                  <Route path="/job-search" element={<JobSearch />} />
                  <Route path="/enterprise" element={<EnterpriseDashboard />} />
                  <Route path="/enterprise/teams" element={<EnterpriseDashboard />} />
                  <Route path="/enterprise/analytics" element={<EnterpriseDashboard />} />
                  <Route path="/career-path" element={<div className="p-8">Career Path Generator - Coming Soon</div>} />
                  <Route path="/user-journeys" element={<div className="p-8">User Journeys - Coming Soon</div>} />
                  <Route path="/faq" element={<div className="p-8">FAQ - Coming Soon</div>} />
                  <Route path="/progress" element={<div className="p-8">My Progress - Coming Soon</div>} />
                  <Route path="/achievements" element={<div className="p-8">Achievements - Coming Soon</div>} />
                </Routes>
              </Suspense>
            </div>
          </main>
        </div>
      </Router>
    </QueryClientProvider>
  );
}

export default App;
