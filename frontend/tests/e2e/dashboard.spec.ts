import { test, expect } from '@playwright/test';

test.describe('Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should display dashboard title and description', async ({ page }) => {
    await expect(page.locator('h1')).toContainText('Dashboard');
    await expect(page.locator('p')).toContainText('Welcome back! Here\'s what\'s happening with your certifications.');
  });

  test('should display stats cards', async ({ page }) => {
    // Wait for data to load
    await page.waitForSelector('[data-testid="stats-grid"]', { timeout: 10000 });
    
    // Check for stats cards
    const statsCards = page.locator('[data-testid="stats-card"]');
    await expect(statsCards).toHaveCount(4);
    
    // Check specific stats
    await expect(page.locator('text=Total Certifications')).toBeVisible();
    await expect(page.locator('text=Entry Level')).toBeVisible();
    await expect(page.locator('text=Advanced')).toBeVisible();
    await expect(page.locator('text=Average Cost')).toBeVisible();
  });

  test('should display quick actions', async ({ page }) => {
    await expect(page.locator('text=Quick Actions')).toBeVisible();
    await expect(page.locator('text=Explore Certifications')).toBeVisible();
    await expect(page.locator('text=Calculate Costs')).toBeVisible();
    await expect(page.locator('text=Plan Study Time')).toBeVisible();
  });

  test('should navigate to certification explorer when clicking quick action', async ({ page }) => {
    await page.click('text=Explore Certifications');
    await expect(page).toHaveURL('/certifications');
  });

  test('should display recent activity', async ({ page }) => {
    await expect(page.locator('text=Recent Activity')).toBeVisible();
    
    // Check for activity items
    const activityItems = page.locator('[data-testid="activity-item"]');
    await expect(activityItems.first()).toBeVisible();
  });

  test('should display system status', async ({ page }) => {
    await expect(page.locator('text=System Status')).toBeVisible();
    await expect(page.locator('text=API Status')).toBeVisible();
    await expect(page.locator('text=Database')).toBeVisible();
  });

  test('should be responsive on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check that the layout adapts to mobile
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('[data-testid="stats-grid"]')).toBeVisible();
  });

  test('should handle loading state', async ({ page }) => {
    // Intercept API calls to simulate slow loading
    await page.route('**/api/v1/certifications/', async route => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      await route.continue();
    });

    await page.goto('/');
    
    // Check for loading skeleton
    await expect(page.locator('.animate-pulse')).toBeVisible();
  });

  test('should handle error state', async ({ page }) => {
    // Intercept API calls to simulate error
    await page.route('**/api/v1/certifications/', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal Server Error' })
      });
    });

    await page.goto('/');
    
    // Check for error message
    await expect(page.locator('text=Error Loading Dashboard')).toBeVisible();
  });
});
