

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Quick Start Guide &mdash; CertPathFinder 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=c14262df" />

  
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="_static/documentation_options.js?v=8d563738"></script>
      <script src="_static/doctools.js?v=9bcbadda"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="API Reference" href="api/index.html" />
    <link rel="prev" title="Installation Guide" href="installation.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="index.html" class="icon icon-home">
            CertPathFinder
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started:</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="installation.html">Installation Guide</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Quick Start Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#first-steps">First Steps</a></li>
<li class="toctree-l2"><a class="reference internal" href="#core-features">Core Features</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#cost-calculator">Cost Calculator</a></li>
<li class="toctree-l3"><a class="reference internal" href="#career-framework">Career Framework</a></li>
<li class="toctree-l3"><a class="reference internal" href="#study-timer">Study Timer</a></li>
<li class="toctree-l3"><a class="reference internal" href="#ai-assistant">AI Assistant</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#web-interface-usage">Web Interface Usage</a></li>
<li class="toctree-l2"><a class="reference internal" href="#key-workflows">Key Workflows</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#certification-planning-workflow">Certification Planning Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="#career-transition-workflow">Career Transition Workflow</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#api-integration">API Integration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#authentication">Authentication</a></li>
<li class="toctree-l3"><a class="reference internal" href="#common-api-patterns">Common API Patterns</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#next-steps">Next Steps</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="api/index.html">API Reference</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">CertPathFinder</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Quick Start Guide</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/quickstart.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="quick-start-guide">
<h1>Quick Start Guide<a class="headerlink" href="#quick-start-guide" title="Link to this heading"></a></h1>
<p>This guide will help you get started with CertPathFinder quickly and understand its core features.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>CertPathFinder is a comprehensive cybersecurity certification and career guidance platform that provides:</p>
<ul class="simple">
<li><p><strong>Personalized career recommendations</strong> based on real market data</p></li>
<li><p><strong>ROI analysis for certifications</strong> with cost-benefit calculations</p></li>
<li><p><strong>AI-powered study assistance</strong> and progress tracking</p></li>
<li><p><strong>Enterprise integrations</strong> for organizations</p></li>
<li><p><strong>Mobile-first design</strong> for learning on the go</p></li>
</ul>
</section>
<section id="first-steps">
<h2>First Steps<a class="headerlink" href="#first-steps" title="Link to this heading"></a></h2>
<ol class="arabic">
<li><p><strong>Access the Platform</strong></p>
<p>After installation, access CertPathFinder at:</p>
<ul class="simple">
<li><p><strong>Web Interface</strong>: <a class="reference external" href="http://localhost:8501">http://localhost:8501</a></p></li>
<li><p><strong>API Documentation</strong>: <a class="reference external" href="http://localhost:8000/docs">http://localhost:8000/docs</a></p></li>
<li><p><strong>API Base URL</strong>: <a class="reference external" href="http://localhost:8000/api/v1">http://localhost:8000/api/v1</a></p></li>
</ul>
</li>
<li><p><strong>Create Your Profile</strong></p>
<p>Start by creating a user profile to get personalized recommendations:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span><span class="w"> </span><span class="nn">requests</span>

<span class="c1"># Create user profile</span>
<span class="n">user_data</span> <span class="o">=</span> <span class="p">{</span>
    <span class="s2">&quot;email&quot;</span><span class="p">:</span> <span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
    <span class="s2">&quot;full_name&quot;</span><span class="p">:</span> <span class="s2">&quot;John Doe&quot;</span><span class="p">,</span>
    <span class="s2">&quot;current_role&quot;</span><span class="p">:</span> <span class="s2">&quot;Security Analyst&quot;</span><span class="p">,</span>
    <span class="s2">&quot;experience_years&quot;</span><span class="p">:</span> <span class="mi">3</span><span class="p">,</span>
    <span class="s2">&quot;target_domain&quot;</span><span class="p">:</span> <span class="s2">&quot;cloud_security&quot;</span>
<span class="p">}</span>

<span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">post</span><span class="p">(</span>
    <span class="s2">&quot;http://localhost:8000/api/v1/user/profile&quot;</span><span class="p">,</span>
    <span class="n">json</span><span class="o">=</span><span class="n">user_data</span>
<span class="p">)</span>
</pre></div>
</div>
</li>
<li><p><strong>Explore Certifications</strong></p>
<p>Get certification recommendations based on your profile:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Get certification recommendations</span>
<span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">get</span><span class="p">(</span>
    <span class="s2">&quot;http://localhost:8000/api/v1/enhanced-taxonomy/certifications/high-value&quot;</span>
<span class="p">)</span>
<span class="n">certifications</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>
</pre></div>
</div>
</li>
</ol>
</section>
<section id="core-features">
<h2>Core Features<a class="headerlink" href="#core-features" title="Link to this heading"></a></h2>
<section id="cost-calculator">
<h3>Cost Calculator<a class="headerlink" href="#cost-calculator" title="Link to this heading"></a></h3>
<p>Calculate the ROI of certifications with real market data:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Calculate certification costs</span>
<span class="n">cost_data</span> <span class="o">=</span> <span class="p">{</span>
    <span class="s2">&quot;certification_id&quot;</span><span class="p">:</span> <span class="mi">1</span><span class="p">,</span>
    <span class="s2">&quot;location&quot;</span><span class="p">:</span> <span class="s2">&quot;United States&quot;</span><span class="p">,</span>
    <span class="s2">&quot;study_hours_per_week&quot;</span><span class="p">:</span> <span class="mi">10</span><span class="p">,</span>
    <span class="s2">&quot;target_completion_months&quot;</span><span class="p">:</span> <span class="mi">6</span>
<span class="p">}</span>

<span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">post</span><span class="p">(</span>
    <span class="s2">&quot;http://localhost:8000/api/v1/cost-calculator/calculate&quot;</span><span class="p">,</span>
    <span class="n">json</span><span class="o">=</span><span class="n">cost_data</span>
<span class="p">)</span>
<span class="n">cost_analysis</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>
</pre></div>
</div>
</section>
<section id="career-framework">
<h3>Career Framework<a class="headerlink" href="#career-framework" title="Link to this heading"></a></h3>
<p>Explore career paths in cybersecurity:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Get career progression options</span>
<span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">get</span><span class="p">(</span>
    <span class="s2">&quot;http://localhost:8000/api/v1/enhanced-taxonomy/job-titles/high-demand&quot;</span>
<span class="p">)</span>
<span class="n">job_opportunities</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>

<span class="c1"># Get career progression from current role</span>
<span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">get</span><span class="p">(</span>
    <span class="s2">&quot;http://localhost:8000/api/v1/enhanced-taxonomy/job-titles/1/progression&quot;</span>
<span class="p">)</span>
<span class="n">career_paths</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>
</pre></div>
</div>
</section>
<section id="study-timer">
<h3>Study Timer<a class="headerlink" href="#study-timer" title="Link to this heading"></a></h3>
<p>Track your study sessions and progress:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Start a study session</span>
<span class="n">session_data</span> <span class="o">=</span> <span class="p">{</span>
    <span class="s2">&quot;user_id&quot;</span><span class="p">:</span> <span class="mi">1</span><span class="p">,</span>
    <span class="s2">&quot;certification_id&quot;</span><span class="p">:</span> <span class="mi">1</span><span class="p">,</span>
    <span class="s2">&quot;planned_duration_minutes&quot;</span><span class="p">:</span> <span class="mi">60</span><span class="p">,</span>
    <span class="s2">&quot;study_type&quot;</span><span class="p">:</span> <span class="s2">&quot;reading&quot;</span>
<span class="p">}</span>

<span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">post</span><span class="p">(</span>
    <span class="s2">&quot;http://localhost:8000/api/v1/study-timer/sessions/start&quot;</span><span class="p">,</span>
    <span class="n">json</span><span class="o">=</span><span class="n">session_data</span>
<span class="p">)</span>
<span class="n">session</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>

<span class="c1"># End the session</span>
<span class="n">requests</span><span class="o">.</span><span class="n">post</span><span class="p">(</span>
    <span class="sa">f</span><span class="s2">&quot;http://localhost:8000/api/v1/study-timer/sessions/</span><span class="si">{</span><span class="n">session</span><span class="p">[</span><span class="s1">&#39;id&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">/end&quot;</span>
<span class="p">)</span>
</pre></div>
</div>
</section>
<section id="ai-assistant">
<h3>AI Assistant<a class="headerlink" href="#ai-assistant" title="Link to this heading"></a></h3>
<p>Get personalized study recommendations:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Get skill recommendations</span>
<span class="n">recommendations_data</span> <span class="o">=</span> <span class="p">{</span>
    <span class="s2">&quot;user_id&quot;</span><span class="p">:</span> <span class="mi">1</span><span class="p">,</span>
    <span class="s2">&quot;current_skills&quot;</span><span class="p">:</span> <span class="p">[</span><span class="s2">&quot;Network Security&quot;</span><span class="p">,</span> <span class="s2">&quot;Incident Response&quot;</span><span class="p">],</span>
    <span class="s2">&quot;target_domain&quot;</span><span class="p">:</span> <span class="s2">&quot;cloud_security&quot;</span>
<span class="p">}</span>

<span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">post</span><span class="p">(</span>
    <span class="s2">&quot;http://localhost:8000/api/v1/enhanced-taxonomy/recommendations/skills&quot;</span><span class="p">,</span>
    <span class="n">json</span><span class="o">=</span><span class="n">recommendations_data</span>
<span class="p">)</span>
<span class="n">skill_recommendations</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>
</pre></div>
</div>
</section>
</section>
<section id="web-interface-usage">
<h2>Web Interface Usage<a class="headerlink" href="#web-interface-usage" title="Link to this heading"></a></h2>
<p>The Streamlit web interface provides an intuitive way to interact with all features:</p>
<ol class="arabic simple">
<li><p><strong>Dashboard</strong>: Overview of your progress and recommendations</p></li>
<li><p><strong>Certifications</strong>: Browse and compare certifications</p></li>
<li><p><strong>Career Paths</strong>: Explore career opportunities and progression</p></li>
<li><p><strong>Study Timer</strong>: Track study sessions and productivity</p></li>
<li><p><strong>Cost Calculator</strong>: Analyze certification ROI</p></li>
<li><p><strong>Progress Tracking</strong>: Monitor learning goals and achievements</p></li>
</ol>
</section>
<section id="key-workflows">
<h2>Key Workflows<a class="headerlink" href="#key-workflows" title="Link to this heading"></a></h2>
<section id="certification-planning-workflow">
<h3>Certification Planning Workflow<a class="headerlink" href="#certification-planning-workflow" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Assess Current Skills</strong>: Use the skill assessment feature</p></li>
<li><p><strong>Set Career Goals</strong>: Define target roles and timeframes</p></li>
<li><p><strong>Get Recommendations</strong>: Receive personalized certification suggestions</p></li>
<li><p><strong>Calculate ROI</strong>: Analyze costs and benefits</p></li>
<li><p><strong>Create Study Plan</strong>: Set up learning goals and schedules</p></li>
<li><p><strong>Track Progress</strong>: Monitor study sessions and achievements</p></li>
</ol>
</section>
<section id="career-transition-workflow">
<h3>Career Transition Workflow<a class="headerlink" href="#career-transition-workflow" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Current Role Analysis</strong>: Input your current position and skills</p></li>
<li><p><strong>Target Role Selection</strong>: Choose desired career direction</p></li>
<li><p><strong>Gap Analysis</strong>: Identify skill and certification gaps</p></li>
<li><p><strong>Learning Path Creation</strong>: Get step-by-step guidance</p></li>
<li><p><strong>Progress Monitoring</strong>: Track advancement toward goals</p></li>
<li><p><strong>Market Intelligence</strong>: Stay updated on industry trends</p></li>
</ol>
</section>
</section>
<section id="api-integration">
<h2>API Integration<a class="headerlink" href="#api-integration" title="Link to this heading"></a></h2>
<p>For developers integrating with CertPathFinder:</p>
<section id="authentication">
<h3>Authentication<a class="headerlink" href="#authentication" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Get access token</span>
<span class="n">auth_data</span> <span class="o">=</span> <span class="p">{</span>
    <span class="s2">&quot;username&quot;</span><span class="p">:</span> <span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
    <span class="s2">&quot;password&quot;</span><span class="p">:</span> <span class="s2">&quot;password&quot;</span>
<span class="p">}</span>

<span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">post</span><span class="p">(</span>
    <span class="s2">&quot;http://localhost:8000/api/v1/auth/login&quot;</span><span class="p">,</span>
    <span class="n">data</span><span class="o">=</span><span class="n">auth_data</span>
<span class="p">)</span>
<span class="n">token</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()[</span><span class="s2">&quot;access_token&quot;</span><span class="p">]</span>

<span class="c1"># Use token in subsequent requests</span>
<span class="n">headers</span> <span class="o">=</span> <span class="p">{</span><span class="s2">&quot;Authorization&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;Bearer </span><span class="si">{</span><span class="n">token</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">}</span>
</pre></div>
</div>
</section>
<section id="common-api-patterns">
<h3>Common API Patterns<a class="headerlink" href="#common-api-patterns" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Get user&#39;s personalized data</span>
<span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">get</span><span class="p">(</span>
    <span class="s2">&quot;http://localhost:8000/api/v1/user/dashboard&quot;</span><span class="p">,</span>
    <span class="n">headers</span><span class="o">=</span><span class="n">headers</span>
<span class="p">)</span>

<span class="c1"># Submit assessment data</span>
<span class="n">assessment_data</span> <span class="o">=</span> <span class="p">{</span>
    <span class="s2">&quot;skill_assessments&quot;</span><span class="p">:</span> <span class="p">[</span>
        <span class="p">{</span><span class="s2">&quot;skill_id&quot;</span><span class="p">:</span> <span class="mi">1</span><span class="p">,</span> <span class="s2">&quot;skill_level&quot;</span><span class="p">:</span> <span class="s2">&quot;intermediate&quot;</span><span class="p">,</span> <span class="s2">&quot;confidence_level&quot;</span><span class="p">:</span> <span class="mi">4</span><span class="p">}</span>
    <span class="p">]</span>
<span class="p">}</span>

<span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">post</span><span class="p">(</span>
    <span class="s2">&quot;http://localhost:8000/api/v1/enhanced-taxonomy/assess/skills&quot;</span><span class="p">,</span>
    <span class="n">json</span><span class="o">=</span><span class="n">assessment_data</span><span class="p">,</span>
    <span class="n">headers</span><span class="o">=</span><span class="n">headers</span>
<span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="next-steps">
<h2>Next Steps<a class="headerlink" href="#next-steps" title="Link to this heading"></a></h2>
<p>Now that you’re familiar with the basics:</p>
<ul class="simple">
<li><p>Explore the complete <a class="reference internal" href="api/index.html"><span class="doc">API Reference</span></a> documentation</p></li>
<li><p>Read the detailed <span class="xref std std-doc">guides/user_guide</span></p></li>
<li><p>Check out <span class="xref std std-doc">development/architecture</span> for technical details</p></li>
<li><p>Review <span class="xref std std-doc">guides/enterprise_guide</span> for organizational features</p></li>
</ul>
<p>For more advanced usage and customization, see the comprehensive guides in the documentation.</p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="installation.html" class="btn btn-neutral float-left" title="Installation Guide" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="api/index.html" class="btn btn-neutral float-right" title="API Reference" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, CertPathFinder Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>