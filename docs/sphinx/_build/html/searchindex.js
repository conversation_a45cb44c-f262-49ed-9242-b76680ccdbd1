Search.setIndex({"alltitles": {"AI Assistant": [[3, "ai-assistant"]], "AI Study Assistant API": [[0, "ai-study-assistant-api"]], "API Integration": [[3, "api-integration"]], "API Modules": [[0, "api-modules"]], "API Reference": [[0, null]], "API Reference:": [[1, null]], "Authentication": [[0, "authentication"], [3, "authentication"]], "Base URL": [[0, "base-url"]], "Career Framework": [[3, "career-framework"]], "Career Transition API": [[0, "career-transition-api"]], "Career Transition Workflow": [[3, "career-transition-workflow"]], "CertPathFinder Documentation": [[1, null]], "Certification Planning Workflow": [[3, "certification-planning-workflow"]], "Common API Patterns": [[3, "common-api-patterns"]], "Common Response Formats": [[0, "common-response-formats"]], "Core Features": [[3, "core-features"]], "Cost Calculator": [[3, "cost-calculator"]], "Cost Calculator API": [[0, "cost-calculator-api"]], "Enhanced Security Taxonomy API": [[0, "enhanced-security-taxonomy-api"]], "Enterprise Dashboard API": [[0, "enterprise-dashboard-api"]], "Environment Configuration": [[2, "environment-configuration"]], "Error Codes": [[0, "error-codes"]], "Error Response": [[0, "error-response"]], "First Steps": [[3, "first-steps"]], "Getting Started:": [[1, null]], "Indices and tables": [[1, "indices-and-tables"]], "Installation Guide": [[2, null]], "Integration Hub API": [[0, "integration-hub-api"]], "Interactive Documentation": [[0, "interactive-documentation"]], "Key Workflows": [[3, "key-workflows"]], "Manual Installation": [[2, "manual-installation"]], "Mobile Enterprise API": [[0, "mobile-enterprise-api"]], "Next Steps": [[2, "next-steps"], [3, "next-steps"]], "Overview": [[3, "overview"]], "Pagination": [[0, "pagination"]], "Prerequisites": [[2, "prerequisites"]], "Progress Tracking API": [[0, "progress-tracking-api"]], "Quick Start Guide": [[3, null]], "Quick Start with Docker": [[2, "quick-start-with-docker"]], "Rate Limiting": [[0, "rate-limiting"]], "SDK and Client Libraries": [[0, "sdk-and-client-libraries"]], "Security Career Framework API": [[0, "security-career-framework-api"]], "Study Timer": [[3, "study-timer"]], "Study Timer API": [[0, "study-timer-api"]], "Success Response": [[0, "success-response"]], "Troubleshooting": [[2, "troubleshooting"]], "Verification": [[2, "verification"]], "Web Interface Usage": [[3, "web-interface-usage"]], "Webhooks": [[0, "webhooks"]], "\ud83c\udfaf Key Features": [[1, "key-features"]], "\ud83d\udcca Platform Statistics": [[1, "platform-statistics"]], "\ud83d\udcda Documentation Sections": [[1, "documentation-sections"]], "\ud83d\ude80 Quick Links": [[1, "quick-links"]]}, "docnames": ["api/index", "index", "installation", "quickstart"], "envversion": {"sphinx": 64, "sphinx.domains.c": 3, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 9, "sphinx.domains.index": 1, "sphinx.domains.javascript": 3, "sphinx.domains.math": 2, "sphinx.domains.python": 4, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.intersphinx": 1, "sphinx.ext.todo": 2, "sphinx.ext.viewcode": 1}, "filenames": ["api/index.rst", "index.rst", "installation.rst", "quickstart.rst"], "indexentries": {"api.v1.career_transition": [[0, "module-api.v1.career_transition", false]], "api.v1.enterprise": [[0, "module-api.v1.enterprise", false]], "api.v1.integration_hub": [[0, "module-api.v1.integration_hub", false]], "api.v1.mobile": [[0, "module-api.v1.mobile", false]], "api.v1.progress_tracking": [[0, "module-api.v1.progress_tracking", false]], "api.v1.security_career_framework": [[0, "module-api.v1.security_career_framework", false]], "assign_license() (in module api.v1.enterprise)": [[0, "api.v1.enterprise.assign_license", false]], "authenticate_mobile_user() (in module api.v1.mobile)": [[0, "api.v1.mobile.authenticate_mobile_user", false]], "authenticate_sso_user() (in module api.v1.integration_hub)": [[0, "api.v1.integration_hub.authenticate_sso_user", false]], "configure_hr_integration() (in module api.v1.integration_hub)": [[0, "api.v1.integration_hub.configure_hr_integration", false]], "configure_ldap_integration() (in module api.v1.integration_hub)": [[0, "api.v1.integration_hub.configure_ldap_integration", false]], "configure_lms_integration() (in module api.v1.integration_hub)": [[0, "api.v1.integration_hub.configure_lms_integration", false]], "configure_sso_integration() (in module api.v1.integration_hub)": [[0, "api.v1.integration_hub.configure_sso_integration", false]], "configure_webhook_integration() (in module api.v1.integration_hub)": [[0, "api.v1.integration_hub.configure_webhook_integration", false]], "create_department() (in module api.v1.enterprise)": [[0, "api.v1.enterprise.create_department", false]], "create_learning_goal() (in module api.v1.progress_tracking)": [[0, "api.v1.progress_tracking.create_learning_goal", false]], "create_organization() (in module api.v1.enterprise)": [[0, "api.v1.enterprise.create_organization", false]], "create_security_job_type() (in module api.v1.security_career_framework)": [[0, "api.v1.security_career_framework.create_security_job_type", false]], "create_transition_plan() (in module api.v1.career_transition)": [[0, "api.v1.career_transition.create_transition_plan", false]], "create_user() (in module api.v1.enterprise)": [[0, "api.v1.enterprise.create_user", false]], "delete_learning_goal() (in module api.v1.progress_tracking)": [[0, "api.v1.progress_tracking.delete_learning_goal", false]], "delete_organization() (in module api.v1.enterprise)": [[0, "api.v1.enterprise.delete_organization", false]], "delete_study_session() (in module api.v1.progress_tracking)": [[0, "api.v1.progress_tracking.delete_study_session", false]], "delete_transition_plan() (in module api.v1.career_transition)": [[0, "api.v1.career_transition.delete_transition_plan", false]], "end_study_session() (in module api.v1.progress_tracking)": [[0, "api.v1.progress_tracking.end_study_session", false]], "find_career_paths() (in module api.v1.career_transition)": [[0, "api.v1.career_transition.find_career_paths", false]], "generate_career_summary_pdf() (in module api.v1.career_transition)": [[0, "api.v1.career_transition.generate_career_summary_pdf", false]], "generate_path_comparison_pdf() (in module api.v1.career_transition)": [[0, "api.v1.career_transition.generate_path_comparison_pdf", false]], "generate_transition_plan_pdf() (in module api.v1.career_transition)": [[0, "api.v1.career_transition.generate_transition_plan_pdf", false]], "get_achievements() (in module api.v1.progress_tracking)": [[0, "api.v1.progress_tracking.get_achievements", false]], "get_career_recommendations() (in module api.v1.security_career_framework)": [[0, "api.v1.security_career_framework.get_career_recommendations", false]], "get_career_roles() (in module api.v1.career_transition)": [[0, "api.v1.career_transition.get_career_roles", false]], "get_current_admin_user() (in module api.v1.enterprise)": [[0, "api.v1.enterprise.get_current_admin_user", false]], "get_current_mobile_user() (in module api.v1.mobile)": [[0, "api.v1.mobile.get_current_mobile_user", false]], "get_current_user() (in module api.v1.integration_hub)": [[0, "api.v1.integration_hub.get_current_user", false]], "get_current_user_id() (in module api.v1.career_transition)": [[0, "api.v1.career_transition.get_current_user_id", false]], "get_current_user_id() (in module api.v1.progress_tracking)": [[0, "api.v1.progress_tracking.get_current_user_id", false]], "get_device_id() (in module api.v1.mobile)": [[0, "api.v1.mobile.get_device_id", false]], "get_integration_status() (in module api.v1.integration_hub)": [[0, "api.v1.integration_hub.get_integration_status", false]], "get_job_families_by_area() (in module api.v1.security_career_framework)": [[0, "api.v1.security_career_framework.get_job_families_by_area", false]], "get_learning_analytics() (in module api.v1.progress_tracking)": [[0, "api.v1.progress_tracking.get_learning_analytics", false]], "get_learning_goals() (in module api.v1.progress_tracking)": [[0, "api.v1.progress_tracking.get_learning_goals", false]], "get_license_usage() (in module api.v1.enterprise)": [[0, "api.v1.enterprise.get_license_usage", false]], "get_mobile_app_config() (in module api.v1.mobile)": [[0, "api.v1.mobile.get_mobile_app_config", false]], "get_mobile_dashboard() (in module api.v1.mobile)": [[0, "api.v1.mobile.get_mobile_dashboard", false]], "get_mobile_practice_test() (in module api.v1.mobile)": [[0, "api.v1.mobile.get_mobile_practice_test", false]], "get_offline_ai_recommendations() (in module api.v1.mobile)": [[0, "api.v1.mobile.get_offline_ai_recommendations", false]], "get_offline_data_package() (in module api.v1.mobile)": [[0, "api.v1.mobile.get_offline_data_package", false]], "get_organization() (in module api.v1.enterprise)": [[0, "api.v1.enterprise.get_organization", false]], "get_organization_analytics() (in module api.v1.enterprise)": [[0, "api.v1.enterprise.get_organization_analytics", false]], "get_organization_dashboard() (in module api.v1.enterprise)": [[0, "api.v1.enterprise.get_organization_dashboard", false]], "get_organization_id() (in module api.v1.integration_hub)": [[0, "api.v1.integration_hub.get_organization_id", false]], "get_practice_test_results() (in module api.v1.progress_tracking)": [[0, "api.v1.progress_tracking.get_practice_test_results", false]], "get_progress_dashboard() (in module api.v1.progress_tracking)": [[0, "api.v1.progress_tracking.get_progress_dashboard", false]], "get_progress_summary() (in module api.v1.progress_tracking)": [[0, "api.v1.progress_tracking.get_progress_summary", false]], "get_security_areas() (in module api.v1.security_career_framework)": [[0, "api.v1.security_career_framework.get_security_areas", false]], "get_security_career_analytics() (in module api.v1.security_career_framework)": [[0, "api.v1.security_career_framework.get_security_career_analytics", false]], "get_security_career_path() (in module api.v1.security_career_framework)": [[0, "api.v1.security_career_framework.get_security_career_path", false]], "get_security_career_paths() (in module api.v1.security_career_framework)": [[0, "api.v1.security_career_framework.get_security_career_paths", false]], "get_security_job_type() (in module api.v1.security_career_framework)": [[0, "api.v1.security_career_framework.get_security_job_type", false]], "get_security_job_types() (in module api.v1.security_career_framework)": [[0, "api.v1.security_career_framework.get_security_job_types", false]], "get_security_skill_matrix() (in module api.v1.security_career_framework)": [[0, "api.v1.security_career_framework.get_security_skill_matrix", false]], "get_seniority_levels() (in module api.v1.security_career_framework)": [[0, "api.v1.security_career_framework.get_seniority_levels", false]], "get_sso_metadata() (in module api.v1.integration_hub)": [[0, "api.v1.integration_hub.get_sso_metadata", false]], "get_study_insights() (in module api.v1.progress_tracking)": [[0, "api.v1.progress_tracking.get_study_insights", false]], "get_study_sessions() (in module api.v1.progress_tracking)": [[0, "api.v1.progress_tracking.get_study_sessions", false]], "get_transition_plan() (in module api.v1.career_transition)": [[0, "api.v1.career_transition.get_transition_plan", false]], "get_transition_summary() (in module api.v1.career_transition)": [[0, "api.v1.career_transition.get_transition_summary", false]], "get_user() (in module api.v1.enterprise)": [[0, "api.v1.enterprise.get_user", false]], "get_user_transition_plans() (in module api.v1.career_transition)": [[0, "api.v1.career_transition.get_user_transition_plans", false]], "health_check() (in module api.v1.enterprise)": [[0, "api.v1.enterprise.health_check", false]], "integration_hub_health() (in module api.v1.integration_hub)": [[0, "api.v1.integration_hub.integration_hub_health", false]], "list_integrations() (in module api.v1.integration_hub)": [[0, "api.v1.integration_hub.list_integrations", false]], "list_organization_departments() (in module api.v1.enterprise)": [[0, "api.v1.enterprise.list_organization_departments", false]], "list_organization_users() (in module api.v1.enterprise)": [[0, "api.v1.enterprise.list_organization_users", false]], "list_organizations() (in module api.v1.enterprise)": [[0, "api.v1.enterprise.list_organizations", false]], "mobile_health_check() (in module api.v1.mobile)": [[0, "api.v1.mobile.mobile_health_check", false]], "module": [[0, "module-api.v1.career_transition", false], [0, "module-api.v1.enterprise", false], [0, "module-api.v1.integration_hub", false], [0, "module-api.v1.mobile", false], [0, "module-api.v1.progress_tracking", false], [0, "module-api.v1.security_career_framework", false]], "quick_start_study_session() (in module api.v1.mobile)": [[0, "api.v1.mobile.quick_start_study_session", false]], "record_practice_test_result() (in module api.v1.progress_tracking)": [[0, "api.v1.progress_tracking.record_practice_test_result", false]], "register_mobile_device() (in module api.v1.mobile)": [[0, "api.v1.mobile.register_mobile_device", false]], "require_org_admin() (in module api.v1.enterprise)": [[0, "api.v1.enterprise.require_org_admin", false]], "require_super_admin() (in module api.v1.enterprise)": [[0, "api.v1.enterprise.require_super_admin", false]], "schedule_smart_notifications() (in module api.v1.mobile)": [[0, "api.v1.mobile.schedule_smart_notifications", false]], "send_push_notification() (in module api.v1.mobile)": [[0, "api.v1.mobile.send_push_notification", false]], "start_study_session() (in module api.v1.progress_tracking)": [[0, "api.v1.progress_tracking.start_study_session", false]], "sync_hr_data() (in module api.v1.integration_hub)": [[0, "api.v1.integration_hub.sync_hr_data", false]], "sync_ldap_users() (in module api.v1.integration_hub)": [[0, "api.v1.integration_hub.sync_ldap_users", false]], "sync_lms_data() (in module api.v1.integration_hub)": [[0, "api.v1.integration_hub.sync_lms_data", false]], "sync_mobile_data() (in module api.v1.mobile)": [[0, "api.v1.mobile.sync_mobile_data", false]], "test_integration_connection() (in module api.v1.integration_hub)": [[0, "api.v1.integration_hub.test_integration_connection", false]], "track_mobile_analytics() (in module api.v1.mobile)": [[0, "api.v1.mobile.track_mobile_analytics", false]], "update_goal_progress() (in module api.v1.progress_tracking)": [[0, "api.v1.progress_tracking.update_goal_progress", false]], "update_learning_goal() (in module api.v1.progress_tracking)": [[0, "api.v1.progress_tracking.update_learning_goal", false]], "update_organization() (in module api.v1.enterprise)": [[0, "api.v1.enterprise.update_organization", false]], "update_security_job_type() (in module api.v1.security_career_framework)": [[0, "api.v1.security_career_framework.update_security_job_type", false]], "update_step_progress() (in module api.v1.career_transition)": [[0, "api.v1.career_transition.update_step_progress", false]], "update_transition_plan() (in module api.v1.career_transition)": [[0, "api.v1.career_transition.update_transition_plan", false]], "update_user() (in module api.v1.enterprise)": [[0, "api.v1.enterprise.update_user", false]]}, "objects": {"api.v1": [[0, 0, 0, "-", "career_transition"], [0, 0, 0, "-", "enterprise"], [0, 0, 0, "-", "integration_hub"], [0, 0, 0, "-", "mobile"], [0, 0, 0, "-", "progress_tracking"], [0, 0, 0, "-", "security_career_framework"]], "api.v1.career_transition": [[0, 1, 1, "", "create_transition_plan"], [0, 1, 1, "", "delete_transition_plan"], [0, 1, 1, "", "find_career_paths"], [0, 1, 1, "", "generate_career_summary_pdf"], [0, 1, 1, "", "generate_path_comparison_pdf"], [0, 1, 1, "", "generate_transition_plan_pdf"], [0, 1, 1, "", "get_career_roles"], [0, 1, 1, "", "get_current_user_id"], [0, 1, 1, "", "get_transition_plan"], [0, 1, 1, "", "get_transition_summary"], [0, 1, 1, "", "get_user_transition_plans"], [0, 1, 1, "", "update_step_progress"], [0, 1, 1, "", "update_transition_plan"]], "api.v1.enterprise": [[0, 1, 1, "", "assign_license"], [0, 1, 1, "", "create_department"], [0, 1, 1, "", "create_organization"], [0, 1, 1, "", "create_user"], [0, 1, 1, "", "delete_organization"], [0, 1, 1, "", "get_current_admin_user"], [0, 1, 1, "", "get_license_usage"], [0, 1, 1, "", "get_organization"], [0, 1, 1, "", "get_organization_analytics"], [0, 1, 1, "", "get_organization_dashboard"], [0, 1, 1, "", "get_user"], [0, 1, 1, "", "health_check"], [0, 1, 1, "", "list_organization_departments"], [0, 1, 1, "", "list_organization_users"], [0, 1, 1, "", "list_organizations"], [0, 1, 1, "", "require_org_admin"], [0, 1, 1, "", "require_super_admin"], [0, 1, 1, "", "update_organization"], [0, 1, 1, "", "update_user"]], "api.v1.integration_hub": [[0, 1, 1, "", "authenticate_sso_user"], [0, 1, 1, "", "configure_hr_integration"], [0, 1, 1, "", "configure_ldap_integration"], [0, 1, 1, "", "configure_lms_integration"], [0, 1, 1, "", "configure_sso_integration"], [0, 1, 1, "", "configure_webhook_integration"], [0, 1, 1, "", "get_current_user"], [0, 1, 1, "", "get_integration_status"], [0, 1, 1, "", "get_organization_id"], [0, 1, 1, "", "get_sso_metadata"], [0, 1, 1, "", "integration_hub_health"], [0, 1, 1, "", "list_integrations"], [0, 1, 1, "", "sync_hr_data"], [0, 1, 1, "", "sync_ldap_users"], [0, 1, 1, "", "sync_lms_data"], [0, 1, 1, "", "test_integration_connection"]], "api.v1.mobile": [[0, 1, 1, "", "authenticate_mobile_user"], [0, 1, 1, "", "get_current_mobile_user"], [0, 1, 1, "", "get_device_id"], [0, 1, 1, "", "get_mobile_app_config"], [0, 1, 1, "", "get_mobile_dashboard"], [0, 1, 1, "", "get_mobile_practice_test"], [0, 1, 1, "", "get_offline_ai_recommendations"], [0, 1, 1, "", "get_offline_data_package"], [0, 1, 1, "", "mobile_health_check"], [0, 1, 1, "", "quick_start_study_session"], [0, 1, 1, "", "register_mobile_device"], [0, 1, 1, "", "schedule_smart_notifications"], [0, 1, 1, "", "send_push_notification"], [0, 1, 1, "", "sync_mobile_data"], [0, 1, 1, "", "track_mobile_analytics"]], "api.v1.progress_tracking": [[0, 1, 1, "", "create_learning_goal"], [0, 1, 1, "", "delete_learning_goal"], [0, 1, 1, "", "delete_study_session"], [0, 1, 1, "", "end_study_session"], [0, 1, 1, "", "get_achievements"], [0, 1, 1, "", "get_current_user_id"], [0, 1, 1, "", "get_learning_analytics"], [0, 1, 1, "", "get_learning_goals"], [0, 1, 1, "", "get_practice_test_results"], [0, 1, 1, "", "get_progress_dashboard"], [0, 1, 1, "", "get_progress_summary"], [0, 1, 1, "", "get_study_insights"], [0, 1, 1, "", "get_study_sessions"], [0, 1, 1, "", "record_practice_test_result"], [0, 1, 1, "", "start_study_session"], [0, 1, 1, "", "update_goal_progress"], [0, 1, 1, "", "update_learning_goal"]], "api.v1.security_career_framework": [[0, 1, 1, "", "create_security_job_type"], [0, 1, 1, "", "get_career_recommendations"], [0, 1, 1, "", "get_job_families_by_area"], [0, 1, 1, "", "get_security_areas"], [0, 1, 1, "", "get_security_career_analytics"], [0, 1, 1, "", "get_security_career_path"], [0, 1, 1, "", "get_security_career_paths"], [0, 1, 1, "", "get_security_job_type"], [0, 1, 1, "", "get_security_job_types"], [0, 1, 1, "", "get_security_skill_matrix"], [0, 1, 1, "", "get_seniority_levels"], [0, 1, 1, "", "update_security_job_type"]]}, "objnames": {"0": ["py", "module", "Python module"], "1": ["py", "function", "Python function"]}, "objtypes": {"0": "py:module", "1": "py:function"}, "terms": {"": [0, 1, 3], "0": 0, "1": [0, 3], "10": [0, 2, 3], "100": [0, 1], "1000": 0, "10000": 0, "13": 2, "140": 1, "150": 0, "1640995200": 0, "20": 0, "200": [0, 1], "201": 0, "25": 1, "3": [2, 3], "30": [0, 2], "300": 1, "4": 3, "400": 0, "401": 0, "403": 0, "404": 0, "422": 0, "429": 0, "50": [0, 1], "500": [0, 1], "5432": 2, "587": 2, "6": [2, 3], "60": 3, "6379": 2, "8": [0, 1], "8000": [0, 2, 3], "8501": [2, 3], "90": 0, "999": 0, "For": [2, 3], "Not": 0, "On": 2, "Or": 2, "The": [0, 2, 3], "_": 0, "access": [0, 2, 3], "access_token": [0, 3], "access_token_expire_minut": 2, "achiev": [0, 1, 3], "activ": [0, 2], "admin": 0, "administr": 0, "adopt": 1, "adp": 0, "advanc": [2, 3], "after": [2, 3], "ai": 1, "alemb": 2, "algorithm": 2, "all": [0, 1, 2, 3], "alreadi": 2, "an": [0, 1, 3], "analysi": [0, 1, 3], "analyst": 3, "analyt": [0, 1], "analytics_request": 0, "analyz": 3, "android": 0, "anonym": 0, "anoth": 2, "anthrop": 2, "anthropic_api_kei": 2, "api": 2, "api_kei": 0, "api_v1_str": 2, "app": [0, 2], "app_vers": 0, "applic": [0, 2], "ar": [0, 2], "architectur": [1, 3], "area": [0, 1], "assess": [0, 3], "assessment_data": 3, "assign": 0, "assign_licens": 0, "assist": 1, "async": 0, "auth": [0, 3], "auth_data": 3, "auth_request": 0, "authent": 1, "authenticate_mobile_us": 0, "authenticate_sso_us": 0, "author": [0, 3], "automat": [0, 2], "avail": 0, "awar": 0, "backend": [1, 2], "background": [0, 2], "background_task": 0, "backgroundtask": 0, "bad": 0, "bamboohr": 0, "base": [1, 3], "base_url": 0, "basic": [2, 3], "bearer": [0, 3], "befor": 2, "behavior": 0, "benefit": 3, "bin": 2, "blackboard": 0, "bool": 0, "brows": 3, "budget": 0, "built": [0, 1], "cach": 2, "calcul": 1, "can": 2, "canva": 0, "capabl": [0, 1], "career": 1, "career_path": 3, "career_transit": 0, "careerpathfindingrequest": 0, "careerrecommendationrequest": 0, "careertransitionplancr": 0, "careertransitionplanupd": 0, "case": 1, "categori": 0, "cd": 2, "certif": [0, 1], "certification_id": [0, 3], "certpathfind": [0, 2, 3], "certpathfindercli": 0, "chang": [0, 2], "check": [0, 2, 3], "choos": 3, "client": 1, "clone": 2, "cloud_secur": 3, "code": 1, "com": [0, 2, 3], "common": [1, 2], "compar": [0, 3], "comparison_curr": 0, "complet": [0, 1, 3], "compos": 2, "comprehens": [0, 1, 3], "confidence_level": 3, "configur": [0, 1], "configure_hr_integr": 0, "configure_ldap_integr": 0, "configure_lms_integr": 0, "configure_sso_integr": 0, "configure_webhook_integr": 0, "conflict": 2, "connect": [0, 2], "constraint": 0, "consult": 2, "container": [1, 2], "context": 0, "core": 1, "correct": 2, "cost": 1, "cost_analysi": [0, 3], "cost_calcul": 0, "cost_data": 3, "cover": 1, "cp": 2, "creat": [0, 2, 3], "create_depart": 0, "create_learning_go": 0, "create_organ": 0, "create_security_job_typ": 0, "create_transition_plan": 0, "create_us": 0, "createdb": 2, "creation": 3, "credenti": 2, "curl": 2, "current": [0, 3], "current_rol": 3, "current_skil": 3, "current_us": 0, "custom": [2, 3], "cybersecur": [1, 3], "d": 2, "dashboard": [1, 3], "data": [0, 1, 3], "databas": [1, 2], "database_url": 2, "days_back": 0, "db": [0, 2], "deactiv": 0, "defin": 3, "delet": 0, "delete_learning_go": 0, "delete_organ": 0, "delete_study_sess": 0, "delete_transition_plan": 0, "demand": [1, 3], "demand_level": 0, "demandlevelenum": 0, "depart": 0, "department_data": 0, "department_id": 0, "departmentcr": 0, "depend": [0, 2], "deploy": [0, 1, 2], "design": 3, "desir": 3, "detail": [0, 2, 3], "develop": [1, 2, 3], "devic": 0, "device_id": 0, "deviceregistrationrequest": 0, "difficulti": 0, "direct": 3, "directori": 0, "doc": [0, 2, 3], "docker": 1, "document": [2, 3], "doe": 3, "domain": 0, "driven": 1, "duration_minut": 0, "earned_onli": 0, "easi": 1, "edit": 2, "ellipsi": 0, "email": [2, 3], "end": [0, 3], "end_dat": 0, "end_study_sess": 0, "endpoint": [0, 1], "enhanc": [1, 3], "ensur": [0, 2], "enterpris": [1, 3], "enterprise_guid": 3, "enterpriseusercr": 0, "enterpriseuserupd": 0, "entiti": 0, "env": 2, "environ": 1, "error": [1, 2], "event": 0, "everyth": 2, "exampl": [0, 2, 3], "exceed": 0, "experience_year": 3, "explor": [2, 3], "extern": 2, "f": [0, 3], "fair": 0, "fals": 0, "famili": 0, "familiar": 3, "fastapi": [0, 2], "fastest": 2, "featur": 0, "file": 2, "filter": 0, "find": 0, "find_career_path": 0, "first": 1, "flag": 0, "float": 0, "follow": 2, "forbidden": 0, "forkrul": 2, "format": 1, "found": 0, "framework": 1, "from": [0, 1, 3], "frontend": 2, "full_nam": 3, "function": [0, 1], "gap": 3, "gener": 0, "generate_career_summary_pdf": 0, "generate_path_comparison_pdf": 0, "generate_transition_plan_pdf": 0, "get": [0, 2, 3], "get_achiev": 0, "get_career_recommend": 0, "get_career_rol": 0, "get_current_admin_us": 0, "get_current_mobile_us": 0, "get_current_us": 0, "get_current_user_id": 0, "get_db": 0, "get_device_id": 0, "get_integration_statu": 0, "get_job_families_by_area": 0, "get_learning_analyt": 0, "get_learning_go": 0, "get_license_usag": 0, "get_mobile_app_config": 0, "get_mobile_dashboard": 0, "get_mobile_practice_test": 0, "get_offline_ai_recommend": 0, "get_offline_data_packag": 0, "get_organ": 0, "get_organization_analyt": 0, "get_organization_dashboard": 0, "get_organization_id": 0, "get_practice_test_result": 0, "get_progress_dashboard": 0, "get_progress_summari": 0, "get_security_area": 0, "get_security_career_analyt": 0, "get_security_career_path": 0, "get_security_job_typ": 0, "get_security_skill_matrix": 0, "get_seniority_level": 0, "get_sso_metadata": 0, "get_study_insight": 0, "get_study_sess": 0, "get_transition_plan": 0, "get_transition_summari": 0, "get_us": 0, "get_user_transition_plan": 0, "git": 2, "github": [0, 2], "gmail": 2, "go": [0, 3], "goal": [0, 3], "goal_id": 0, "goal_typ": 0, "goalprogressupd": 0, "grade": 1, "guid": 1, "guidanc": [0, 1, 3], "have": 2, "head": 2, "header": [0, 3], "health": [0, 2], "health_check": 0, "help": [2, 3], "here": 2, "high": 3, "hour": 0, "hr": [0, 1], "hr_config": 0, "hrconfigrequest": 0, "hs256": 2, "http": [0, 2, 3], "hub": 1, "i": [0, 1, 2, 3], "id": [0, 3], "identifi": 3, "implement": 1, "import": [0, 2, 3], "incid": 3, "includ": 0, "include_cost_analysi": 0, "include_cost_project": 0, "include_detailed_step": 0, "include_recommend": 0, "include_transition_plan": 0, "index": 1, "individu": 1, "industri": [0, 3], "inform": 0, "input": [0, 3], "insight": 0, "instal": [0, 1, 3], "instruct": 2, "insuffici": 0, "int": 0, "integr": 1, "integration_hub": 0, "integration_hub_health": 0, "integration_id": 0, "integration_typ": 0, "intellig": [0, 1, 3], "interact": [1, 2, 3], "interfac": [1, 2], "intermedi": 3, "intern": 0, "intuit": 3, "invalid": 0, "io": 0, "is_act": 0, "issu": 2, "item": 0, "its": 3, "javascript": 0, "jerimi": [0, 1], "job": [0, 1, 3], "job_famili": 0, "job_opportun": 3, "job_type_data": 0, "job_type_id": 0, "john": 3, "json": [0, 3], "jwt": 0, "kei": [0, 2], "ldap": [0, 1], "ldap_config": 0, "ldapconfigrequest": 0, "learn": [0, 1, 3], "learninggoalcr": 0, "learninggoalupd": 0, "level": 0, "librari": 1, "licens": 0, "license_data": 0, "licenseassign": 0, "limit": 1, "list": 0, "list_integr": 0, "list_organ": 0, "list_organization_depart": 0, "list_organization_us": 0, "lm": [0, 1], "lms_config": 0, "lmsconfigrequest": 0, "localhost": [0, 2, 3], "locat": [0, 3], "log": 2, "login": [0, 3], "m": 2, "main": 2, "manag": [0, 1], "mani": 0, "manual": 1, "market": [1, 3], "matric": 0, "matrix": 0, "max_experi": 0, "max_salari": 0, "messag": [0, 2], "metadata": 0, "migrat": 2, "min_experi": 0, "min_salari": 0, "mix": 0, "mobil": [1, 3], "mobile_health_check": 0, "mobileanalyticsrequest": 0, "mobileauthrequest": 0, "model": 1, "modern": 1, "modul": 1, "monitor": [0, 3], "monthli": 0, "moodl": 0, "more": 3, "most": [0, 1], "multi": [0, 1], "multipl": 0, "nativ": 1, "network": 3, "new": 0, "next": 1, "none": 0, "notif": 0, "notification_request": 0, "now": 3, "npm": 0, "object": 0, "offer": 1, "offici": 0, "offlin": [0, 1], "offline_request": 0, "offlinedatarequest": 0, "ok": 0, "openai": 2, "openai_api_kei": 2, "openapi": 0, "oper": 0, "opportun": 3, "optim": 0, "option": [0, 2, 3], "org_id": 0, "organ": [0, 1, 3], "organiz": [0, 3], "organization_data": 0, "organization_id": 0, "organization_typ": 0, "organizationcr": 0, "organizationupd": 0, "orm": 0, "other": 0, "out": [2, 3], "overview": 1, "packag": 0, "page": [0, 1], "page_s": 0, "password": [0, 2, 3], "path": [0, 1, 3], "path_id": 0, "pathfind": 0, "paul": [0, 1], "pdf": 0, "per": 0, "period": 0, "period_typ": 0, "periodtyp": 0, "permiss": 0, "person": [0, 1, 3], "pip": [0, 2], "plan": 0, "plan_id": 0, "planned_duration_minut": 3, "platform": [0, 3], "port": 2, "posit": 3, "post": [0, 3], "postgresql": 2, "power": [0, 1, 3], "practic": 0, "practicetestresultcr": 0, "prerequisit": 1, "primari": 2, "privileg": 0, "product": [0, 1, 3], "profession": 1, "profil": [0, 3], "progress": [1, 3], "progress_track": 0, "project_nam": 2, "properli": 2, "provid": [0, 1, 3], "push": 0, "pushnotificationrequest": 0, "py": 2, "pydanticundefin": 0, "pytest": 2, "python": [0, 2], "queri": 0, "question_count": 0, "quick": 0, "quick_start_study_sess": 0, "quickli": 3, "r": 2, "rate": 1, "ratelimit": 0, "re": 3, "read": [2, 3], "real": [0, 1, 3], "receiv": 3, "recommend": [0, 1, 3], "recommendations_data": 3, "record": 0, "record_practice_test_result": 0, "redi": 2, "redis_url": 2, "redoc": 0, "refer": [2, 3], "regist": 0, "register_mobile_devic": 0, "registration_request": 0, "remain": 0, "remote_friendli": 0, "replit": 2, "report": 0, "repositori": 2, "request": [0, 3], "requir": [0, 2], "require_org_admin": 0, "require_super_admin": 0, "research": 1, "reset": 0, "resourc": 0, "respons": [1, 3], "rest": 0, "result": 0, "review": [2, 3], "roi": [0, 1, 3], "role": [0, 3], "run": 2, "run_api": 2, "saml": 0, "saml_respons": 0, "schedul": [0, 3], "schedule_smart_notif": 0, "schema": 0, "script": 2, "sdk": 1, "search": [0, 1], "secret": [0, 2], "secret_kei": 2, "secur": [1, 2, 3], "security_area": 0, "security_career_framework": 0, "securityareaenum": 0, "securityjobtypecr": 0, "securityjobtypeupd": 0, "see": 3, "select": 3, "send": 0, "send_push_notif": 0, "senior": 0, "seniority_level": 0, "senioritylevelenum": 0, "server": [0, 2], "servic": [0, 2], "session": [0, 1, 3], "session_data": 3, "session_id": 0, "session_typ": 0, "set": [2, 3], "setup": 2, "size": 0, "skill": [0, 1, 3], "skill_assess": 3, "skill_id": 3, "skill_level": 3, "skill_recommend": 3, "skip": 0, "smtp": 2, "smtp_host": 2, "smtp_password": 2, "smtp_port": 2, "smtp_tl": 2, "smtp_user": 2, "soft": 0, "sourc": [0, 2], "specif": 0, "sqlalchemi": 0, "sqlite": 2, "sso": [0, 1], "sso_config": 0, "ssoconfigrequest": 0, "stai": 3, "standard": 0, "start": 0, "start_dat": 0, "start_study_sess": 0, "state": [0, 3], "statist": 0, "statu": 0, "step": [0, 1], "step_id": 0, "stepprogressupd": 0, "stop": 2, "str": 0, "streamlit": [2, 3], "studi": 1, "study_hours_per_week": 3, "study_sess": 0, "study_typ": 3, "studysessionend": 0, "studysessionstart": 0, "submit": 3, "subscription_ti": 0, "subsequ": 3, "success": 2, "successfulli": 0, "suggest": 3, "summari": 0, "super": 0, "support": [0, 1, 2], "swagger": 0, "sync_hr_data": 0, "sync_ldap_us": 0, "sync_lms_data": 0, "sync_mobile_data": 0, "sync_request": 0, "synchron": 0, "syncrequest": 0, "system": [0, 1, 2], "target": 3, "target_completion_month": 3, "target_domain": 3, "task": 2, "taxonomi": [1, 3], "technic": 3, "technologi": 1, "tenant": [0, 1], "termin": 2, "test": [0, 2], "test_integration_connect": 0, "test_typ": 0, "thi": [0, 2, 3], "time": 0, "timefram": 3, "timelin": 0, "timer": 1, "titl": [0, 1, 3], "token": [0, 3], "too": 0, "total": 0, "toward": 3, "track": [1, 3], "track_mobile_analyt": 0, "trend": 3, "troubleshoot": 1, "true": [0, 2], "tutori": 1, "txt": 2, "type": 0, "typescript": 0, "ui": 0, "unauthor": 0, "under": 0, "understand": 3, "unit": [0, 3], "unprocess": 0, "up": [2, 3], "updat": [0, 3], "update_data": 0, "update_goal_progress": 0, "update_learning_go": 0, "update_organ": 0, "update_security_job_typ": 0, "update_step_progress": 0, "update_transition_plan": 0, "update_us": 0, "upgrad": 2, "url": [1, 3], "us": [0, 1, 2, 3], "usag": [0, 1, 2], "usd": 0, "user": [0, 1, 2, 3], "user_data": [0, 3], "user_guid": [1, 2, 3], "user_id": [0, 3], "usernam": [0, 3], "v1": [0, 2, 3], "valid": 0, "validation_error": 0, "valu": 3, "venv": 2, "verif": 1, "verifi": 2, "via": 0, "virtual": 2, "visit": 2, "wai": [2, 3], "web": 1, "webhook": 1, "webhook_config": 0, "webhook_data": 0, "webhookconfigrequest": 0, "welcom": 1, "window": 2, "work": 2, "workdai": 0, "workflow": 1, "world": 1, "x": 0, "x_device_id": 0, "x_organization_id": 0, "you": [2, 3], "your": [0, 2, 3]}, "titles": ["API Reference", "CertPathFinder Documentation", "Installation Guide", "Quick Start Guide"], "titleterms": {"ai": [0, 3], "api": [0, 1, 3], "assist": [0, 3], "authent": [0, 3], "base": 0, "calcul": [0, 3], "career": [0, 3], "certif": 3, "certpathfind": 1, "client": 0, "code": 0, "common": [0, 3], "configur": 2, "core": 3, "cost": [0, 3], "dashboard": 0, "docker": 2, "document": [0, 1], "enhanc": 0, "enterpris": 0, "environ": 2, "error": 0, "featur": [1, 3], "first": 3, "format": 0, "framework": [0, 3], "get": 1, "guid": [2, 3], "hub": 0, "indic": 1, "instal": 2, "integr": [0, 3], "interact": 0, "interfac": 3, "kei": [1, 3], "librari": 0, "limit": 0, "link": 1, "manual": 2, "mobil": 0, "modul": 0, "next": [2, 3], "overview": 3, "pagin": 0, "pattern": 3, "plan": 3, "platform": 1, "prerequisit": 2, "progress": 0, "quick": [1, 2, 3], "rate": 0, "refer": [0, 1], "respons": 0, "sdk": 0, "section": 1, "secur": 0, "start": [1, 2, 3], "statist": 1, "step": [2, 3], "studi": [0, 3], "success": 0, "tabl": 1, "taxonomi": 0, "timer": [0, 3], "track": 0, "transit": [0, 3], "troubleshoot": 2, "url": 0, "usag": 3, "verif": 2, "web": 3, "webhook": 0, "workflow": 3}})