

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Index &mdash; CertPathFinder 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=c14262df" />

  
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="_static/documentation_options.js?v=8d563738"></script>
      <script src="_static/doctools.js?v=9bcbadda"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="#" />
    <link rel="search" title="Search" href="search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="index.html" class="icon icon-home">
            CertPathFinder
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="quickstart.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="api/index.html">API Reference</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">CertPathFinder</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Index</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             

<h1 id="index">Index</h1>

<div class="genindex-jumpbox">
 <a href="#A"><strong>A</strong></a>
 | <a href="#C"><strong>C</strong></a>
 | <a href="#D"><strong>D</strong></a>
 | <a href="#E"><strong>E</strong></a>
 | <a href="#F"><strong>F</strong></a>
 | <a href="#G"><strong>G</strong></a>
 | <a href="#H"><strong>H</strong></a>
 | <a href="#I"><strong>I</strong></a>
 | <a href="#L"><strong>L</strong></a>
 | <a href="#M"><strong>M</strong></a>
 | <a href="#Q"><strong>Q</strong></a>
 | <a href="#R"><strong>R</strong></a>
 | <a href="#S"><strong>S</strong></a>
 | <a href="#T"><strong>T</strong></a>
 | <a href="#U"><strong>U</strong></a>
 
</div>
<h2 id="A">A</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    api.v1.career_transition

      <ul>
        <li><a href="api/index.html#module-api.v1.career_transition">module</a>
</li>
      </ul></li>
      <li>
    api.v1.enterprise

      <ul>
        <li><a href="api/index.html#module-api.v1.enterprise">module</a>
</li>
      </ul></li>
      <li>
    api.v1.integration_hub

      <ul>
        <li><a href="api/index.html#module-api.v1.integration_hub">module</a>
</li>
      </ul></li>
      <li>
    api.v1.mobile

      <ul>
        <li><a href="api/index.html#module-api.v1.mobile">module</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li>
    api.v1.progress_tracking

      <ul>
        <li><a href="api/index.html#module-api.v1.progress_tracking">module</a>
</li>
      </ul></li>
      <li>
    api.v1.security_career_framework

      <ul>
        <li><a href="api/index.html#module-api.v1.security_career_framework">module</a>
</li>
      </ul></li>
      <li><a href="api/index.html#api.v1.enterprise.assign_license">assign_license() (in module api.v1.enterprise)</a>
</li>
      <li><a href="api/index.html#api.v1.mobile.authenticate_mobile_user">authenticate_mobile_user() (in module api.v1.mobile)</a>
</li>
      <li><a href="api/index.html#api.v1.integration_hub.authenticate_sso_user">authenticate_sso_user() (in module api.v1.integration_hub)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="C">C</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/index.html#api.v1.integration_hub.configure_hr_integration">configure_hr_integration() (in module api.v1.integration_hub)</a>
</li>
      <li><a href="api/index.html#api.v1.integration_hub.configure_ldap_integration">configure_ldap_integration() (in module api.v1.integration_hub)</a>
</li>
      <li><a href="api/index.html#api.v1.integration_hub.configure_lms_integration">configure_lms_integration() (in module api.v1.integration_hub)</a>
</li>
      <li><a href="api/index.html#api.v1.integration_hub.configure_sso_integration">configure_sso_integration() (in module api.v1.integration_hub)</a>
</li>
      <li><a href="api/index.html#api.v1.integration_hub.configure_webhook_integration">configure_webhook_integration() (in module api.v1.integration_hub)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/index.html#api.v1.enterprise.create_department">create_department() (in module api.v1.enterprise)</a>
</li>
      <li><a href="api/index.html#api.v1.progress_tracking.create_learning_goal">create_learning_goal() (in module api.v1.progress_tracking)</a>
</li>
      <li><a href="api/index.html#api.v1.enterprise.create_organization">create_organization() (in module api.v1.enterprise)</a>
</li>
      <li><a href="api/index.html#api.v1.security_career_framework.create_security_job_type">create_security_job_type() (in module api.v1.security_career_framework)</a>
</li>
      <li><a href="api/index.html#api.v1.career_transition.create_transition_plan">create_transition_plan() (in module api.v1.career_transition)</a>
</li>
      <li><a href="api/index.html#api.v1.enterprise.create_user">create_user() (in module api.v1.enterprise)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="D">D</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/index.html#api.v1.progress_tracking.delete_learning_goal">delete_learning_goal() (in module api.v1.progress_tracking)</a>
</li>
      <li><a href="api/index.html#api.v1.enterprise.delete_organization">delete_organization() (in module api.v1.enterprise)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/index.html#api.v1.progress_tracking.delete_study_session">delete_study_session() (in module api.v1.progress_tracking)</a>
</li>
      <li><a href="api/index.html#api.v1.career_transition.delete_transition_plan">delete_transition_plan() (in module api.v1.career_transition)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="E">E</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/index.html#api.v1.progress_tracking.end_study_session">end_study_session() (in module api.v1.progress_tracking)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="F">F</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/index.html#api.v1.career_transition.find_career_paths">find_career_paths() (in module api.v1.career_transition)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="G">G</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/index.html#api.v1.career_transition.generate_career_summary_pdf">generate_career_summary_pdf() (in module api.v1.career_transition)</a>
</li>
      <li><a href="api/index.html#api.v1.career_transition.generate_path_comparison_pdf">generate_path_comparison_pdf() (in module api.v1.career_transition)</a>
</li>
      <li><a href="api/index.html#api.v1.career_transition.generate_transition_plan_pdf">generate_transition_plan_pdf() (in module api.v1.career_transition)</a>
</li>
      <li><a href="api/index.html#api.v1.progress_tracking.get_achievements">get_achievements() (in module api.v1.progress_tracking)</a>
</li>
      <li><a href="api/index.html#api.v1.security_career_framework.get_career_recommendations">get_career_recommendations() (in module api.v1.security_career_framework)</a>
</li>
      <li><a href="api/index.html#api.v1.career_transition.get_career_roles">get_career_roles() (in module api.v1.career_transition)</a>
</li>
      <li><a href="api/index.html#api.v1.enterprise.get_current_admin_user">get_current_admin_user() (in module api.v1.enterprise)</a>
</li>
      <li><a href="api/index.html#api.v1.mobile.get_current_mobile_user">get_current_mobile_user() (in module api.v1.mobile)</a>
</li>
      <li><a href="api/index.html#api.v1.integration_hub.get_current_user">get_current_user() (in module api.v1.integration_hub)</a>
</li>
      <li><a href="api/index.html#api.v1.career_transition.get_current_user_id">get_current_user_id() (in module api.v1.career_transition)</a>

      <ul>
        <li><a href="api/index.html#api.v1.progress_tracking.get_current_user_id">(in module api.v1.progress_tracking)</a>
</li>
      </ul></li>
      <li><a href="api/index.html#api.v1.mobile.get_device_id">get_device_id() (in module api.v1.mobile)</a>
</li>
      <li><a href="api/index.html#api.v1.integration_hub.get_integration_status">get_integration_status() (in module api.v1.integration_hub)</a>
</li>
      <li><a href="api/index.html#api.v1.security_career_framework.get_job_families_by_area">get_job_families_by_area() (in module api.v1.security_career_framework)</a>
</li>
      <li><a href="api/index.html#api.v1.progress_tracking.get_learning_analytics">get_learning_analytics() (in module api.v1.progress_tracking)</a>
</li>
      <li><a href="api/index.html#api.v1.progress_tracking.get_learning_goals">get_learning_goals() (in module api.v1.progress_tracking)</a>
</li>
      <li><a href="api/index.html#api.v1.enterprise.get_license_usage">get_license_usage() (in module api.v1.enterprise)</a>
</li>
      <li><a href="api/index.html#api.v1.mobile.get_mobile_app_config">get_mobile_app_config() (in module api.v1.mobile)</a>
</li>
      <li><a href="api/index.html#api.v1.mobile.get_mobile_dashboard">get_mobile_dashboard() (in module api.v1.mobile)</a>
</li>
      <li><a href="api/index.html#api.v1.mobile.get_mobile_practice_test">get_mobile_practice_test() (in module api.v1.mobile)</a>
</li>
      <li><a href="api/index.html#api.v1.mobile.get_offline_ai_recommendations">get_offline_ai_recommendations() (in module api.v1.mobile)</a>
</li>
      <li><a href="api/index.html#api.v1.mobile.get_offline_data_package">get_offline_data_package() (in module api.v1.mobile)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/index.html#api.v1.enterprise.get_organization">get_organization() (in module api.v1.enterprise)</a>
</li>
      <li><a href="api/index.html#api.v1.enterprise.get_organization_analytics">get_organization_analytics() (in module api.v1.enterprise)</a>
</li>
      <li><a href="api/index.html#api.v1.enterprise.get_organization_dashboard">get_organization_dashboard() (in module api.v1.enterprise)</a>
</li>
      <li><a href="api/index.html#api.v1.integration_hub.get_organization_id">get_organization_id() (in module api.v1.integration_hub)</a>
</li>
      <li><a href="api/index.html#api.v1.progress_tracking.get_practice_test_results">get_practice_test_results() (in module api.v1.progress_tracking)</a>
</li>
      <li><a href="api/index.html#api.v1.progress_tracking.get_progress_dashboard">get_progress_dashboard() (in module api.v1.progress_tracking)</a>
</li>
      <li><a href="api/index.html#api.v1.progress_tracking.get_progress_summary">get_progress_summary() (in module api.v1.progress_tracking)</a>
</li>
      <li><a href="api/index.html#api.v1.security_career_framework.get_security_areas">get_security_areas() (in module api.v1.security_career_framework)</a>
</li>
      <li><a href="api/index.html#api.v1.security_career_framework.get_security_career_analytics">get_security_career_analytics() (in module api.v1.security_career_framework)</a>
</li>
      <li><a href="api/index.html#api.v1.security_career_framework.get_security_career_path">get_security_career_path() (in module api.v1.security_career_framework)</a>
</li>
      <li><a href="api/index.html#api.v1.security_career_framework.get_security_career_paths">get_security_career_paths() (in module api.v1.security_career_framework)</a>
</li>
      <li><a href="api/index.html#api.v1.security_career_framework.get_security_job_type">get_security_job_type() (in module api.v1.security_career_framework)</a>
</li>
      <li><a href="api/index.html#api.v1.security_career_framework.get_security_job_types">get_security_job_types() (in module api.v1.security_career_framework)</a>
</li>
      <li><a href="api/index.html#api.v1.security_career_framework.get_security_skill_matrix">get_security_skill_matrix() (in module api.v1.security_career_framework)</a>
</li>
      <li><a href="api/index.html#api.v1.security_career_framework.get_seniority_levels">get_seniority_levels() (in module api.v1.security_career_framework)</a>
</li>
      <li><a href="api/index.html#api.v1.integration_hub.get_sso_metadata">get_sso_metadata() (in module api.v1.integration_hub)</a>
</li>
      <li><a href="api/index.html#api.v1.progress_tracking.get_study_insights">get_study_insights() (in module api.v1.progress_tracking)</a>
</li>
      <li><a href="api/index.html#api.v1.progress_tracking.get_study_sessions">get_study_sessions() (in module api.v1.progress_tracking)</a>
</li>
      <li><a href="api/index.html#api.v1.career_transition.get_transition_plan">get_transition_plan() (in module api.v1.career_transition)</a>
</li>
      <li><a href="api/index.html#api.v1.career_transition.get_transition_summary">get_transition_summary() (in module api.v1.career_transition)</a>
</li>
      <li><a href="api/index.html#api.v1.enterprise.get_user">get_user() (in module api.v1.enterprise)</a>
</li>
      <li><a href="api/index.html#api.v1.career_transition.get_user_transition_plans">get_user_transition_plans() (in module api.v1.career_transition)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="H">H</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/index.html#api.v1.enterprise.health_check">health_check() (in module api.v1.enterprise)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="I">I</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/index.html#api.v1.integration_hub.integration_hub_health">integration_hub_health() (in module api.v1.integration_hub)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="L">L</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/index.html#api.v1.integration_hub.list_integrations">list_integrations() (in module api.v1.integration_hub)</a>
</li>
      <li><a href="api/index.html#api.v1.enterprise.list_organization_departments">list_organization_departments() (in module api.v1.enterprise)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/index.html#api.v1.enterprise.list_organization_users">list_organization_users() (in module api.v1.enterprise)</a>
</li>
      <li><a href="api/index.html#api.v1.enterprise.list_organizations">list_organizations() (in module api.v1.enterprise)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="M">M</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/index.html#api.v1.mobile.mobile_health_check">mobile_health_check() (in module api.v1.mobile)</a>
</li>
      <li>
    module

      <ul>
        <li><a href="api/index.html#module-api.v1.career_transition">api.v1.career_transition</a>
</li>
        <li><a href="api/index.html#module-api.v1.enterprise">api.v1.enterprise</a>
</li>
        <li><a href="api/index.html#module-api.v1.integration_hub">api.v1.integration_hub</a>
</li>
        <li><a href="api/index.html#module-api.v1.mobile">api.v1.mobile</a>
</li>
        <li><a href="api/index.html#module-api.v1.progress_tracking">api.v1.progress_tracking</a>
</li>
        <li><a href="api/index.html#module-api.v1.security_career_framework">api.v1.security_career_framework</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="Q">Q</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/index.html#api.v1.mobile.quick_start_study_session">quick_start_study_session() (in module api.v1.mobile)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="R">R</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/index.html#api.v1.progress_tracking.record_practice_test_result">record_practice_test_result() (in module api.v1.progress_tracking)</a>
</li>
      <li><a href="api/index.html#api.v1.mobile.register_mobile_device">register_mobile_device() (in module api.v1.mobile)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/index.html#api.v1.enterprise.require_org_admin">require_org_admin() (in module api.v1.enterprise)</a>
</li>
      <li><a href="api/index.html#api.v1.enterprise.require_super_admin">require_super_admin() (in module api.v1.enterprise)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="S">S</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/index.html#api.v1.mobile.schedule_smart_notifications">schedule_smart_notifications() (in module api.v1.mobile)</a>
</li>
      <li><a href="api/index.html#api.v1.mobile.send_push_notification">send_push_notification() (in module api.v1.mobile)</a>
</li>
      <li><a href="api/index.html#api.v1.progress_tracking.start_study_session">start_study_session() (in module api.v1.progress_tracking)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/index.html#api.v1.integration_hub.sync_hr_data">sync_hr_data() (in module api.v1.integration_hub)</a>
</li>
      <li><a href="api/index.html#api.v1.integration_hub.sync_ldap_users">sync_ldap_users() (in module api.v1.integration_hub)</a>
</li>
      <li><a href="api/index.html#api.v1.integration_hub.sync_lms_data">sync_lms_data() (in module api.v1.integration_hub)</a>
</li>
      <li><a href="api/index.html#api.v1.mobile.sync_mobile_data">sync_mobile_data() (in module api.v1.mobile)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="T">T</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/index.html#api.v1.integration_hub.test_integration_connection">test_integration_connection() (in module api.v1.integration_hub)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/index.html#api.v1.mobile.track_mobile_analytics">track_mobile_analytics() (in module api.v1.mobile)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="U">U</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/index.html#api.v1.progress_tracking.update_goal_progress">update_goal_progress() (in module api.v1.progress_tracking)</a>
</li>
      <li><a href="api/index.html#api.v1.progress_tracking.update_learning_goal">update_learning_goal() (in module api.v1.progress_tracking)</a>
</li>
      <li><a href="api/index.html#api.v1.enterprise.update_organization">update_organization() (in module api.v1.enterprise)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="api/index.html#api.v1.security_career_framework.update_security_job_type">update_security_job_type() (in module api.v1.security_career_framework)</a>
</li>
      <li><a href="api/index.html#api.v1.career_transition.update_step_progress">update_step_progress() (in module api.v1.career_transition)</a>
</li>
      <li><a href="api/index.html#api.v1.career_transition.update_transition_plan">update_transition_plan() (in module api.v1.career_transition)</a>
</li>
      <li><a href="api/index.html#api.v1.enterprise.update_user">update_user() (in module api.v1.enterprise)</a>
</li>
  </ul></td>
</tr></table>



           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, CertPathFinder Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>