CertPathFinder Documentation
============================

Welcome to CertPathFinder, the most comprehensive cybersecurity certification and career guidance platform!

CertPathFinder is an AI-powered platform that provides personalized career guidance, certification recommendations, 
and learning paths for cybersecurity professionals. Built with modern technologies and real-world market data, 
it offers enterprise-grade features for individuals and organizations.

🎯 **Key Features**
-------------------

* **💰 Cost Calculator API** - ROI analysis for certifications with real market data
* **🔗 Integration Hub** - Enterprise SSO, LDAP, LMS, and HR system integrations
* **🎯 Security Career Framework** - Paul <PERSON>'s 8 security areas implementation
* **🤖 AI Study Assistant** - Intelligent learning recommendations and progress tracking
* **📱 Mobile Enterprise** - Native mobile API platform with offline capabilities
* **📊 Progress Tracking** - Comprehensive analytics and achievement systems
* **🏢 Enterprise Dashboard** - Multi-tenant organization management
* **🧠 Enhanced Taxonomy** - Market-driven career guidance with 500+ skills, 200+ certifications
* **⏱️ Study Timer Backend** - Session management and productivity tracking
* **🐳 Docker Support** - Complete containerization for easy deployment

📚 **Documentation Sections**
------------------------------

.. toctree::
   :maxdepth: 2
   :caption: Getting Started:

   installation
   quickstart
   configuration

.. toctree::
   :maxdepth: 2
   :caption: API Reference:

   api/index
   api/authentication
   api/cost_calculator
   api/career_framework
   api/study_timer
   api/integration_hub
   api/enhanced_taxonomy

.. toctree::
   :maxdepth: 2
   :caption: User Guides:

   guides/user_guide
   guides/admin_guide
   guides/enterprise_guide
   guides/mobile_guide

.. toctree::
   :maxdepth: 2
   :caption: Development:

   development/architecture
   development/contributing
   development/testing
   development/deployment

.. toctree::
   :maxdepth: 2
   :caption: Reference:

   models/index
   schemas/index
   services/index
   utils/index

🚀 **Quick Links**
------------------

* :doc:`installation` - Get started with CertPathFinder
* :doc:`api/index` - Complete API documentation
* :doc:`guides/user_guide` - User guide and tutorials
* :doc:`development/architecture` - System architecture overview

📊 **Platform Statistics**
---------------------------

* **25+ API modules** with comprehensive functionality
* **50+ database models** for complete data management  
* **100+ API endpoints** covering all use cases
* **500+ security skills** with market demand data
* **200+ certifications** with ROI analysis
* **140+ job titles** from real market research
* **300+ security technologies** with adoption rates

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
