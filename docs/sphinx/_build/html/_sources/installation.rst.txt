Installation Guide
==================

This guide will help you install and set up CertPathFinder on your system.

Prerequisites
-------------

Before installing CertPathFinder, ensure you have the following prerequisites:

* **Python 3.10+** - Required for running the application
* **PostgreSQL 13+** - Primary database (SQLite supported for development)
* **Redis 6+** - For caching and background tasks
* **Docker & Docker Compose** - For containerized deployment (optional)
* **Git** - For cloning the repository

Quick Start with Docker
------------------------

The fastest way to get CertPathFinder running is using Docker Compose:

.. code-block:: bash

   # Clone the repository
   git clone https://github.com/forkrul/replit-CertPathFinder.git
   cd replit-CertPathFinder

   # Start all services
   docker-compose up -d

   # Access the application
   # FastAPI: http://localhost:8000
   # Streamlit: http://localhost:8501

Manual Installation
-------------------

For development or custom deployments, you can install manually:

1. **Clone the Repository**

.. code-block:: bash

   git clone https://github.com/forkrul/replit-CertPathFinder.git
   cd replit-CertPathFinder

2. **Create Virtual Environment**

.. code-block:: bash

   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate

3. **Install Dependencies**

.. code-block:: bash

   pip install -r requirements.txt

4. **Set Up Database**

.. code-block:: bash

   # For PostgreSQL
   createdb certpathfinder
   
   # For SQLite (development)
   # Database will be created automatically

5. **Configure Environment**

.. code-block:: bash

   cp .env.example .env
   # Edit .env with your configuration

6. **Run Database Migrations**

.. code-block:: bash

   alembic upgrade head

7. **Start the Services**

.. code-block:: bash

   # Start FastAPI backend
   python run_api.py

   # Start Streamlit frontend (in another terminal)
   streamlit run main.py

Environment Configuration
-------------------------

Create a `.env` file with the following configuration:

.. code-block:: bash

   # Database Configuration
   DATABASE_URL=postgresql://user:password@localhost:5432/certpathfinder
   # Or for SQLite: DATABASE_URL=sqlite:///./certpathfinder.db

   # Redis Configuration
   REDIS_URL=redis://localhost:6379

   # Security
   SECRET_KEY=your-secret-key-here
   ALGORITHM=HS256
   ACCESS_TOKEN_EXPIRE_MINUTES=30

   # API Configuration
   API_V1_STR=/api/v1
   PROJECT_NAME=CertPathFinder

   # External APIs (optional)
   OPENAI_API_KEY=your-openai-key
   ANTHROPIC_API_KEY=your-anthropic-key

   # Email Configuration (optional)
   SMTP_TLS=True
   SMTP_PORT=587
   SMTP_HOST=smtp.gmail.com
   SMTP_USER=<EMAIL>
   SMTP_PASSWORD=your-app-password

Verification
------------

After installation, verify that everything is working:

1. **Check API Health**

.. code-block:: bash

   curl http://localhost:8000/health

2. **Access API Documentation**

   Visit http://localhost:8000/docs for interactive API documentation

3. **Access Frontend**

   Visit http://localhost:8501 for the Streamlit interface

4. **Run Tests**

.. code-block:: bash

   pytest tests/

Troubleshooting
---------------

**Common Issues:**

* **Database Connection Error**: Ensure PostgreSQL is running and credentials are correct
* **Redis Connection Error**: Ensure Redis server is running
* **Port Already in Use**: Change ports in configuration or stop conflicting services
* **Import Errors**: Ensure all dependencies are installed in the virtual environment

**Getting Help:**

* Check the logs for detailed error messages
* Review the configuration in `.env` file
* Ensure all prerequisites are properly installed
* Consult the development documentation for advanced setup

Next Steps
----------

After successful installation:

* Read the :doc:`quickstart` guide for basic usage
* Review the :doc:`configuration` for advanced settings
* Explore the :doc:`api/index` for API documentation
* Check out :doc:`guides/user_guide` for detailed usage instructions
