Quick Start Guide
==================

This guide will help you get started with CertPathFinder quickly and understand its core features.

Overview
--------

CertPathFinder is a comprehensive cybersecurity certification and career guidance platform that provides:

* **Personalized career recommendations** based on real market data
* **ROI analysis for certifications** with cost-benefit calculations
* **AI-powered study assistance** and progress tracking
* **Enterprise integrations** for organizations
* **Mobile-first design** for learning on the go

First Steps
-----------

1. **Access the Platform**

   After installation, access CertPathFinder at:
   
   * **Web Interface**: http://localhost:8501
   * **API Documentation**: http://localhost:8000/docs
   * **API Base URL**: http://localhost:8000/api/v1

2. **Create Your Profile**

   Start by creating a user profile to get personalized recommendations:

   .. code-block:: python

      import requests

      # Create user profile
      user_data = {
          "email": "<EMAIL>",
          "full_name": "<PERSON>",
          "current_role": "Security Analyst",
          "experience_years": 3,
          "target_domain": "cloud_security"
      }

      response = requests.post(
          "http://localhost:8000/api/v1/user/profile",
          json=user_data
      )

3. **Explore Certifications**

   Get certification recommendations based on your profile:

   .. code-block:: python

      # Get certification recommendations
      response = requests.get(
          "http://localhost:8000/api/v1/enhanced-taxonomy/certifications/high-value"
      )
      certifications = response.json()

Core Features
-------------

Cost Calculator
~~~~~~~~~~~~~~~

Calculate the ROI of certifications with real market data:

.. code-block:: python

   # Calculate certification costs
   cost_data = {
       "certification_id": 1,
       "location": "United States",
       "study_hours_per_week": 10,
       "target_completion_months": 6
   }

   response = requests.post(
       "http://localhost:8000/api/v1/cost-calculator/calculate",
       json=cost_data
   )
   cost_analysis = response.json()

Career Framework
~~~~~~~~~~~~~~~~

Explore career paths in cybersecurity:

.. code-block:: python

   # Get career progression options
   response = requests.get(
       "http://localhost:8000/api/v1/enhanced-taxonomy/job-titles/high-demand"
   )
   job_opportunities = response.json()

   # Get career progression from current role
   response = requests.get(
       "http://localhost:8000/api/v1/enhanced-taxonomy/job-titles/1/progression"
   )
   career_paths = response.json()

Study Timer
~~~~~~~~~~~

Track your study sessions and progress:

.. code-block:: python

   # Start a study session
   session_data = {
       "user_id": 1,
       "certification_id": 1,
       "planned_duration_minutes": 60,
       "study_type": "reading"
   }

   response = requests.post(
       "http://localhost:8000/api/v1/study-timer/sessions/start",
       json=session_data
   )
   session = response.json()

   # End the session
   requests.post(
       f"http://localhost:8000/api/v1/study-timer/sessions/{session['id']}/end"
   )

AI Assistant
~~~~~~~~~~~~

Get personalized study recommendations:

.. code-block:: python

   # Get skill recommendations
   recommendations_data = {
       "user_id": 1,
       "current_skills": ["Network Security", "Incident Response"],
       "target_domain": "cloud_security"
   }

   response = requests.post(
       "http://localhost:8000/api/v1/enhanced-taxonomy/recommendations/skills",
       json=recommendations_data
   )
   skill_recommendations = response.json()

Web Interface Usage
-------------------

The Streamlit web interface provides an intuitive way to interact with all features:

1. **Dashboard**: Overview of your progress and recommendations
2. **Certifications**: Browse and compare certifications
3. **Career Paths**: Explore career opportunities and progression
4. **Study Timer**: Track study sessions and productivity
5. **Cost Calculator**: Analyze certification ROI
6. **Progress Tracking**: Monitor learning goals and achievements

Key Workflows
-------------

Certification Planning Workflow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

1. **Assess Current Skills**: Use the skill assessment feature
2. **Set Career Goals**: Define target roles and timeframes
3. **Get Recommendations**: Receive personalized certification suggestions
4. **Calculate ROI**: Analyze costs and benefits
5. **Create Study Plan**: Set up learning goals and schedules
6. **Track Progress**: Monitor study sessions and achievements

Career Transition Workflow
~~~~~~~~~~~~~~~~~~~~~~~~~~~

1. **Current Role Analysis**: Input your current position and skills
2. **Target Role Selection**: Choose desired career direction
3. **Gap Analysis**: Identify skill and certification gaps
4. **Learning Path Creation**: Get step-by-step guidance
5. **Progress Monitoring**: Track advancement toward goals
6. **Market Intelligence**: Stay updated on industry trends

API Integration
---------------

For developers integrating with CertPathFinder:

Authentication
~~~~~~~~~~~~~~

.. code-block:: python

   # Get access token
   auth_data = {
       "username": "<EMAIL>",
       "password": "password"
   }

   response = requests.post(
       "http://localhost:8000/api/v1/auth/login",
       data=auth_data
   )
   token = response.json()["access_token"]

   # Use token in subsequent requests
   headers = {"Authorization": f"Bearer {token}"}

Common API Patterns
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Get user's personalized data
   response = requests.get(
       "http://localhost:8000/api/v1/user/dashboard",
       headers=headers
   )

   # Submit assessment data
   assessment_data = {
       "skill_assessments": [
           {"skill_id": 1, "skill_level": "intermediate", "confidence_level": 4}
       ]
   }

   response = requests.post(
       "http://localhost:8000/api/v1/enhanced-taxonomy/assess/skills",
       json=assessment_data,
       headers=headers
   )

Next Steps
----------

Now that you're familiar with the basics:

* Explore the complete :doc:`api/index` documentation
* Read the detailed :doc:`guides/user_guide`
* Check out :doc:`development/architecture` for technical details
* Review :doc:`guides/enterprise_guide` for organizational features

For more advanced usage and customization, see the comprehensive guides in the documentation.
