

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../../../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>api.v1.progress_tracking &mdash; CertPathFinder 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../../../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../../../_static/custom.css?v=c14262df" />

  
      <script src="../../../_static/jquery.js?v=5d32c60e"></script>
      <script src="../../../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../../../_static/documentation_options.js?v=8d563738"></script>
      <script src="../../../_static/doctools.js?v=9bcbadda"></script>
      <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../../../index.html" class="icon icon-home">
            CertPathFinder
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../quickstart.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../api/index.html">API Reference</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../../index.html">CertPathFinder</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../../index.html">Module code</a></li>
      <li class="breadcrumb-item active">api.v1.progress_tracking</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>Source code for api.v1.progress_tracking</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;FastAPI endpoints for progress tracking and learning analytics.</span>

<span class="sd">This module provides comprehensive API endpoints for study session tracking,</span>
<span class="sd">practice test results, learning goals, achievements, and analytics.</span>
<span class="sd">&quot;&quot;&quot;</span>

<span class="kn">import</span><span class="w"> </span><span class="nn">logging</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">typing</span><span class="w"> </span><span class="kn">import</span> <span class="n">List</span><span class="p">,</span> <span class="n">Optional</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">fastapi</span><span class="w"> </span><span class="kn">import</span> <span class="n">APIRouter</span><span class="p">,</span> <span class="n">Depends</span><span class="p">,</span> <span class="n">HTTPException</span><span class="p">,</span> <span class="n">Query</span><span class="p">,</span> <span class="n">Path</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">sqlalchemy.orm</span><span class="w"> </span><span class="kn">import</span> <span class="n">Session</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">datetime</span><span class="w"> </span><span class="kn">import</span> <span class="n">datetime</span><span class="p">,</span> <span class="n">timedelta</span>

<span class="kn">from</span><span class="w"> </span><span class="nn">database</span><span class="w"> </span><span class="kn">import</span> <span class="n">get_db</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">schemas.progress_tracking</span><span class="w"> </span><span class="kn">import</span> <span class="p">(</span>
    <span class="n">StudySessionStart</span><span class="p">,</span> <span class="n">StudySessionEnd</span><span class="p">,</span> <span class="n">StudySessionResponse</span><span class="p">,</span>
    <span class="n">PracticeTestResultCreate</span><span class="p">,</span> <span class="n">PracticeTestResultResponse</span><span class="p">,</span>
    <span class="n">LearningGoalCreate</span><span class="p">,</span> <span class="n">LearningGoalUpdate</span><span class="p">,</span> <span class="n">LearningGoalResponse</span><span class="p">,</span>
    <span class="n">GoalProgressUpdate</span><span class="p">,</span> <span class="n">AchievementResponse</span><span class="p">,</span>
    <span class="n">ProgressSummaryResponse</span><span class="p">,</span> <span class="n">StudyInsightsResponse</span><span class="p">,</span> <span class="n">LearningAnalyticsResponse</span><span class="p">,</span>
    <span class="n">ProgressDashboardResponse</span><span class="p">,</span> <span class="n">PeriodType</span>
<span class="p">)</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">services.progress_tracking</span><span class="w"> </span><span class="kn">import</span> <span class="n">ProgressTrackingService</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">models.progress_tracking</span><span class="w"> </span><span class="kn">import</span> <span class="n">StudySession</span><span class="p">,</span> <span class="n">PracticeTestResult</span><span class="p">,</span> <span class="n">LearningGoal</span><span class="p">,</span> <span class="n">Achievement</span>

<span class="n">logger</span> <span class="o">=</span> <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="vm">__name__</span><span class="p">)</span>
<span class="n">router</span> <span class="o">=</span> <span class="n">APIRouter</span><span class="p">(</span><span class="n">prefix</span><span class="o">=</span><span class="s2">&quot;/progress&quot;</span><span class="p">,</span> <span class="n">tags</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;Progress Tracking&quot;</span><span class="p">])</span>


<div class="viewcode-block" id="get_current_user_id">
<a class="viewcode-back" href="../../../api/index.html#api.v1.progress_tracking.get_current_user_id">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">get_current_user_id</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get current user ID from authentication context.&quot;&quot;&quot;</span>
    <span class="c1"># TODO: Implement proper authentication</span>
    <span class="k">return</span> <span class="s2">&quot;test_user_1&quot;</span></div>



<div class="viewcode-block" id="start_study_session">
<a class="viewcode-back" href="../../../api/index.html#api.v1.progress_tracking.start_study_session">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/sessions/start&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">StudySessionResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">start_study_session</span><span class="p">(</span>
    <span class="n">request</span><span class="p">:</span> <span class="n">StudySessionStart</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Start a new study session.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Starting study session for user </span><span class="si">{</span><span class="n">user_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">service</span> <span class="o">=</span> <span class="n">ProgressTrackingService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        
        <span class="n">session</span> <span class="o">=</span> <span class="n">service</span><span class="o">.</span><span class="n">start_study_session</span><span class="p">(</span>
            <span class="n">user_id</span><span class="o">=</span><span class="n">user_id</span><span class="p">,</span>
            <span class="n">session_type</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">session_type</span><span class="o">.</span><span class="n">value</span><span class="p">,</span>
            <span class="n">certification_id</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">certification_id</span><span class="p">,</span>
            <span class="n">learning_path_id</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">learning_path_id</span><span class="p">,</span>
            <span class="n">transition_plan_id</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">transition_plan_id</span><span class="p">,</span>
            <span class="n">topic</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">topic</span><span class="p">,</span>
            <span class="n">planned_duration_minutes</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">planned_duration_minutes</span>
        <span class="p">)</span>
        
        <span class="k">return</span> <span class="n">session</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error starting study session: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error starting study session&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="end_study_session">
<a class="viewcode-back" href="../../../api/index.html#api.v1.progress_tracking.end_study_session">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">put</span><span class="p">(</span><span class="s2">&quot;/sessions/</span><span class="si">{session_id}</span><span class="s2">/end&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">StudySessionResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">end_study_session</span><span class="p">(</span>
    <span class="n">session_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span>
    <span class="n">request</span><span class="p">:</span> <span class="n">StudySessionEnd</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;End a study session and record progress.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Ending study session </span><span class="si">{</span><span class="n">session_id</span><span class="si">}</span><span class="s2"> for user </span><span class="si">{</span><span class="n">user_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">service</span> <span class="o">=</span> <span class="n">ProgressTrackingService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        
        <span class="n">session</span> <span class="o">=</span> <span class="n">service</span><span class="o">.</span><span class="n">end_study_session</span><span class="p">(</span>
            <span class="n">session_id</span><span class="o">=</span><span class="n">session_id</span><span class="p">,</span>
            <span class="n">user_id</span><span class="o">=</span><span class="n">user_id</span><span class="p">,</span>
            <span class="n">progress_after</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">progress_after</span><span class="p">,</span>
            <span class="n">confidence_level</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">confidence_level</span><span class="p">,</span>
            <span class="n">difficulty_rating</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">difficulty_rating</span><span class="p">,</span>
            <span class="n">focus_rating</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">focus_rating</span><span class="p">,</span>
            <span class="n">effectiveness_rating</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">effectiveness_rating</span><span class="p">,</span>
            <span class="n">notes</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">notes</span><span class="p">,</span>
            <span class="n">materials_used</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">materials_used</span><span class="p">,</span>
            <span class="n">tags</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">tags</span>
        <span class="p">)</span>
        
        <span class="k">return</span> <span class="n">session</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span>
        
    <span class="k">except</span> <span class="ne">ValueError</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">))</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error ending study session: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error ending study session&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="get_study_sessions">
<a class="viewcode-back" href="../../../api/index.html#api.v1.progress_tracking.get_study_sessions">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/sessions&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">List</span><span class="p">[</span><span class="n">StudySessionResponse</span><span class="p">])</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_study_sessions</span><span class="p">(</span>
    <span class="n">certification_id</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by certification&quot;</span><span class="p">),</span>
    <span class="n">session_type</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by session type&quot;</span><span class="p">),</span>
    <span class="n">days_back</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">30</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">le</span><span class="o">=</span><span class="mi">365</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Number of days to look back&quot;</span><span class="p">),</span>
    <span class="n">page</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Page number&quot;</span><span class="p">),</span>
    <span class="n">page_size</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">20</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">le</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Page size&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get user&#39;s study sessions with filtering options.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        
        <span class="n">cutoff_date</span> <span class="o">=</span> <span class="n">datetime</span><span class="o">.</span><span class="n">utcnow</span><span class="p">()</span> <span class="o">-</span> <span class="n">timedelta</span><span class="p">(</span><span class="n">days</span><span class="o">=</span><span class="n">days_back</span><span class="p">)</span>
        
        <span class="n">query</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">StudySession</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
            <span class="n">StudySession</span><span class="o">.</span><span class="n">user_id</span> <span class="o">==</span> <span class="n">user_id</span><span class="p">,</span>
            <span class="n">StudySession</span><span class="o">.</span><span class="n">started_at</span> <span class="o">&gt;=</span> <span class="n">cutoff_date</span>
        <span class="p">)</span>
        
        <span class="k">if</span> <span class="n">certification_id</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">StudySession</span><span class="o">.</span><span class="n">certification_id</span> <span class="o">==</span> <span class="n">certification_id</span><span class="p">)</span>
        
        <span class="k">if</span> <span class="n">session_type</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">StudySession</span><span class="o">.</span><span class="n">session_type</span> <span class="o">==</span> <span class="n">session_type</span><span class="p">)</span>
        
        <span class="c1"># Apply pagination</span>
        <span class="n">offset</span> <span class="o">=</span> <span class="p">(</span><span class="n">page</span> <span class="o">-</span> <span class="mi">1</span><span class="p">)</span> <span class="o">*</span> <span class="n">page_size</span>
        <span class="n">sessions</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">order_by</span><span class="p">(</span><span class="n">StudySession</span><span class="o">.</span><span class="n">started_at</span><span class="o">.</span><span class="n">desc</span><span class="p">())</span><span class="o">.</span><span class="n">offset</span><span class="p">(</span><span class="n">offset</span><span class="p">)</span><span class="o">.</span><span class="n">limit</span><span class="p">(</span><span class="n">page_size</span><span class="p">)</span><span class="o">.</span><span class="n">all</span><span class="p">()</span>
        
        <span class="k">return</span> <span class="p">[</span><span class="n">session</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span> <span class="k">for</span> <span class="n">session</span> <span class="ow">in</span> <span class="n">sessions</span><span class="p">]</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error retrieving study sessions: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error retrieving study sessions&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="record_practice_test_result">
<a class="viewcode-back" href="../../../api/index.html#api.v1.progress_tracking.record_practice_test_result">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/practice-tests&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">PracticeTestResultResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">record_practice_test_result</span><span class="p">(</span>
    <span class="n">request</span><span class="p">:</span> <span class="n">PracticeTestResultCreate</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Record a practice test result.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Recording practice test result for user </span><span class="si">{</span><span class="n">user_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">service</span> <span class="o">=</span> <span class="n">ProgressTrackingService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        
        <span class="n">result</span> <span class="o">=</span> <span class="n">service</span><span class="o">.</span><span class="n">record_practice_test_result</span><span class="p">(</span>
            <span class="n">user_id</span><span class="o">=</span><span class="n">user_id</span><span class="p">,</span>
            <span class="n">certification_id</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">certification_id</span><span class="p">,</span>
            <span class="n">test_name</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">test_name</span><span class="p">,</span>
            <span class="n">test_type</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">test_type</span><span class="o">.</span><span class="n">value</span><span class="p">,</span>
            <span class="n">total_questions</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">total_questions</span><span class="p">,</span>
            <span class="n">correct_answers</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">correct_answers</span><span class="p">,</span>
            <span class="n">time_taken_minutes</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">time_taken_minutes</span><span class="p">,</span>
            <span class="n">domain_scores</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">domain_scores</span><span class="p">,</span>
            <span class="n">topic_scores</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">topic_scores</span><span class="p">,</span>
            <span class="n">question_details</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">question_details</span><span class="p">,</span>
            <span class="n">test_provider</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">test_provider</span><span class="p">,</span>
            <span class="n">passing_score</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">passing_score</span>
        <span class="p">)</span>
        
        <span class="k">return</span> <span class="n">result</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error recording practice test result: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error recording practice test result&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="get_practice_test_results">
<a class="viewcode-back" href="../../../api/index.html#api.v1.progress_tracking.get_practice_test_results">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/practice-tests&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">List</span><span class="p">[</span><span class="n">PracticeTestResultResponse</span><span class="p">])</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_practice_test_results</span><span class="p">(</span>
    <span class="n">certification_id</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by certification&quot;</span><span class="p">),</span>
    <span class="n">test_type</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by test type&quot;</span><span class="p">),</span>
    <span class="n">days_back</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">90</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">le</span><span class="o">=</span><span class="mi">365</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Number of days to look back&quot;</span><span class="p">),</span>
    <span class="n">page</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Page number&quot;</span><span class="p">),</span>
    <span class="n">page_size</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">20</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">le</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Page size&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get user&#39;s practice test results with filtering options.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        
        <span class="n">cutoff_date</span> <span class="o">=</span> <span class="n">datetime</span><span class="o">.</span><span class="n">utcnow</span><span class="p">()</span> <span class="o">-</span> <span class="n">timedelta</span><span class="p">(</span><span class="n">days</span><span class="o">=</span><span class="n">days_back</span><span class="p">)</span>
        
        <span class="n">query</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">PracticeTestResult</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
            <span class="n">PracticeTestResult</span><span class="o">.</span><span class="n">user_id</span> <span class="o">==</span> <span class="n">user_id</span><span class="p">,</span>
            <span class="n">PracticeTestResult</span><span class="o">.</span><span class="n">created_at</span> <span class="o">&gt;=</span> <span class="n">cutoff_date</span>
        <span class="p">)</span>
        
        <span class="k">if</span> <span class="n">certification_id</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">PracticeTestResult</span><span class="o">.</span><span class="n">certification_id</span> <span class="o">==</span> <span class="n">certification_id</span><span class="p">)</span>
        
        <span class="k">if</span> <span class="n">test_type</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">PracticeTestResult</span><span class="o">.</span><span class="n">test_type</span> <span class="o">==</span> <span class="n">test_type</span><span class="p">)</span>
        
        <span class="c1"># Apply pagination</span>
        <span class="n">offset</span> <span class="o">=</span> <span class="p">(</span><span class="n">page</span> <span class="o">-</span> <span class="mi">1</span><span class="p">)</span> <span class="o">*</span> <span class="n">page_size</span>
        <span class="n">results</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">order_by</span><span class="p">(</span><span class="n">PracticeTestResult</span><span class="o">.</span><span class="n">created_at</span><span class="o">.</span><span class="n">desc</span><span class="p">())</span><span class="o">.</span><span class="n">offset</span><span class="p">(</span><span class="n">offset</span><span class="p">)</span><span class="o">.</span><span class="n">limit</span><span class="p">(</span><span class="n">page_size</span><span class="p">)</span><span class="o">.</span><span class="n">all</span><span class="p">()</span>
        
        <span class="k">return</span> <span class="p">[</span><span class="n">result</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span> <span class="k">for</span> <span class="n">result</span> <span class="ow">in</span> <span class="n">results</span><span class="p">]</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error retrieving practice test results: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error retrieving practice test results&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="create_learning_goal">
<a class="viewcode-back" href="../../../api/index.html#api.v1.progress_tracking.create_learning_goal">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/goals&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">LearningGoalResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">create_learning_goal</span><span class="p">(</span>
    <span class="n">request</span><span class="p">:</span> <span class="n">LearningGoalCreate</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Create a new learning goal.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Creating learning goal for user </span><span class="si">{</span><span class="n">user_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">service</span> <span class="o">=</span> <span class="n">ProgressTrackingService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        
        <span class="n">goal</span> <span class="o">=</span> <span class="n">service</span><span class="o">.</span><span class="n">create_learning_goal</span><span class="p">(</span>
            <span class="n">user_id</span><span class="o">=</span><span class="n">user_id</span><span class="p">,</span>
            <span class="n">title</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">title</span><span class="p">,</span>
            <span class="n">goal_type</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">goal_type</span><span class="o">.</span><span class="n">value</span><span class="p">,</span>
            <span class="n">target_value</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">target_value</span><span class="p">,</span>
            <span class="n">target_unit</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">target_unit</span><span class="p">,</span>
            <span class="n">target_date</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">target_date</span><span class="p">,</span>
            <span class="n">certification_id</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">certification_id</span><span class="p">,</span>
            <span class="n">description</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">description</span><span class="p">,</span>
            <span class="n">priority</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">priority</span><span class="o">.</span><span class="n">value</span>
        <span class="p">)</span>
        
        <span class="k">return</span> <span class="n">goal</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error creating learning goal: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error creating learning goal&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="get_learning_goals">
<a class="viewcode-back" href="../../../api/index.html#api.v1.progress_tracking.get_learning_goals">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/goals&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">List</span><span class="p">[</span><span class="n">LearningGoalResponse</span><span class="p">])</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_learning_goals</span><span class="p">(</span>
    <span class="n">status</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by goal status&quot;</span><span class="p">),</span>
    <span class="n">goal_type</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by goal type&quot;</span><span class="p">),</span>
    <span class="n">certification_id</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by certification&quot;</span><span class="p">),</span>
    <span class="n">page</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Page number&quot;</span><span class="p">),</span>
    <span class="n">page_size</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">20</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">le</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Page size&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get user&#39;s learning goals with filtering options.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        
        <span class="n">query</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">LearningGoal</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">LearningGoal</span><span class="o">.</span><span class="n">user_id</span> <span class="o">==</span> <span class="n">user_id</span><span class="p">)</span>
        
        <span class="k">if</span> <span class="n">status</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">LearningGoal</span><span class="o">.</span><span class="n">status</span> <span class="o">==</span> <span class="n">status</span><span class="p">)</span>
        
        <span class="k">if</span> <span class="n">goal_type</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">LearningGoal</span><span class="o">.</span><span class="n">goal_type</span> <span class="o">==</span> <span class="n">goal_type</span><span class="p">)</span>
        
        <span class="k">if</span> <span class="n">certification_id</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">LearningGoal</span><span class="o">.</span><span class="n">certification_id</span> <span class="o">==</span> <span class="n">certification_id</span><span class="p">)</span>
        
        <span class="c1"># Apply pagination</span>
        <span class="n">offset</span> <span class="o">=</span> <span class="p">(</span><span class="n">page</span> <span class="o">-</span> <span class="mi">1</span><span class="p">)</span> <span class="o">*</span> <span class="n">page_size</span>
        <span class="n">goals</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">order_by</span><span class="p">(</span><span class="n">LearningGoal</span><span class="o">.</span><span class="n">created_at</span><span class="o">.</span><span class="n">desc</span><span class="p">())</span><span class="o">.</span><span class="n">offset</span><span class="p">(</span><span class="n">offset</span><span class="p">)</span><span class="o">.</span><span class="n">limit</span><span class="p">(</span><span class="n">page_size</span><span class="p">)</span><span class="o">.</span><span class="n">all</span><span class="p">()</span>
        
        <span class="k">return</span> <span class="p">[</span><span class="n">goal</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span> <span class="k">for</span> <span class="n">goal</span> <span class="ow">in</span> <span class="n">goals</span><span class="p">]</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error retrieving learning goals: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error retrieving learning goals&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="update_learning_goal">
<a class="viewcode-back" href="../../../api/index.html#api.v1.progress_tracking.update_learning_goal">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">put</span><span class="p">(</span><span class="s2">&quot;/goals/</span><span class="si">{goal_id}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">LearningGoalResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">update_learning_goal</span><span class="p">(</span>
    <span class="n">goal_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span>
    <span class="n">request</span><span class="p">:</span> <span class="n">LearningGoalUpdate</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Update a learning goal.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        
        <span class="n">goal</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">LearningGoal</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
            <span class="n">LearningGoal</span><span class="o">.</span><span class="n">id</span> <span class="o">==</span> <span class="n">goal_id</span><span class="p">,</span>
            <span class="n">LearningGoal</span><span class="o">.</span><span class="n">user_id</span> <span class="o">==</span> <span class="n">user_id</span>
        <span class="p">)</span><span class="o">.</span><span class="n">first</span><span class="p">()</span>
        
        <span class="k">if</span> <span class="ow">not</span> <span class="n">goal</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Learning goal not found&quot;</span><span class="p">)</span>
        
        <span class="c1"># Update fields</span>
        <span class="n">update_data</span> <span class="o">=</span> <span class="n">request</span><span class="o">.</span><span class="n">dict</span><span class="p">(</span><span class="n">exclude_unset</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">field</span><span class="p">,</span> <span class="n">value</span> <span class="ow">in</span> <span class="n">update_data</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
            <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">goal</span><span class="p">,</span> <span class="n">field</span><span class="p">):</span>
                <span class="nb">setattr</span><span class="p">(</span><span class="n">goal</span><span class="p">,</span> <span class="n">field</span><span class="p">,</span> <span class="n">value</span><span class="o">.</span><span class="n">value</span> <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="s1">&#39;value&#39;</span><span class="p">)</span> <span class="k">else</span> <span class="n">value</span><span class="p">)</span>
        
        <span class="n">goal</span><span class="o">.</span><span class="n">updated_at</span> <span class="o">=</span> <span class="n">datetime</span><span class="o">.</span><span class="n">utcnow</span><span class="p">()</span>
        <span class="n">db</span><span class="o">.</span><span class="n">commit</span><span class="p">()</span>
        
        <span class="k">return</span> <span class="n">goal</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span>
        
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error updating learning goal: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error updating learning goal&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="update_goal_progress">
<a class="viewcode-back" href="../../../api/index.html#api.v1.progress_tracking.update_goal_progress">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">put</span><span class="p">(</span><span class="s2">&quot;/goals/</span><span class="si">{goal_id}</span><span class="s2">/progress&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">LearningGoalResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">update_goal_progress</span><span class="p">(</span>
    <span class="n">goal_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span>
    <span class="n">request</span><span class="p">:</span> <span class="n">GoalProgressUpdate</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Update progress on a learning goal.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        
        <span class="n">service</span> <span class="o">=</span> <span class="n">ProgressTrackingService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        <span class="n">goal</span> <span class="o">=</span> <span class="n">service</span><span class="o">.</span><span class="n">update_goal_progress</span><span class="p">(</span><span class="n">goal_id</span><span class="p">,</span> <span class="n">user_id</span><span class="p">,</span> <span class="n">request</span><span class="o">.</span><span class="n">current_value</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="n">goal</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span>
        
    <span class="k">except</span> <span class="ne">ValueError</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">))</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error updating goal progress: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error updating goal progress&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="get_achievements">
<a class="viewcode-back" href="../../../api/index.html#api.v1.progress_tracking.get_achievements">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/achievements&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">List</span><span class="p">[</span><span class="n">AchievementResponse</span><span class="p">])</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_achievements</span><span class="p">(</span>
    <span class="n">earned_only</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">False</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Show only earned achievements&quot;</span><span class="p">),</span>
    <span class="n">category</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by achievement category&quot;</span><span class="p">),</span>
    <span class="n">page</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Page number&quot;</span><span class="p">),</span>
    <span class="n">page_size</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">50</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">le</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Page size&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get user&#39;s achievements.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        
        <span class="n">query</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">Achievement</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">Achievement</span><span class="o">.</span><span class="n">user_id</span> <span class="o">==</span> <span class="n">user_id</span><span class="p">)</span>
        
        <span class="k">if</span> <span class="n">earned_only</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">Achievement</span><span class="o">.</span><span class="n">is_earned</span> <span class="o">==</span> <span class="kc">True</span><span class="p">)</span>
        
        <span class="k">if</span> <span class="n">category</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">Achievement</span><span class="o">.</span><span class="n">category</span> <span class="o">==</span> <span class="n">category</span><span class="p">)</span>
        
        <span class="c1"># Apply pagination</span>
        <span class="n">offset</span> <span class="o">=</span> <span class="p">(</span><span class="n">page</span> <span class="o">-</span> <span class="mi">1</span><span class="p">)</span> <span class="o">*</span> <span class="n">page_size</span>
        <span class="n">achievements</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">order_by</span><span class="p">(</span><span class="n">Achievement</span><span class="o">.</span><span class="n">earned_at</span><span class="o">.</span><span class="n">desc</span><span class="p">()</span><span class="o">.</span><span class="n">nullslast</span><span class="p">())</span><span class="o">.</span><span class="n">offset</span><span class="p">(</span><span class="n">offset</span><span class="p">)</span><span class="o">.</span><span class="n">limit</span><span class="p">(</span><span class="n">page_size</span><span class="p">)</span><span class="o">.</span><span class="n">all</span><span class="p">()</span>
        
        <span class="k">return</span> <span class="p">[</span><span class="n">achievement</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span> <span class="k">for</span> <span class="n">achievement</span> <span class="ow">in</span> <span class="n">achievements</span><span class="p">]</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error retrieving achievements: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error retrieving achievements&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="get_progress_summary">
<a class="viewcode-back" href="../../../api/index.html#api.v1.progress_tracking.get_progress_summary">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/summary&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">ProgressSummaryResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_progress_summary</span><span class="p">(</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get comprehensive progress summary for the user.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        
        <span class="n">service</span> <span class="o">=</span> <span class="n">ProgressTrackingService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        <span class="n">summary</span> <span class="o">=</span> <span class="n">service</span><span class="o">.</span><span class="n">get_user_progress_summary</span><span class="p">(</span><span class="n">user_id</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="n">summary</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error generating progress summary: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error generating progress summary&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="get_study_insights">
<a class="viewcode-back" href="../../../api/index.html#api.v1.progress_tracking.get_study_insights">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/insights&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">StudyInsightsResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_study_insights</span><span class="p">(</span>
    <span class="n">days_back</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">30</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">7</span><span class="p">,</span> <span class="n">le</span><span class="o">=</span><span class="mi">365</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Number of days to analyze&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get intelligent study insights and recommendations.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        
        <span class="n">service</span> <span class="o">=</span> <span class="n">ProgressTrackingService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        <span class="n">insights</span> <span class="o">=</span> <span class="n">service</span><span class="o">.</span><span class="n">get_study_insights</span><span class="p">(</span><span class="n">user_id</span><span class="p">,</span> <span class="n">days_back</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="n">insights</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error generating study insights: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error generating study insights&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="get_learning_analytics">
<a class="viewcode-back" href="../../../api/index.html#api.v1.progress_tracking.get_learning_analytics">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/analytics&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">LearningAnalyticsResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_learning_analytics</span><span class="p">(</span>
    <span class="n">period_type</span><span class="p">:</span> <span class="n">PeriodType</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="n">PeriodType</span><span class="o">.</span><span class="n">MONTHLY</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Analytics period type&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get learning analytics for a specific period.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        
        <span class="n">service</span> <span class="o">=</span> <span class="n">ProgressTrackingService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        <span class="n">analytics</span> <span class="o">=</span> <span class="n">service</span><span class="o">.</span><span class="n">get_learning_analytics</span><span class="p">(</span><span class="n">user_id</span><span class="p">,</span> <span class="n">period_type</span><span class="o">.</span><span class="n">value</span><span class="p">)</span>
        
        <span class="k">if</span> <span class="ow">not</span> <span class="n">analytics</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Analytics not found for the specified period&quot;</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="n">analytics</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span>
        
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error retrieving learning analytics: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error retrieving learning analytics&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="get_progress_dashboard">
<a class="viewcode-back" href="../../../api/index.html#api.v1.progress_tracking.get_progress_dashboard">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/dashboard&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">ProgressDashboardResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_progress_dashboard</span><span class="p">(</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get comprehensive progress dashboard with all key information.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        
        <span class="n">service</span> <span class="o">=</span> <span class="n">ProgressTrackingService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        
        <span class="c1"># Get summary</span>
        <span class="n">summary</span> <span class="o">=</span> <span class="n">service</span><span class="o">.</span><span class="n">get_user_progress_summary</span><span class="p">(</span><span class="n">user_id</span><span class="p">)</span>
        
        <span class="c1"># Get insights</span>
        <span class="n">insights</span> <span class="o">=</span> <span class="n">service</span><span class="o">.</span><span class="n">get_study_insights</span><span class="p">(</span><span class="n">user_id</span><span class="p">,</span> <span class="n">days_back</span><span class="o">=</span><span class="mi">30</span><span class="p">)</span>
        
        <span class="c1"># Get recent sessions (last 10)</span>
        <span class="n">recent_sessions</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">StudySession</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
            <span class="n">StudySession</span><span class="o">.</span><span class="n">user_id</span> <span class="o">==</span> <span class="n">user_id</span><span class="p">,</span>
            <span class="n">StudySession</span><span class="o">.</span><span class="n">ended_at</span><span class="o">.</span><span class="n">isnot</span><span class="p">(</span><span class="kc">None</span><span class="p">)</span>
        <span class="p">)</span><span class="o">.</span><span class="n">order_by</span><span class="p">(</span><span class="n">StudySession</span><span class="o">.</span><span class="n">started_at</span><span class="o">.</span><span class="n">desc</span><span class="p">())</span><span class="o">.</span><span class="n">limit</span><span class="p">(</span><span class="mi">10</span><span class="p">)</span><span class="o">.</span><span class="n">all</span><span class="p">()</span>
        
        <span class="c1"># Get recent test results (last 5)</span>
        <span class="n">recent_tests</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">PracticeTestResult</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
            <span class="n">PracticeTestResult</span><span class="o">.</span><span class="n">user_id</span> <span class="o">==</span> <span class="n">user_id</span>
        <span class="p">)</span><span class="o">.</span><span class="n">order_by</span><span class="p">(</span><span class="n">PracticeTestResult</span><span class="o">.</span><span class="n">created_at</span><span class="o">.</span><span class="n">desc</span><span class="p">())</span><span class="o">.</span><span class="n">limit</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span><span class="o">.</span><span class="n">all</span><span class="p">()</span>
        
        <span class="c1"># Get active goals</span>
        <span class="n">active_goals</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">LearningGoal</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
            <span class="n">LearningGoal</span><span class="o">.</span><span class="n">user_id</span> <span class="o">==</span> <span class="n">user_id</span><span class="p">,</span>
            <span class="n">LearningGoal</span><span class="o">.</span><span class="n">status</span> <span class="o">==</span> <span class="s1">&#39;active&#39;</span>
        <span class="p">)</span><span class="o">.</span><span class="n">order_by</span><span class="p">(</span><span class="n">LearningGoal</span><span class="o">.</span><span class="n">created_at</span><span class="o">.</span><span class="n">desc</span><span class="p">())</span><span class="o">.</span><span class="n">limit</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span><span class="o">.</span><span class="n">all</span><span class="p">()</span>
        
        <span class="c1"># Get recent achievements (last 5)</span>
        <span class="n">recent_achievements</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">Achievement</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
            <span class="n">Achievement</span><span class="o">.</span><span class="n">user_id</span> <span class="o">==</span> <span class="n">user_id</span><span class="p">,</span>
            <span class="n">Achievement</span><span class="o">.</span><span class="n">is_earned</span> <span class="o">==</span> <span class="kc">True</span>
        <span class="p">)</span><span class="o">.</span><span class="n">order_by</span><span class="p">(</span><span class="n">Achievement</span><span class="o">.</span><span class="n">earned_at</span><span class="o">.</span><span class="n">desc</span><span class="p">())</span><span class="o">.</span><span class="n">limit</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span><span class="o">.</span><span class="n">all</span><span class="p">()</span>
        
        <span class="c1"># Get current month analytics</span>
        <span class="n">analytics</span> <span class="o">=</span> <span class="n">service</span><span class="o">.</span><span class="n">get_learning_analytics</span><span class="p">(</span><span class="n">user_id</span><span class="p">,</span> <span class="s1">&#39;monthly&#39;</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="n">ProgressDashboardResponse</span><span class="p">(</span>
            <span class="n">summary</span><span class="o">=</span><span class="n">summary</span><span class="p">,</span>
            <span class="n">insights</span><span class="o">=</span><span class="n">insights</span><span class="p">,</span>
            <span class="n">recent_sessions</span><span class="o">=</span><span class="p">[</span><span class="n">session</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span> <span class="k">for</span> <span class="n">session</span> <span class="ow">in</span> <span class="n">recent_sessions</span><span class="p">],</span>
            <span class="n">recent_test_results</span><span class="o">=</span><span class="p">[</span><span class="n">test</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span> <span class="k">for</span> <span class="n">test</span> <span class="ow">in</span> <span class="n">recent_tests</span><span class="p">],</span>
            <span class="n">active_goals</span><span class="o">=</span><span class="p">[</span><span class="n">goal</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span> <span class="k">for</span> <span class="n">goal</span> <span class="ow">in</span> <span class="n">active_goals</span><span class="p">],</span>
            <span class="n">recent_achievements</span><span class="o">=</span><span class="p">[</span><span class="n">achievement</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span> <span class="k">for</span> <span class="n">achievement</span> <span class="ow">in</span> <span class="n">recent_achievements</span><span class="p">],</span>
            <span class="n">analytics</span><span class="o">=</span><span class="n">analytics</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span> <span class="k">if</span> <span class="n">analytics</span> <span class="k">else</span> <span class="kc">None</span>
        <span class="p">)</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error generating progress dashboard: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error generating progress dashboard&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="delete_study_session">
<a class="viewcode-back" href="../../../api/index.html#api.v1.progress_tracking.delete_study_session">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">delete</span><span class="p">(</span><span class="s2">&quot;/sessions/</span><span class="si">{session_id}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">delete_study_session</span><span class="p">(</span>
    <span class="n">session_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Delete a study session.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        
        <span class="n">session</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">StudySession</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
            <span class="n">StudySession</span><span class="o">.</span><span class="n">id</span> <span class="o">==</span> <span class="n">session_id</span><span class="p">,</span>
            <span class="n">StudySession</span><span class="o">.</span><span class="n">user_id</span> <span class="o">==</span> <span class="n">user_id</span>
        <span class="p">)</span><span class="o">.</span><span class="n">first</span><span class="p">()</span>
        
        <span class="k">if</span> <span class="ow">not</span> <span class="n">session</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Study session not found&quot;</span><span class="p">)</span>
        
        <span class="n">db</span><span class="o">.</span><span class="n">delete</span><span class="p">(</span><span class="n">session</span><span class="p">)</span>
        <span class="n">db</span><span class="o">.</span><span class="n">commit</span><span class="p">()</span>
        
        <span class="k">return</span> <span class="p">{</span><span class="s2">&quot;message&quot;</span><span class="p">:</span> <span class="s2">&quot;Study session deleted successfully&quot;</span><span class="p">}</span>
        
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error deleting study session: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error deleting study session&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="delete_learning_goal">
<a class="viewcode-back" href="../../../api/index.html#api.v1.progress_tracking.delete_learning_goal">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">delete</span><span class="p">(</span><span class="s2">&quot;/goals/</span><span class="si">{goal_id}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">delete_learning_goal</span><span class="p">(</span>
    <span class="n">goal_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Delete a learning goal.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        
        <span class="n">goal</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">LearningGoal</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
            <span class="n">LearningGoal</span><span class="o">.</span><span class="n">id</span> <span class="o">==</span> <span class="n">goal_id</span><span class="p">,</span>
            <span class="n">LearningGoal</span><span class="o">.</span><span class="n">user_id</span> <span class="o">==</span> <span class="n">user_id</span>
        <span class="p">)</span><span class="o">.</span><span class="n">first</span><span class="p">()</span>
        
        <span class="k">if</span> <span class="ow">not</span> <span class="n">goal</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Learning goal not found&quot;</span><span class="p">)</span>
        
        <span class="n">db</span><span class="o">.</span><span class="n">delete</span><span class="p">(</span><span class="n">goal</span><span class="p">)</span>
        <span class="n">db</span><span class="o">.</span><span class="n">commit</span><span class="p">()</span>
        
        <span class="k">return</span> <span class="p">{</span><span class="s2">&quot;message&quot;</span><span class="p">:</span> <span class="s2">&quot;Learning goal deleted successfully&quot;</span><span class="p">}</span>
        
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error deleting learning goal: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error deleting learning goal&quot;</span><span class="p">)</span></div>

</pre></div>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, CertPathFinder Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>