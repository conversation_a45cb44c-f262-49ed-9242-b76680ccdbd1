

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../../../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>api.v1.security_career_framework &mdash; CertPathFinder 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../../../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../../../_static/custom.css?v=c14262df" />

  
      <script src="../../../_static/jquery.js?v=5d32c60e"></script>
      <script src="../../../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../../../_static/documentation_options.js?v=8d563738"></script>
      <script src="../../../_static/doctools.js?v=9bcbadda"></script>
      <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../../../index.html" class="icon icon-home">
            CertPathFinder
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../quickstart.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../api/index.html">API Reference</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../../index.html">CertPathFinder</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../../index.html">Module code</a></li>
      <li class="breadcrumb-item active">api.v1.security_career_framework</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>Source code for api.v1.security_career_framework</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;Security Career Framework API endpoints.</span>

<span class="sd">This module provides comprehensive API endpoints for security career paths,</span>
<span class="sd">job types, seniority levels, and skill matrices based on Paul Jerimy&#39;s</span>
<span class="sd">8 security areas and industry standards.</span>
<span class="sd">&quot;&quot;&quot;</span>

<span class="kn">from</span><span class="w"> </span><span class="nn">fastapi</span><span class="w"> </span><span class="kn">import</span> <span class="n">APIRouter</span><span class="p">,</span> <span class="n">Depends</span><span class="p">,</span> <span class="n">HTTPException</span><span class="p">,</span> <span class="n">Query</span><span class="p">,</span> <span class="n">Path</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">sqlalchemy.orm</span><span class="w"> </span><span class="kn">import</span> <span class="n">Session</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">typing</span><span class="w"> </span><span class="kn">import</span> <span class="n">List</span><span class="p">,</span> <span class="n">Optional</span><span class="p">,</span> <span class="n">Dict</span><span class="p">,</span> <span class="n">Any</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">database</span><span class="w"> </span><span class="kn">import</span> <span class="n">get_db</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">models.security_career_framework</span><span class="w"> </span><span class="kn">import</span> <span class="p">(</span>
    <span class="n">SecurityJobType</span><span class="p">,</span> <span class="n">SecurityCareerPath</span><span class="p">,</span> <span class="n">SecuritySkillMatrix</span><span class="p">,</span> <span class="n">SecurityMarketData</span><span class="p">,</span>
    <span class="n">SecurityArea</span><span class="p">,</span> <span class="n">SeniorityLevel</span><span class="p">,</span> <span class="n">JobFamily</span>
<span class="p">)</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">schemas.security_career_framework</span><span class="w"> </span><span class="kn">import</span> <span class="p">(</span>
    <span class="n">SecurityJobTypeCreate</span><span class="p">,</span> <span class="n">SecurityJobTypeUpdate</span><span class="p">,</span> <span class="n">SecurityJobTypeResponse</span><span class="p">,</span> <span class="n">SecurityJobTypeListResponse</span><span class="p">,</span>
    <span class="n">SecurityCareerPathCreate</span><span class="p">,</span> <span class="n">SecurityCareerPathResponse</span><span class="p">,</span>
    <span class="n">SecuritySkillMatrixCreate</span><span class="p">,</span> <span class="n">SecuritySkillMatrixResponse</span><span class="p">,</span>
    <span class="n">SecurityMarketDataCreate</span><span class="p">,</span> <span class="n">SecurityMarketDataResponse</span><span class="p">,</span>
    <span class="n">SecurityAreaFilter</span><span class="p">,</span> <span class="n">CareerRecommendationRequest</span><span class="p">,</span> <span class="n">CareerRecommendationResponse</span><span class="p">,</span>
    <span class="n">SecurityAreaSummary</span><span class="p">,</span> <span class="n">SecurityCareerAnalytics</span><span class="p">,</span>
    <span class="n">SecurityAreaEnum</span><span class="p">,</span> <span class="n">SeniorityLevelEnum</span><span class="p">,</span> <span class="n">DemandLevelEnum</span>
<span class="p">)</span>

<span class="n">router</span> <span class="o">=</span> <span class="n">APIRouter</span><span class="p">(</span><span class="n">prefix</span><span class="o">=</span><span class="s2">&quot;/security-career-framework&quot;</span><span class="p">,</span> <span class="n">tags</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;Security Career Framework&quot;</span><span class="p">])</span>


<span class="c1"># Security Job Types endpoints</span>

<div class="viewcode-block" id="get_security_job_types">
<a class="viewcode-back" href="../../../api/index.html#api.v1.security_career_framework.get_security_job_types">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/job-types&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">SecurityJobTypeListResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_security_job_types</span><span class="p">(</span>
    <span class="n">security_area</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">SecurityAreaEnum</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by security area&quot;</span><span class="p">),</span>
    <span class="n">seniority_level</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">SeniorityLevelEnum</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by seniority level&quot;</span><span class="p">),</span>
    <span class="n">job_family</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by job family&quot;</span><span class="p">),</span>
    <span class="n">demand_level</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">DemandLevelEnum</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by demand level&quot;</span><span class="p">),</span>
    <span class="n">remote_friendly</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by remote work friendly&quot;</span><span class="p">),</span>
    <span class="n">min_salary</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Minimum salary filter&quot;</span><span class="p">),</span>
    <span class="n">max_salary</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Maximum salary filter&quot;</span><span class="p">),</span>
    <span class="n">min_experience</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Minimum experience filter&quot;</span><span class="p">),</span>
    <span class="n">max_experience</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Maximum experience filter&quot;</span><span class="p">),</span>
    <span class="n">skip</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Number of records to skip&quot;</span><span class="p">),</span>
    <span class="n">limit</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">100</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">le</span><span class="o">=</span><span class="mi">1000</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Number of records to return&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get security job types with optional filtering.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">query</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">SecurityJobType</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">SecurityJobType</span><span class="o">.</span><span class="n">is_active</span> <span class="o">==</span> <span class="kc">True</span><span class="p">)</span>
        
        <span class="c1"># Apply filters</span>
        <span class="k">if</span> <span class="n">security_area</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">SecurityJobType</span><span class="o">.</span><span class="n">security_area</span> <span class="o">==</span> <span class="n">security_area</span><span class="o">.</span><span class="n">value</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">seniority_level</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">SecurityJobType</span><span class="o">.</span><span class="n">seniority_level</span> <span class="o">==</span> <span class="n">seniority_level</span><span class="o">.</span><span class="n">value</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">job_family</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">SecurityJobType</span><span class="o">.</span><span class="n">job_family</span> <span class="o">==</span> <span class="n">job_family</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">demand_level</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">SecurityJobType</span><span class="o">.</span><span class="n">demand_level</span> <span class="o">==</span> <span class="n">demand_level</span><span class="o">.</span><span class="n">value</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">remote_friendly</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">SecurityJobType</span><span class="o">.</span><span class="n">remote_friendly</span> <span class="o">==</span> <span class="n">remote_friendly</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">min_salary</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">SecurityJobType</span><span class="o">.</span><span class="n">salary_min</span> <span class="o">&gt;=</span> <span class="n">min_salary</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">max_salary</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">SecurityJobType</span><span class="o">.</span><span class="n">salary_max</span> <span class="o">&lt;=</span> <span class="n">max_salary</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">min_experience</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">SecurityJobType</span><span class="o">.</span><span class="n">min_years_experience</span> <span class="o">&gt;=</span> <span class="n">min_experience</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">max_experience</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">SecurityJobType</span><span class="o">.</span><span class="n">max_years_experience</span> <span class="o">&lt;=</span> <span class="n">max_experience</span><span class="p">)</span>
        
        <span class="c1"># Get total count</span>
        <span class="n">total_count</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">count</span><span class="p">()</span>
        
        <span class="c1"># Apply pagination</span>
        <span class="n">job_types</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">offset</span><span class="p">(</span><span class="n">skip</span><span class="p">)</span><span class="o">.</span><span class="n">limit</span><span class="p">(</span><span class="n">limit</span><span class="p">)</span><span class="o">.</span><span class="n">all</span><span class="p">()</span>
        
        <span class="c1"># Convert to response format</span>
        <span class="n">job_type_responses</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">for</span> <span class="n">job_type</span> <span class="ow">in</span> <span class="n">job_types</span><span class="p">:</span>
            <span class="n">job_dict</span> <span class="o">=</span> <span class="n">job_type</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span>
            <span class="n">job_dict</span><span class="p">[</span><span class="s1">&#39;salary_range&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="p">{</span>
                <span class="s1">&#39;min&#39;</span><span class="p">:</span> <span class="n">job_type</span><span class="o">.</span><span class="n">salary_min</span><span class="p">,</span>
                <span class="s1">&#39;max&#39;</span><span class="p">:</span> <span class="n">job_type</span><span class="o">.</span><span class="n">salary_max</span><span class="p">,</span>
                <span class="s1">&#39;currency&#39;</span><span class="p">:</span> <span class="n">job_type</span><span class="o">.</span><span class="n">salary_currency</span>
            <span class="p">}</span>
            <span class="n">job_dict</span><span class="p">[</span><span class="s1">&#39;career_progression&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="p">{</span>
                <span class="s1">&#39;from_roles&#39;</span><span class="p">:</span> <span class="n">job_type</span><span class="o">.</span><span class="n">career_progression_from</span> <span class="ow">or</span> <span class="p">[],</span>
                <span class="s1">&#39;to_roles&#39;</span><span class="p">:</span> <span class="n">job_type</span><span class="o">.</span><span class="n">career_progression_to</span> <span class="ow">or</span> <span class="p">[]</span>
            <span class="p">}</span>
            <span class="n">job_type_responses</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">SecurityJobTypeResponse</span><span class="p">(</span><span class="o">**</span><span class="n">job_dict</span><span class="p">))</span>
        
        <span class="n">filters_applied</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s1">&#39;security_area&#39;</span><span class="p">:</span> <span class="n">security_area</span><span class="o">.</span><span class="n">value</span> <span class="k">if</span> <span class="n">security_area</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
            <span class="s1">&#39;seniority_level&#39;</span><span class="p">:</span> <span class="n">seniority_level</span><span class="o">.</span><span class="n">value</span> <span class="k">if</span> <span class="n">seniority_level</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
            <span class="s1">&#39;job_family&#39;</span><span class="p">:</span> <span class="n">job_family</span><span class="p">,</span>
            <span class="s1">&#39;demand_level&#39;</span><span class="p">:</span> <span class="n">demand_level</span><span class="o">.</span><span class="n">value</span> <span class="k">if</span> <span class="n">demand_level</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
            <span class="s1">&#39;remote_friendly&#39;</span><span class="p">:</span> <span class="n">remote_friendly</span><span class="p">,</span>
            <span class="s1">&#39;salary_range&#39;</span><span class="p">:</span> <span class="p">{</span><span class="s1">&#39;min&#39;</span><span class="p">:</span> <span class="n">min_salary</span><span class="p">,</span> <span class="s1">&#39;max&#39;</span><span class="p">:</span> <span class="n">max_salary</span><span class="p">},</span>
            <span class="s1">&#39;experience_range&#39;</span><span class="p">:</span> <span class="p">{</span><span class="s1">&#39;min&#39;</span><span class="p">:</span> <span class="n">min_experience</span><span class="p">,</span> <span class="s1">&#39;max&#39;</span><span class="p">:</span> <span class="n">max_experience</span><span class="p">}</span>
        <span class="p">}</span>
        
        <span class="k">return</span> <span class="n">SecurityJobTypeListResponse</span><span class="p">(</span>
            <span class="n">job_types</span><span class="o">=</span><span class="n">job_type_responses</span><span class="p">,</span>
            <span class="n">total_count</span><span class="o">=</span><span class="n">total_count</span><span class="p">,</span>
            <span class="n">filters_applied</span><span class="o">=</span><span class="n">filters_applied</span>
        <span class="p">)</span>
    
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;Error retrieving job types: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="get_security_job_type">
<a class="viewcode-back" href="../../../api/index.html#api.v1.security_career_framework.get_security_job_type">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/job-types/</span><span class="si">{job_type_id}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">SecurityJobTypeResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_security_job_type</span><span class="p">(</span>
    <span class="n">job_type_id</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Job type ID&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get a specific security job type by ID.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">job_type</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">SecurityJobType</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
            <span class="n">SecurityJobType</span><span class="o">.</span><span class="n">id</span> <span class="o">==</span> <span class="n">job_type_id</span><span class="p">,</span>
            <span class="n">SecurityJobType</span><span class="o">.</span><span class="n">is_active</span> <span class="o">==</span> <span class="kc">True</span>
        <span class="p">)</span><span class="o">.</span><span class="n">first</span><span class="p">()</span>
        
        <span class="k">if</span> <span class="ow">not</span> <span class="n">job_type</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Job type not found&quot;</span><span class="p">)</span>
        
        <span class="n">job_dict</span> <span class="o">=</span> <span class="n">job_type</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span>
        <span class="n">job_dict</span><span class="p">[</span><span class="s1">&#39;salary_range&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s1">&#39;min&#39;</span><span class="p">:</span> <span class="n">job_type</span><span class="o">.</span><span class="n">salary_min</span><span class="p">,</span>
            <span class="s1">&#39;max&#39;</span><span class="p">:</span> <span class="n">job_type</span><span class="o">.</span><span class="n">salary_max</span><span class="p">,</span>
            <span class="s1">&#39;currency&#39;</span><span class="p">:</span> <span class="n">job_type</span><span class="o">.</span><span class="n">salary_currency</span>
        <span class="p">}</span>
        <span class="n">job_dict</span><span class="p">[</span><span class="s1">&#39;career_progression&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s1">&#39;from_roles&#39;</span><span class="p">:</span> <span class="n">job_type</span><span class="o">.</span><span class="n">career_progression_from</span> <span class="ow">or</span> <span class="p">[],</span>
            <span class="s1">&#39;to_roles&#39;</span><span class="p">:</span> <span class="n">job_type</span><span class="o">.</span><span class="n">career_progression_to</span> <span class="ow">or</span> <span class="p">[]</span>
        <span class="p">}</span>
        
        <span class="k">return</span> <span class="n">SecurityJobTypeResponse</span><span class="p">(</span><span class="o">**</span><span class="n">job_dict</span><span class="p">)</span>
    
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;Error retrieving job type: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="create_security_job_type">
<a class="viewcode-back" href="../../../api/index.html#api.v1.security_career_framework.create_security_job_type">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/job-types&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">SecurityJobTypeResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">create_security_job_type</span><span class="p">(</span>
    <span class="n">job_type_data</span><span class="p">:</span> <span class="n">SecurityJobTypeCreate</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Create a new security job type.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="c1"># Create new job type</span>
        <span class="n">job_type</span> <span class="o">=</span> <span class="n">SecurityJobType</span><span class="p">(</span>
            <span class="n">title</span><span class="o">=</span><span class="n">job_type_data</span><span class="o">.</span><span class="n">title</span><span class="p">,</span>
            <span class="n">security_area</span><span class="o">=</span><span class="n">job_type_data</span><span class="o">.</span><span class="n">security_area</span><span class="o">.</span><span class="n">value</span><span class="p">,</span>
            <span class="n">job_family</span><span class="o">=</span><span class="n">job_type_data</span><span class="o">.</span><span class="n">job_family</span><span class="p">,</span>
            <span class="n">seniority_level</span><span class="o">=</span><span class="n">job_type_data</span><span class="o">.</span><span class="n">seniority_level</span><span class="o">.</span><span class="n">value</span><span class="p">,</span>
            <span class="n">description</span><span class="o">=</span><span class="n">job_type_data</span><span class="o">.</span><span class="n">description</span><span class="p">,</span>
            <span class="n">responsibilities</span><span class="o">=</span><span class="n">job_type_data</span><span class="o">.</span><span class="n">responsibilities</span><span class="p">,</span>
            <span class="n">required_skills</span><span class="o">=</span><span class="n">job_type_data</span><span class="o">.</span><span class="n">required_skills</span><span class="p">,</span>
            <span class="n">preferred_skills</span><span class="o">=</span><span class="n">job_type_data</span><span class="o">.</span><span class="n">preferred_skills</span><span class="p">,</span>
            <span class="n">min_years_experience</span><span class="o">=</span><span class="n">job_type_data</span><span class="o">.</span><span class="n">min_years_experience</span><span class="p">,</span>
            <span class="n">max_years_experience</span><span class="o">=</span><span class="n">job_type_data</span><span class="o">.</span><span class="n">max_years_experience</span><span class="p">,</span>
            <span class="n">education_requirements</span><span class="o">=</span><span class="n">job_type_data</span><span class="o">.</span><span class="n">education_requirements</span><span class="p">,</span>
            <span class="n">required_certifications</span><span class="o">=</span><span class="n">job_type_data</span><span class="o">.</span><span class="n">required_certifications</span><span class="p">,</span>
            <span class="n">preferred_certifications</span><span class="o">=</span><span class="n">job_type_data</span><span class="o">.</span><span class="n">preferred_certifications</span><span class="p">,</span>
            <span class="n">salary_min</span><span class="o">=</span><span class="n">job_type_data</span><span class="o">.</span><span class="n">salary_min</span><span class="p">,</span>
            <span class="n">salary_max</span><span class="o">=</span><span class="n">job_type_data</span><span class="o">.</span><span class="n">salary_max</span><span class="p">,</span>
            <span class="n">salary_currency</span><span class="o">=</span><span class="n">job_type_data</span><span class="o">.</span><span class="n">salary_currency</span><span class="p">,</span>
            <span class="n">career_progression_from</span><span class="o">=</span><span class="n">job_type_data</span><span class="o">.</span><span class="n">career_progression_from</span><span class="p">,</span>
            <span class="n">career_progression_to</span><span class="o">=</span><span class="n">job_type_data</span><span class="o">.</span><span class="n">career_progression_to</span><span class="p">,</span>
            <span class="n">demand_level</span><span class="o">=</span><span class="n">job_type_data</span><span class="o">.</span><span class="n">demand_level</span><span class="o">.</span><span class="n">value</span><span class="p">,</span>
            <span class="n">remote_friendly</span><span class="o">=</span><span class="n">job_type_data</span><span class="o">.</span><span class="n">remote_friendly</span>
        <span class="p">)</span>
        
        <span class="n">db</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">job_type</span><span class="p">)</span>
        <span class="n">db</span><span class="o">.</span><span class="n">commit</span><span class="p">()</span>
        <span class="n">db</span><span class="o">.</span><span class="n">refresh</span><span class="p">(</span><span class="n">job_type</span><span class="p">)</span>
        
        <span class="n">job_dict</span> <span class="o">=</span> <span class="n">job_type</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span>
        <span class="n">job_dict</span><span class="p">[</span><span class="s1">&#39;salary_range&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s1">&#39;min&#39;</span><span class="p">:</span> <span class="n">job_type</span><span class="o">.</span><span class="n">salary_min</span><span class="p">,</span>
            <span class="s1">&#39;max&#39;</span><span class="p">:</span> <span class="n">job_type</span><span class="o">.</span><span class="n">salary_max</span><span class="p">,</span>
            <span class="s1">&#39;currency&#39;</span><span class="p">:</span> <span class="n">job_type</span><span class="o">.</span><span class="n">salary_currency</span>
        <span class="p">}</span>
        <span class="n">job_dict</span><span class="p">[</span><span class="s1">&#39;career_progression&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s1">&#39;from_roles&#39;</span><span class="p">:</span> <span class="n">job_type</span><span class="o">.</span><span class="n">career_progression_from</span> <span class="ow">or</span> <span class="p">[],</span>
            <span class="s1">&#39;to_roles&#39;</span><span class="p">:</span> <span class="n">job_type</span><span class="o">.</span><span class="n">career_progression_to</span> <span class="ow">or</span> <span class="p">[]</span>
        <span class="p">}</span>
        
        <span class="k">return</span> <span class="n">SecurityJobTypeResponse</span><span class="p">(</span><span class="o">**</span><span class="n">job_dict</span><span class="p">)</span>
    
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">db</span><span class="o">.</span><span class="n">rollback</span><span class="p">()</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;Error creating job type: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="update_security_job_type">
<a class="viewcode-back" href="../../../api/index.html#api.v1.security_career_framework.update_security_job_type">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">put</span><span class="p">(</span><span class="s2">&quot;/job-types/</span><span class="si">{job_type_id}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">SecurityJobTypeResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">update_security_job_type</span><span class="p">(</span>
    <span class="n">job_type_id</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Job type ID&quot;</span><span class="p">),</span>
    <span class="n">job_type_data</span><span class="p">:</span> <span class="n">SecurityJobTypeUpdate</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Update a security job type.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">job_type</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">SecurityJobType</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
            <span class="n">SecurityJobType</span><span class="o">.</span><span class="n">id</span> <span class="o">==</span> <span class="n">job_type_id</span><span class="p">,</span>
            <span class="n">SecurityJobType</span><span class="o">.</span><span class="n">is_active</span> <span class="o">==</span> <span class="kc">True</span>
        <span class="p">)</span><span class="o">.</span><span class="n">first</span><span class="p">()</span>
        
        <span class="k">if</span> <span class="ow">not</span> <span class="n">job_type</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Job type not found&quot;</span><span class="p">)</span>
        
        <span class="c1"># Update fields if provided</span>
        <span class="n">update_data</span> <span class="o">=</span> <span class="n">job_type_data</span><span class="o">.</span><span class="n">dict</span><span class="p">(</span><span class="n">exclude_unset</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">field</span><span class="p">,</span> <span class="n">value</span> <span class="ow">in</span> <span class="n">update_data</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
            <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">job_type</span><span class="p">,</span> <span class="n">field</span><span class="p">):</span>
                <span class="nb">setattr</span><span class="p">(</span><span class="n">job_type</span><span class="p">,</span> <span class="n">field</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
        
        <span class="n">db</span><span class="o">.</span><span class="n">commit</span><span class="p">()</span>
        <span class="n">db</span><span class="o">.</span><span class="n">refresh</span><span class="p">(</span><span class="n">job_type</span><span class="p">)</span>
        
        <span class="n">job_dict</span> <span class="o">=</span> <span class="n">job_type</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span>
        <span class="n">job_dict</span><span class="p">[</span><span class="s1">&#39;salary_range&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s1">&#39;min&#39;</span><span class="p">:</span> <span class="n">job_type</span><span class="o">.</span><span class="n">salary_min</span><span class="p">,</span>
            <span class="s1">&#39;max&#39;</span><span class="p">:</span> <span class="n">job_type</span><span class="o">.</span><span class="n">salary_max</span><span class="p">,</span>
            <span class="s1">&#39;currency&#39;</span><span class="p">:</span> <span class="n">job_type</span><span class="o">.</span><span class="n">salary_currency</span>
        <span class="p">}</span>
        <span class="n">job_dict</span><span class="p">[</span><span class="s1">&#39;career_progression&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s1">&#39;from_roles&#39;</span><span class="p">:</span> <span class="n">job_type</span><span class="o">.</span><span class="n">career_progression_from</span> <span class="ow">or</span> <span class="p">[],</span>
            <span class="s1">&#39;to_roles&#39;</span><span class="p">:</span> <span class="n">job_type</span><span class="o">.</span><span class="n">career_progression_to</span> <span class="ow">or</span> <span class="p">[]</span>
        <span class="p">}</span>
        
        <span class="k">return</span> <span class="n">SecurityJobTypeResponse</span><span class="p">(</span><span class="o">**</span><span class="n">job_dict</span><span class="p">)</span>
    
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">db</span><span class="o">.</span><span class="n">rollback</span><span class="p">()</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;Error updating job type: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span></div>



<span class="c1"># Security Career Paths endpoints</span>

<div class="viewcode-block" id="get_security_career_paths">
<a class="viewcode-back" href="../../../api/index.html#api.v1.security_career_framework.get_security_career_paths">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/career-paths&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">List</span><span class="p">[</span><span class="n">SecurityCareerPathResponse</span><span class="p">])</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_security_career_paths</span><span class="p">(</span>
    <span class="n">security_area</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">SecurityAreaEnum</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by security area&quot;</span><span class="p">),</span>
    <span class="n">skip</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Number of records to skip&quot;</span><span class="p">),</span>
    <span class="n">limit</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">100</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">le</span><span class="o">=</span><span class="mi">1000</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Number of records to return&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get security career paths with optional filtering.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">query</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">SecurityCareerPath</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">SecurityCareerPath</span><span class="o">.</span><span class="n">is_active</span> <span class="o">==</span> <span class="kc">True</span><span class="p">)</span>
        
        <span class="k">if</span> <span class="n">security_area</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">SecurityCareerPath</span><span class="o">.</span><span class="n">security_area</span> <span class="o">==</span> <span class="n">security_area</span><span class="o">.</span><span class="n">value</span><span class="p">)</span>
        
        <span class="n">career_paths</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">offset</span><span class="p">(</span><span class="n">skip</span><span class="p">)</span><span class="o">.</span><span class="n">limit</span><span class="p">(</span><span class="n">limit</span><span class="p">)</span><span class="o">.</span><span class="n">all</span><span class="p">()</span>
        
        <span class="k">return</span> <span class="p">[</span><span class="n">SecurityCareerPathResponse</span><span class="o">.</span><span class="n">from_orm</span><span class="p">(</span><span class="n">path</span><span class="p">)</span> <span class="k">for</span> <span class="n">path</span> <span class="ow">in</span> <span class="n">career_paths</span><span class="p">]</span>
    
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;Error retrieving career paths: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="get_security_career_path">
<a class="viewcode-back" href="../../../api/index.html#api.v1.security_career_framework.get_security_career_path">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/career-paths/</span><span class="si">{path_id}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">SecurityCareerPathResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_security_career_path</span><span class="p">(</span>
    <span class="n">path_id</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Career path ID&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get a specific security career path by ID.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">career_path</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">SecurityCareerPath</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
            <span class="n">SecurityCareerPath</span><span class="o">.</span><span class="n">id</span> <span class="o">==</span> <span class="n">path_id</span><span class="p">,</span>
            <span class="n">SecurityCareerPath</span><span class="o">.</span><span class="n">is_active</span> <span class="o">==</span> <span class="kc">True</span>
        <span class="p">)</span><span class="o">.</span><span class="n">first</span><span class="p">()</span>
        
        <span class="k">if</span> <span class="ow">not</span> <span class="n">career_path</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Career path not found&quot;</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="n">SecurityCareerPathResponse</span><span class="o">.</span><span class="n">from_orm</span><span class="p">(</span><span class="n">career_path</span><span class="p">)</span>
    
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;Error retrieving career path: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span></div>



<span class="c1"># Security Skill Matrix endpoints</span>

<div class="viewcode-block" id="get_security_skill_matrix">
<a class="viewcode-back" href="../../../api/index.html#api.v1.security_career_framework.get_security_skill_matrix">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/skill-matrix&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">List</span><span class="p">[</span><span class="n">SecuritySkillMatrixResponse</span><span class="p">])</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_security_skill_matrix</span><span class="p">(</span>
    <span class="n">security_area</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">SecurityAreaEnum</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by security area&quot;</span><span class="p">),</span>
    <span class="n">job_family</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by job family&quot;</span><span class="p">),</span>
    <span class="n">seniority_level</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">SeniorityLevelEnum</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by seniority level&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get security skill matrix with optional filtering.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">query</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">SecuritySkillMatrix</span><span class="p">)</span>
        
        <span class="k">if</span> <span class="n">security_area</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">SecuritySkillMatrix</span><span class="o">.</span><span class="n">security_area</span> <span class="o">==</span> <span class="n">security_area</span><span class="o">.</span><span class="n">value</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">job_family</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">SecuritySkillMatrix</span><span class="o">.</span><span class="n">job_family</span> <span class="o">==</span> <span class="n">job_family</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">seniority_level</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">SecuritySkillMatrix</span><span class="o">.</span><span class="n">seniority_level</span> <span class="o">==</span> <span class="n">seniority_level</span><span class="o">.</span><span class="n">value</span><span class="p">)</span>
        
        <span class="n">skill_matrices</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">all</span><span class="p">()</span>
        
        <span class="c1"># Convert to response format with computed fields</span>
        <span class="n">responses</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">for</span> <span class="n">matrix</span> <span class="ow">in</span> <span class="n">skill_matrices</span><span class="p">:</span>
            <span class="n">matrix_dict</span> <span class="o">=</span> <span class="n">matrix</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span>
            <span class="n">matrix_dict</span><span class="p">[</span><span class="s1">&#39;skills&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="p">{</span>
                <span class="s1">&#39;core&#39;</span><span class="p">:</span> <span class="n">matrix</span><span class="o">.</span><span class="n">core_skills</span> <span class="ow">or</span> <span class="p">[],</span>
                <span class="s1">&#39;advanced&#39;</span><span class="p">:</span> <span class="n">matrix</span><span class="o">.</span><span class="n">advanced_skills</span> <span class="ow">or</span> <span class="p">[],</span>
                <span class="s1">&#39;leadership&#39;</span><span class="p">:</span> <span class="n">matrix</span><span class="o">.</span><span class="n">leadership_skills</span> <span class="ow">or</span> <span class="p">[],</span>
                <span class="s1">&#39;business&#39;</span><span class="p">:</span> <span class="n">matrix</span><span class="o">.</span><span class="n">business_skills</span> <span class="ow">or</span> <span class="p">[]</span>
            <span class="p">}</span>
            <span class="n">matrix_dict</span><span class="p">[</span><span class="s1">&#39;certifications&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="p">{</span>
                <span class="s1">&#39;entry&#39;</span><span class="p">:</span> <span class="n">matrix</span><span class="o">.</span><span class="n">entry_certifications</span> <span class="ow">or</span> <span class="p">[],</span>
                <span class="s1">&#39;intermediate&#39;</span><span class="p">:</span> <span class="n">matrix</span><span class="o">.</span><span class="n">intermediate_certifications</span> <span class="ow">or</span> <span class="p">[],</span>
                <span class="s1">&#39;advanced&#39;</span><span class="p">:</span> <span class="n">matrix</span><span class="o">.</span><span class="n">advanced_certifications</span> <span class="ow">or</span> <span class="p">[],</span>
                <span class="s1">&#39;expert&#39;</span><span class="p">:</span> <span class="n">matrix</span><span class="o">.</span><span class="n">expert_certifications</span> <span class="ow">or</span> <span class="p">[]</span>
            <span class="p">}</span>
            <span class="n">matrix_dict</span><span class="p">[</span><span class="s1">&#39;tools&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="p">{</span>
                <span class="s1">&#39;required&#39;</span><span class="p">:</span> <span class="n">matrix</span><span class="o">.</span><span class="n">required_tools</span> <span class="ow">or</span> <span class="p">[],</span>
                <span class="s1">&#39;preferred&#39;</span><span class="p">:</span> <span class="n">matrix</span><span class="o">.</span><span class="n">preferred_tools</span> <span class="ow">or</span> <span class="p">[]</span>
            <span class="p">}</span>
            <span class="n">responses</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">SecuritySkillMatrixResponse</span><span class="p">(</span><span class="o">**</span><span class="n">matrix_dict</span><span class="p">))</span>
        
        <span class="k">return</span> <span class="n">responses</span>
    
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;Error retrieving skill matrix: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span></div>



<span class="c1"># Career Recommendations endpoint</span>

<div class="viewcode-block" id="get_career_recommendations">
<a class="viewcode-back" href="../../../api/index.html#api.v1.security_career_framework.get_career_recommendations">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/career-recommendations&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">CareerRecommendationResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_career_recommendations</span><span class="p">(</span>
    <span class="n">request</span><span class="p">:</span> <span class="n">CareerRecommendationRequest</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get personalized career recommendations based on user profile.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="c1"># This is a simplified implementation - in production, this would use</span>
        <span class="c1"># machine learning algorithms to provide personalized recommendations</span>
        
        <span class="c1"># Find matching job types based on experience and preferences</span>
        <span class="n">query</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">SecurityJobType</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">SecurityJobType</span><span class="o">.</span><span class="n">is_active</span> <span class="o">==</span> <span class="kc">True</span><span class="p">)</span>
        
        <span class="k">if</span> <span class="n">request</span><span class="o">.</span><span class="n">security_area</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">SecurityJobType</span><span class="o">.</span><span class="n">security_area</span> <span class="o">==</span> <span class="n">request</span><span class="o">.</span><span class="n">security_area</span><span class="o">.</span><span class="n">value</span><span class="p">)</span>
        
        <span class="k">if</span> <span class="n">request</span><span class="o">.</span><span class="n">target_seniority</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">SecurityJobType</span><span class="o">.</span><span class="n">seniority_level</span> <span class="o">==</span> <span class="n">request</span><span class="o">.</span><span class="n">target_seniority</span><span class="o">.</span><span class="n">value</span><span class="p">)</span>
        
        <span class="c1"># Filter by experience level</span>
        <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">SecurityJobType</span><span class="o">.</span><span class="n">min_years_experience</span> <span class="o">&lt;=</span> <span class="n">request</span><span class="o">.</span><span class="n">years_experience</span><span class="p">)</span>
        
        <span class="k">if</span> <span class="n">request</span><span class="o">.</span><span class="n">salary_expectations</span> <span class="ow">and</span> <span class="n">request</span><span class="o">.</span><span class="n">salary_expectations</span><span class="o">.</span><span class="n">min</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">SecurityJobType</span><span class="o">.</span><span class="n">salary_min</span> <span class="o">&gt;=</span> <span class="n">request</span><span class="o">.</span><span class="n">salary_expectations</span><span class="o">.</span><span class="n">min</span><span class="p">)</span>
        
        <span class="n">recommended_jobs</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">limit</span><span class="p">(</span><span class="mi">10</span><span class="p">)</span><span class="o">.</span><span class="n">all</span><span class="p">()</span>
        
        <span class="c1"># Get relevant career paths</span>
        <span class="n">career_paths_query</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">SecurityCareerPath</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">SecurityCareerPath</span><span class="o">.</span><span class="n">is_active</span> <span class="o">==</span> <span class="kc">True</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">request</span><span class="o">.</span><span class="n">security_area</span><span class="p">:</span>
            <span class="n">career_paths_query</span> <span class="o">=</span> <span class="n">career_paths_query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
                <span class="n">SecurityCareerPath</span><span class="o">.</span><span class="n">security_area</span> <span class="o">==</span> <span class="n">request</span><span class="o">.</span><span class="n">security_area</span><span class="o">.</span><span class="n">value</span>
            <span class="p">)</span>
        
        <span class="n">career_paths</span> <span class="o">=</span> <span class="n">career_paths_query</span><span class="o">.</span><span class="n">limit</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span><span class="o">.</span><span class="n">all</span><span class="p">()</span>
        
        <span class="c1"># Analyze skill gaps (simplified)</span>
        <span class="n">skill_gaps</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">if</span> <span class="n">recommended_jobs</span><span class="p">:</span>
            <span class="n">all_required_skills</span> <span class="o">=</span> <span class="nb">set</span><span class="p">()</span>
            <span class="k">for</span> <span class="n">job</span> <span class="ow">in</span> <span class="n">recommended_jobs</span><span class="p">:</span>
                <span class="n">all_required_skills</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="n">job</span><span class="o">.</span><span class="n">required_skills</span> <span class="ow">or</span> <span class="p">[])</span>
            
            <span class="n">user_skills</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">request</span><span class="o">.</span><span class="n">preferred_skills</span><span class="p">)</span>
            <span class="n">skill_gaps</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">all_required_skills</span> <span class="o">-</span> <span class="n">user_skills</span><span class="p">)</span>
        
        <span class="c1"># Certification recommendations</span>
        <span class="n">cert_recommendations</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">if</span> <span class="n">recommended_jobs</span><span class="p">:</span>
            <span class="n">all_certs</span> <span class="o">=</span> <span class="nb">set</span><span class="p">()</span>
            <span class="k">for</span> <span class="n">job</span> <span class="ow">in</span> <span class="n">recommended_jobs</span><span class="p">:</span>
                <span class="n">all_certs</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="n">job</span><span class="o">.</span><span class="n">required_certifications</span> <span class="ow">or</span> <span class="p">[])</span>
                <span class="n">all_certs</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="n">job</span><span class="o">.</span><span class="n">preferred_certifications</span> <span class="ow">or</span> <span class="p">[])</span>
            
            <span class="n">user_certs</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">request</span><span class="o">.</span><span class="n">current_certifications</span><span class="p">)</span>
            <span class="n">cert_recommendations</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">all_certs</span> <span class="o">-</span> <span class="n">user_certs</span><span class="p">)</span>
        
        <span class="c1"># Convert to response format</span>
        <span class="n">job_responses</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">for</span> <span class="n">job</span> <span class="ow">in</span> <span class="n">recommended_jobs</span><span class="p">:</span>
            <span class="n">job_dict</span> <span class="o">=</span> <span class="n">job</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span>
            <span class="n">job_dict</span><span class="p">[</span><span class="s1">&#39;salary_range&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="p">{</span>
                <span class="s1">&#39;min&#39;</span><span class="p">:</span> <span class="n">job</span><span class="o">.</span><span class="n">salary_min</span><span class="p">,</span>
                <span class="s1">&#39;max&#39;</span><span class="p">:</span> <span class="n">job</span><span class="o">.</span><span class="n">salary_max</span><span class="p">,</span>
                <span class="s1">&#39;currency&#39;</span><span class="p">:</span> <span class="n">job</span><span class="o">.</span><span class="n">salary_currency</span>
            <span class="p">}</span>
            <span class="n">job_dict</span><span class="p">[</span><span class="s1">&#39;career_progression&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="p">{</span>
                <span class="s1">&#39;from_roles&#39;</span><span class="p">:</span> <span class="n">job</span><span class="o">.</span><span class="n">career_progression_from</span> <span class="ow">or</span> <span class="p">[],</span>
                <span class="s1">&#39;to_roles&#39;</span><span class="p">:</span> <span class="n">job</span><span class="o">.</span><span class="n">career_progression_to</span> <span class="ow">or</span> <span class="p">[]</span>
            <span class="p">}</span>
            <span class="n">job_responses</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">SecurityJobTypeResponse</span><span class="p">(</span><span class="o">**</span><span class="n">job_dict</span><span class="p">))</span>
        
        <span class="n">path_responses</span> <span class="o">=</span> <span class="p">[</span><span class="n">SecurityCareerPathResponse</span><span class="o">.</span><span class="n">from_orm</span><span class="p">(</span><span class="n">path</span><span class="p">)</span> <span class="k">for</span> <span class="n">path</span> <span class="ow">in</span> <span class="n">career_paths</span><span class="p">]</span>
        
        <span class="k">return</span> <span class="n">CareerRecommendationResponse</span><span class="p">(</span>
            <span class="n">recommended_roles</span><span class="o">=</span><span class="n">job_responses</span><span class="p">,</span>
            <span class="n">career_paths</span><span class="o">=</span><span class="n">path_responses</span><span class="p">,</span>
            <span class="n">skill_gaps</span><span class="o">=</span><span class="n">skill_gaps</span><span class="p">[:</span><span class="mi">10</span><span class="p">],</span>  <span class="c1"># Top 10 skill gaps</span>
            <span class="n">certification_recommendations</span><span class="o">=</span><span class="n">cert_recommendations</span><span class="p">[:</span><span class="mi">10</span><span class="p">],</span>  <span class="c1"># Top 10 certs</span>
            <span class="n">market_insights</span><span class="o">=</span><span class="p">{</span>
                <span class="s1">&#39;total_opportunities&#39;</span><span class="p">:</span> <span class="nb">len</span><span class="p">(</span><span class="n">recommended_jobs</span><span class="p">),</span>
                <span class="s1">&#39;average_salary&#39;</span><span class="p">:</span> <span class="nb">sum</span><span class="p">(</span><span class="n">job</span><span class="o">.</span><span class="n">salary_min</span> <span class="ow">or</span> <span class="mi">0</span> <span class="k">for</span> <span class="n">job</span> <span class="ow">in</span> <span class="n">recommended_jobs</span><span class="p">)</span> <span class="o">/</span> <span class="nb">len</span><span class="p">(</span><span class="n">recommended_jobs</span><span class="p">)</span> <span class="k">if</span> <span class="n">recommended_jobs</span> <span class="k">else</span> <span class="mi">0</span><span class="p">,</span>
                <span class="s1">&#39;remote_friendly_percentage&#39;</span><span class="p">:</span> <span class="nb">sum</span><span class="p">(</span><span class="mi">1</span> <span class="k">for</span> <span class="n">job</span> <span class="ow">in</span> <span class="n">recommended_jobs</span> <span class="k">if</span> <span class="n">job</span><span class="o">.</span><span class="n">remote_friendly</span><span class="p">)</span> <span class="o">/</span> <span class="nb">len</span><span class="p">(</span><span class="n">recommended_jobs</span><span class="p">)</span> <span class="o">*</span> <span class="mi">100</span> <span class="k">if</span> <span class="n">recommended_jobs</span> <span class="k">else</span> <span class="mi">0</span>
            <span class="p">},</span>
            <span class="n">next_steps</span><span class="o">=</span><span class="p">[</span>
                <span class="s2">&quot;Review recommended job roles and their requirements&quot;</span><span class="p">,</span>
                <span class="s2">&quot;Identify skill gaps and create a learning plan&quot;</span><span class="p">,</span>
                <span class="s2">&quot;Pursue recommended certifications&quot;</span><span class="p">,</span>
                <span class="s2">&quot;Build a portfolio showcasing relevant skills&quot;</span><span class="p">,</span>
                <span class="s2">&quot;Network with professionals in your target security area&quot;</span>
            <span class="p">]</span>
        <span class="p">)</span>
    
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;Error generating recommendations: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span></div>



<span class="c1"># Analytics endpoints</span>

<div class="viewcode-block" id="get_security_career_analytics">
<a class="viewcode-back" href="../../../api/index.html#api.v1.security_career_framework.get_security_career_analytics">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/analytics&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">SecurityCareerAnalytics</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_security_career_analytics</span><span class="p">(</span><span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get comprehensive analytics for security careers.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="c1"># Get basic counts</span>
        <span class="n">total_job_types</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">SecurityJobType</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">SecurityJobType</span><span class="o">.</span><span class="n">is_active</span> <span class="o">==</span> <span class="kc">True</span><span class="p">)</span><span class="o">.</span><span class="n">count</span><span class="p">()</span>
        <span class="n">total_career_paths</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">SecurityCareerPath</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">SecurityCareerPath</span><span class="o">.</span><span class="n">is_active</span> <span class="o">==</span> <span class="kc">True</span><span class="p">)</span><span class="o">.</span><span class="n">count</span><span class="p">()</span>
        
        <span class="c1"># Get security area summaries</span>
        <span class="n">area_summaries</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">for</span> <span class="n">area</span> <span class="ow">in</span> <span class="n">SecurityAreaEnum</span><span class="p">:</span>
            <span class="n">area_jobs</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">SecurityJobType</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
                <span class="n">SecurityJobType</span><span class="o">.</span><span class="n">security_area</span> <span class="o">==</span> <span class="n">area</span><span class="o">.</span><span class="n">value</span><span class="p">,</span>
                <span class="n">SecurityJobType</span><span class="o">.</span><span class="n">is_active</span> <span class="o">==</span> <span class="kc">True</span>
            <span class="p">)</span><span class="o">.</span><span class="n">all</span><span class="p">()</span>
            
            <span class="k">if</span> <span class="n">area_jobs</span><span class="p">:</span>
                <span class="n">avg_salary</span> <span class="o">=</span> <span class="nb">sum</span><span class="p">(</span><span class="n">job</span><span class="o">.</span><span class="n">salary_min</span> <span class="ow">or</span> <span class="mi">0</span> <span class="k">for</span> <span class="n">job</span> <span class="ow">in</span> <span class="n">area_jobs</span><span class="p">)</span> <span class="o">/</span> <span class="nb">len</span><span class="p">(</span><span class="n">area_jobs</span><span class="p">)</span>
                <span class="n">demand_counts</span> <span class="o">=</span> <span class="p">{}</span>
                <span class="k">for</span> <span class="n">job</span> <span class="ow">in</span> <span class="n">area_jobs</span><span class="p">:</span>
                    <span class="n">demand_counts</span><span class="p">[</span><span class="n">job</span><span class="o">.</span><span class="n">demand_level</span><span class="p">]</span> <span class="o">=</span> <span class="n">demand_counts</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">job</span><span class="o">.</span><span class="n">demand_level</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span> <span class="o">+</span> <span class="mi">1</span>
                
                <span class="n">overall_demand</span> <span class="o">=</span> <span class="nb">max</span><span class="p">(</span><span class="n">demand_counts</span><span class="o">.</span><span class="n">items</span><span class="p">(),</span> <span class="n">key</span><span class="o">=</span><span class="k">lambda</span> <span class="n">x</span><span class="p">:</span> <span class="n">x</span><span class="p">[</span><span class="mi">1</span><span class="p">])[</span><span class="mi">0</span><span class="p">]</span> <span class="k">if</span> <span class="n">demand_counts</span> <span class="k">else</span> <span class="s1">&#39;medium&#39;</span>
                
                <span class="c1"># Get top skills</span>
                <span class="n">all_skills</span> <span class="o">=</span> <span class="p">[]</span>
                <span class="k">for</span> <span class="n">job</span> <span class="ow">in</span> <span class="n">area_jobs</span><span class="p">:</span>
                    <span class="n">all_skills</span><span class="o">.</span><span class="n">extend</span><span class="p">(</span><span class="n">job</span><span class="o">.</span><span class="n">required_skills</span> <span class="ow">or</span> <span class="p">[])</span>
                
                <span class="n">skill_counts</span> <span class="o">=</span> <span class="p">{}</span>
                <span class="k">for</span> <span class="n">skill</span> <span class="ow">in</span> <span class="n">all_skills</span><span class="p">:</span>
                    <span class="n">skill_counts</span><span class="p">[</span><span class="n">skill</span><span class="p">]</span> <span class="o">=</span> <span class="n">skill_counts</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">skill</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span> <span class="o">+</span> <span class="mi">1</span>
                
                <span class="n">top_skills</span> <span class="o">=</span> <span class="nb">sorted</span><span class="p">(</span><span class="n">skill_counts</span><span class="o">.</span><span class="n">items</span><span class="p">(),</span> <span class="n">key</span><span class="o">=</span><span class="k">lambda</span> <span class="n">x</span><span class="p">:</span> <span class="n">x</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span> <span class="n">reverse</span><span class="o">=</span><span class="kc">True</span><span class="p">)[:</span><span class="mi">5</span><span class="p">]</span>
                <span class="n">top_skills</span> <span class="o">=</span> <span class="p">[</span><span class="n">skill</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="k">for</span> <span class="n">skill</span> <span class="ow">in</span> <span class="n">top_skills</span><span class="p">]</span>
                
                <span class="n">area_summaries</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">SecurityAreaSummary</span><span class="p">(</span>
                    <span class="n">security_area</span><span class="o">=</span><span class="n">area</span><span class="p">,</span>
                    <span class="n">total_job_types</span><span class="o">=</span><span class="nb">len</span><span class="p">(</span><span class="n">area_jobs</span><span class="p">),</span>
                    <span class="n">average_salary</span><span class="o">=</span><span class="n">avg_salary</span><span class="p">,</span>
                    <span class="n">demand_level</span><span class="o">=</span><span class="n">DemandLevelEnum</span><span class="p">(</span><span class="n">overall_demand</span><span class="p">),</span>
                    <span class="n">growth_rate</span><span class="o">=</span><span class="mf">5.0</span><span class="p">,</span>  <span class="c1"># Mock data</span>
                    <span class="n">top_skills</span><span class="o">=</span><span class="n">top_skills</span><span class="p">,</span>
                    <span class="n">top_certifications</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;Security+&#39;</span><span class="p">,</span> <span class="s1">&#39;CISSP&#39;</span><span class="p">],</span>  <span class="c1"># Mock data</span>
                    <span class="n">remote_work_percentage</span><span class="o">=</span><span class="mf">75.0</span>  <span class="c1"># Mock data</span>
                <span class="p">))</span>
        
        <span class="k">return</span> <span class="n">SecurityCareerAnalytics</span><span class="p">(</span>
            <span class="n">total_job_types</span><span class="o">=</span><span class="n">total_job_types</span><span class="p">,</span>
            <span class="n">total_career_paths</span><span class="o">=</span><span class="n">total_career_paths</span><span class="p">,</span>
            <span class="n">security_area_summaries</span><span class="o">=</span><span class="n">area_summaries</span><span class="p">,</span>
            <span class="n">trending_skills</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;Cloud Security&#39;</span><span class="p">,</span> <span class="s1">&#39;DevSecOps&#39;</span><span class="p">,</span> <span class="s1">&#39;AI/ML Security&#39;</span><span class="p">],</span>
            <span class="n">highest_demand_roles</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;SOC Analyst&#39;</span><span class="p">,</span> <span class="s1">&#39;Security Engineer&#39;</span><span class="p">,</span> <span class="s1">&#39;Penetration Tester&#39;</span><span class="p">],</span>
            <span class="n">highest_paying_roles</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;CISO&#39;</span><span class="p">,</span> <span class="s1">&#39;Security Architect&#39;</span><span class="p">,</span> <span class="s1">&#39;Principal Security Engineer&#39;</span><span class="p">],</span>
            <span class="n">fastest_growing_areas</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;Cloud Security&#39;</span><span class="p">,</span> <span class="s1">&#39;DevSecOps&#39;</span><span class="p">,</span> <span class="s1">&#39;AI Security&#39;</span><span class="p">],</span>
            <span class="n">certification_popularity</span><span class="o">=</span><span class="p">{</span><span class="s1">&#39;Security+&#39;</span><span class="p">:</span> <span class="mi">100</span><span class="p">,</span> <span class="s1">&#39;CISSP&#39;</span><span class="p">:</span> <span class="mi">85</span><span class="p">,</span> <span class="s1">&#39;CySA+&#39;</span><span class="p">:</span> <span class="mi">70</span><span class="p">},</span>
            <span class="n">market_trends</span><span class="o">=</span><span class="p">{</span><span class="s1">&#39;remote_work_adoption&#39;</span><span class="p">:</span> <span class="mf">80.0</span><span class="p">,</span> <span class="s1">&#39;salary_growth&#39;</span><span class="p">:</span> <span class="mf">8.5</span><span class="p">},</span>
            <span class="n">generated_at</span><span class="o">=</span><span class="n">datetime</span><span class="o">.</span><span class="n">utcnow</span><span class="p">()</span>
        <span class="p">)</span>
    
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;Error generating analytics: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span></div>



<span class="c1"># Utility endpoints</span>

<div class="viewcode-block" id="get_security_areas">
<a class="viewcode-back" href="../../../api/index.html#api.v1.security_career_framework.get_security_areas">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/security-areas&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">])</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_security_areas</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get list of all security areas.&quot;&quot;&quot;</span>
    <span class="k">return</span> <span class="p">[</span><span class="n">area</span><span class="o">.</span><span class="n">value</span> <span class="k">for</span> <span class="n">area</span> <span class="ow">in</span> <span class="n">SecurityAreaEnum</span><span class="p">]</span></div>



<div class="viewcode-block" id="get_seniority_levels">
<a class="viewcode-back" href="../../../api/index.html#api.v1.security_career_framework.get_seniority_levels">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/seniority-levels&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">])</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_seniority_levels</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get list of all seniority levels.&quot;&quot;&quot;</span>
    <span class="k">return</span> <span class="p">[</span><span class="n">level</span><span class="o">.</span><span class="n">value</span> <span class="k">for</span> <span class="n">level</span> <span class="ow">in</span> <span class="n">SeniorityLevelEnum</span><span class="p">]</span></div>



<div class="viewcode-block" id="get_job_families_by_area">
<a class="viewcode-back" href="../../../api/index.html#api.v1.security_career_framework.get_job_families_by_area">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/job-families/</span><span class="si">{security_area}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">])</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_job_families_by_area</span><span class="p">(</span>
    <span class="n">security_area</span><span class="p">:</span> <span class="n">SecurityAreaEnum</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Security area&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get job families for a specific security area.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">job_families</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">SecurityJobType</span><span class="o">.</span><span class="n">job_family</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
            <span class="n">SecurityJobType</span><span class="o">.</span><span class="n">security_area</span> <span class="o">==</span> <span class="n">security_area</span><span class="o">.</span><span class="n">value</span><span class="p">,</span>
            <span class="n">SecurityJobType</span><span class="o">.</span><span class="n">is_active</span> <span class="o">==</span> <span class="kc">True</span>
        <span class="p">)</span><span class="o">.</span><span class="n">distinct</span><span class="p">()</span><span class="o">.</span><span class="n">all</span><span class="p">()</span>
        
        <span class="k">return</span> <span class="p">[</span><span class="n">family</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="k">for</span> <span class="n">family</span> <span class="ow">in</span> <span class="n">job_families</span><span class="p">]</span>
    
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;Error retrieving job families: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span></div>

</pre></div>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, CertPathFinder Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>