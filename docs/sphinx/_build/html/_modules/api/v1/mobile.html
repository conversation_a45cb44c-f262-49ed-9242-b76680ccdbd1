

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../../../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>api.v1.mobile &mdash; CertPathFinder 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../../../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../../../_static/custom.css?v=c14262df" />

  
      <script src="../../../_static/jquery.js?v=5d32c60e"></script>
      <script src="../../../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../../../_static/documentation_options.js?v=8d563738"></script>
      <script src="../../../_static/doctools.js?v=9bcbadda"></script>
      <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../../../index.html" class="icon icon-home">
            CertPathFinder
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../quickstart.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../api/index.html">API Reference</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../../index.html">CertPathFinder</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../../index.html">Module code</a></li>
      <li class="breadcrumb-item active">api.v1.mobile</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>Source code for api.v1.mobile</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;FastAPI endpoints for mobile enterprise applications.</span>

<span class="sd">This module provides comprehensive mobile API endpoints with offline capabilities,</span>
<span class="sd">synchronization, push notifications, and enterprise mobile device management</span>
<span class="sd">for iOS and Android applications.</span>
<span class="sd">&quot;&quot;&quot;</span>

<span class="kn">import</span><span class="w"> </span><span class="nn">logging</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">typing</span><span class="w"> </span><span class="kn">import</span> <span class="n">List</span><span class="p">,</span> <span class="n">Optional</span><span class="p">,</span> <span class="n">Dict</span><span class="p">,</span> <span class="n">Any</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">fastapi</span><span class="w"> </span><span class="kn">import</span> <span class="n">APIRouter</span><span class="p">,</span> <span class="n">Depends</span><span class="p">,</span> <span class="n">HTTPException</span><span class="p">,</span> <span class="n">Query</span><span class="p">,</span> <span class="n">Path</span><span class="p">,</span> <span class="n">status</span><span class="p">,</span> <span class="n">Header</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">sqlalchemy.orm</span><span class="w"> </span><span class="kn">import</span> <span class="n">Session</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">datetime</span><span class="w"> </span><span class="kn">import</span> <span class="n">datetime</span><span class="p">,</span> <span class="n">timedelta</span>

<span class="kn">from</span><span class="w"> </span><span class="nn">database</span><span class="w"> </span><span class="kn">import</span> <span class="n">get_db</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">mobile.mobile_api_service</span><span class="w"> </span><span class="kn">import</span> <span class="n">MobileAPIService</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">schemas.mobile</span><span class="w"> </span><span class="kn">import</span> <span class="p">(</span>
    <span class="n">DeviceRegistrationRequest</span><span class="p">,</span> <span class="n">DeviceRegistrationResponse</span><span class="p">,</span>
    <span class="n">MobileAuthRequest</span><span class="p">,</span> <span class="n">MobileAuthResponse</span><span class="p">,</span>
    <span class="n">OfflineDataRequest</span><span class="p">,</span> <span class="n">OfflineDataResponse</span><span class="p">,</span>
    <span class="n">SyncRequest</span><span class="p">,</span> <span class="n">SyncResponse</span><span class="p">,</span>
    <span class="n">PushNotificationRequest</span><span class="p">,</span> <span class="n">PushNotificationResponse</span><span class="p">,</span>
    <span class="n">MobileAnalyticsRequest</span><span class="p">,</span> <span class="n">MobileAnalyticsResponse</span><span class="p">,</span>
    <span class="n">MobileDashboardResponse</span><span class="p">,</span> <span class="n">MobileHealthResponse</span>
<span class="p">)</span>

<span class="n">logger</span> <span class="o">=</span> <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="vm">__name__</span><span class="p">)</span>
<span class="n">router</span> <span class="o">=</span> <span class="n">APIRouter</span><span class="p">(</span><span class="n">prefix</span><span class="o">=</span><span class="s2">&quot;/mobile&quot;</span><span class="p">,</span> <span class="n">tags</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;Mobile Enterprise&quot;</span><span class="p">])</span>


<div class="viewcode-block" id="get_current_mobile_user">
<a class="viewcode-back" href="../../../api/index.html#api.v1.mobile.get_current_mobile_user">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">get_current_mobile_user</span><span class="p">(</span><span class="n">authorization</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Header</span><span class="p">(</span><span class="kc">None</span><span class="p">))</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get current mobile user from session token.&quot;&quot;&quot;</span>
    <span class="k">if</span> <span class="ow">not</span> <span class="n">authorization</span> <span class="ow">or</span> <span class="ow">not</span> <span class="n">authorization</span><span class="o">.</span><span class="n">startswith</span><span class="p">(</span><span class="s2">&quot;Bearer &quot;</span><span class="p">):</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">401</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Invalid authorization header&quot;</span><span class="p">)</span>
    
    <span class="c1"># TODO: Implement proper token validation</span>
    <span class="c1"># For now, extract user_id from token (simplified)</span>
    <span class="n">token</span> <span class="o">=</span> <span class="n">authorization</span><span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="s2">&quot;Bearer &quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">)</span>
    <span class="c1"># In production, would validate JWT token and extract user_id</span>
    <span class="k">return</span> <span class="s2">&quot;mobile_user_1&quot;</span></div>



<div class="viewcode-block" id="get_device_id">
<a class="viewcode-back" href="../../../api/index.html#api.v1.mobile.get_device_id">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">get_device_id</span><span class="p">(</span><span class="n">x_device_id</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Header</span><span class="p">(</span><span class="kc">None</span><span class="p">))</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get device ID from request headers.&quot;&quot;&quot;</span>
    <span class="k">if</span> <span class="ow">not</span> <span class="n">x_device_id</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">400</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Device ID header required&quot;</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">x_device_id</span></div>



<span class="c1"># Device Registration and Authentication</span>

<div class="viewcode-block" id="register_mobile_device">
<a class="viewcode-back" href="../../../api/index.html#api.v1.mobile.register_mobile_device">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/register-device&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">DeviceRegistrationResponse</span><span class="p">,</span> <span class="n">status_code</span><span class="o">=</span><span class="n">status</span><span class="o">.</span><span class="n">HTTP_201_CREATED</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">register_mobile_device</span><span class="p">(</span>
    <span class="n">registration_request</span><span class="p">:</span> <span class="n">DeviceRegistrationRequest</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Register a mobile device for enterprise access.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Registering mobile device for user </span><span class="si">{</span><span class="n">registration_request</span><span class="o">.</span><span class="n">user_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">mobile_service</span> <span class="o">=</span> <span class="n">MobileAPIService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        <span class="n">result</span> <span class="o">=</span> <span class="n">mobile_service</span><span class="o">.</span><span class="n">register_mobile_device</span><span class="p">(</span>
            <span class="n">user_id</span><span class="o">=</span><span class="n">registration_request</span><span class="o">.</span><span class="n">user_id</span><span class="p">,</span>
            <span class="n">device_data</span><span class="o">=</span><span class="n">registration_request</span><span class="o">.</span><span class="n">dict</span><span class="p">()</span>
        <span class="p">)</span>
        
        <span class="k">if</span> <span class="s1">&#39;error&#39;</span> <span class="ow">in</span> <span class="n">result</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">400</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;error&#39;</span><span class="p">])</span>
        
        <span class="k">return</span> <span class="n">DeviceRegistrationResponse</span><span class="p">(</span><span class="o">**</span><span class="n">result</span><span class="p">)</span>
        
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error registering mobile device: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error registering mobile device&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="authenticate_mobile_user">
<a class="viewcode-back" href="../../../api/index.html#api.v1.mobile.authenticate_mobile_user">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/authenticate&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">MobileAuthResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">authenticate_mobile_user</span><span class="p">(</span>
    <span class="n">auth_request</span><span class="p">:</span> <span class="n">MobileAuthRequest</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Authenticate user for mobile application access.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Authenticating mobile user: </span><span class="si">{</span><span class="n">auth_request</span><span class="o">.</span><span class="n">email</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">mobile_service</span> <span class="o">=</span> <span class="n">MobileAPIService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        <span class="n">result</span> <span class="o">=</span> <span class="n">mobile_service</span><span class="o">.</span><span class="n">authenticate_mobile_user</span><span class="p">(</span><span class="n">auth_request</span><span class="o">.</span><span class="n">dict</span><span class="p">())</span>
        
        <span class="k">if</span> <span class="s1">&#39;error&#39;</span> <span class="ow">in</span> <span class="n">result</span><span class="p">:</span>
            <span class="n">error_code</span> <span class="o">=</span> <span class="n">result</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;error_code&#39;</span><span class="p">,</span> <span class="s1">&#39;AUTH_ERROR&#39;</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">error_code</span> <span class="o">==</span> <span class="s1">&#39;AUTH_FAILED&#39;</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">401</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;error&#39;</span><span class="p">])</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">400</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;error&#39;</span><span class="p">])</span>
        
        <span class="k">return</span> <span class="n">MobileAuthResponse</span><span class="p">(</span><span class="o">**</span><span class="n">result</span><span class="p">)</span>
        
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error authenticating mobile user: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error authenticating mobile user&quot;</span><span class="p">)</span></div>



<span class="c1"># Offline Data Management</span>

<div class="viewcode-block" id="get_offline_data_package">
<a class="viewcode-back" href="../../../api/index.html#api.v1.mobile.get_offline_data_package">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/offline-data&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">OfflineDataResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_offline_data_package</span><span class="p">(</span>
    <span class="n">offline_request</span><span class="p">:</span> <span class="n">OfflineDataRequest</span><span class="p">,</span>
    <span class="n">user_id</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_mobile_user</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get comprehensive offline data package for mobile app.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Preparing offline data package for user </span><span class="si">{</span><span class="n">user_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">mobile_service</span> <span class="o">=</span> <span class="n">MobileAPIService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        <span class="n">result</span> <span class="o">=</span> <span class="n">mobile_service</span><span class="o">.</span><span class="n">prepare_offline_data_package</span><span class="p">(</span>
            <span class="n">user_id</span><span class="o">=</span><span class="n">user_id</span><span class="p">,</span>
            <span class="n">sync_token</span><span class="o">=</span><span class="n">offline_request</span><span class="o">.</span><span class="n">sync_token</span>
        <span class="p">)</span>
        
        <span class="k">if</span> <span class="s1">&#39;error&#39;</span> <span class="ow">in</span> <span class="n">result</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">400</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;error&#39;</span><span class="p">])</span>
        
        <span class="k">return</span> <span class="n">OfflineDataResponse</span><span class="p">(</span><span class="o">**</span><span class="n">result</span><span class="p">)</span>
        
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error preparing offline data package: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error preparing offline data package&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="sync_mobile_data">
<a class="viewcode-back" href="../../../api/index.html#api.v1.mobile.sync_mobile_data">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/sync&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">SyncResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">sync_mobile_data</span><span class="p">(</span>
    <span class="n">sync_request</span><span class="p">:</span> <span class="n">SyncRequest</span><span class="p">,</span>
    <span class="n">user_id</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_mobile_user</span><span class="p">),</span>
    <span class="n">device_id</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_device_id</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Synchronize mobile app data with server.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Syncing mobile data for user </span><span class="si">{</span><span class="n">user_id</span><span class="si">}</span><span class="s2">, device </span><span class="si">{</span><span class="n">device_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">mobile_service</span> <span class="o">=</span> <span class="n">MobileAPIService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        <span class="n">result</span> <span class="o">=</span> <span class="n">mobile_service</span><span class="o">.</span><span class="n">sync_mobile_data</span><span class="p">(</span>
            <span class="n">user_id</span><span class="o">=</span><span class="n">user_id</span><span class="p">,</span>
            <span class="n">device_id</span><span class="o">=</span><span class="n">device_id</span><span class="p">,</span>
            <span class="n">sync_data</span><span class="o">=</span><span class="n">sync_request</span><span class="o">.</span><span class="n">dict</span><span class="p">()</span>
        <span class="p">)</span>
        
        <span class="k">if</span> <span class="s1">&#39;error&#39;</span> <span class="ow">in</span> <span class="n">result</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">400</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;error&#39;</span><span class="p">])</span>
        
        <span class="k">return</span> <span class="n">SyncResponse</span><span class="p">(</span><span class="o">**</span><span class="n">result</span><span class="p">)</span>
        
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error syncing mobile data: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error syncing mobile data&quot;</span><span class="p">)</span></div>



<span class="c1"># Push Notifications</span>

<div class="viewcode-block" id="send_push_notification">
<a class="viewcode-back" href="../../../api/index.html#api.v1.mobile.send_push_notification">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/notifications/send&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">PushNotificationResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">send_push_notification</span><span class="p">(</span>
    <span class="n">notification_request</span><span class="p">:</span> <span class="n">PushNotificationRequest</span><span class="p">,</span>
    <span class="n">user_id</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_mobile_user</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Send push notification to user&#39;s mobile devices.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Sending push notification to user </span><span class="si">{</span><span class="n">user_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">mobile_service</span> <span class="o">=</span> <span class="n">MobileAPIService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        <span class="n">result</span> <span class="o">=</span> <span class="n">mobile_service</span><span class="o">.</span><span class="n">send_push_notification</span><span class="p">(</span>
            <span class="n">user_id</span><span class="o">=</span><span class="n">user_id</span><span class="p">,</span>
            <span class="n">notification_data</span><span class="o">=</span><span class="n">notification_request</span><span class="o">.</span><span class="n">dict</span><span class="p">()</span>
        <span class="p">)</span>
        
        <span class="k">if</span> <span class="s1">&#39;error&#39;</span> <span class="ow">in</span> <span class="n">result</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">400</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;error&#39;</span><span class="p">])</span>
        
        <span class="k">return</span> <span class="n">PushNotificationResponse</span><span class="p">(</span><span class="o">**</span><span class="n">result</span><span class="p">)</span>
        
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error sending push notification: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error sending push notification&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="schedule_smart_notifications">
<a class="viewcode-back" href="../../../api/index.html#api.v1.mobile.schedule_smart_notifications">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/notifications/schedule-smart&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">schedule_smart_notifications</span><span class="p">(</span>
    <span class="n">user_id</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_mobile_user</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Schedule intelligent notifications based on user behavior and AI insights.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Scheduling smart notifications for user </span><span class="si">{</span><span class="n">user_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">mobile_service</span> <span class="o">=</span> <span class="n">MobileAPIService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        <span class="n">result</span> <span class="o">=</span> <span class="n">mobile_service</span><span class="o">.</span><span class="n">schedule_smart_notifications</span><span class="p">(</span><span class="n">user_id</span><span class="p">)</span>
        
        <span class="k">if</span> <span class="s1">&#39;error&#39;</span> <span class="ow">in</span> <span class="n">result</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">400</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;error&#39;</span><span class="p">])</span>
        
        <span class="k">return</span> <span class="n">result</span>
        
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error scheduling smart notifications: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error scheduling smart notifications&quot;</span><span class="p">)</span></div>



<span class="c1"># Mobile Analytics</span>

<div class="viewcode-block" id="track_mobile_analytics">
<a class="viewcode-back" href="../../../api/index.html#api.v1.mobile.track_mobile_analytics">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/analytics/track&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">MobileAnalyticsResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">track_mobile_analytics</span><span class="p">(</span>
    <span class="n">analytics_request</span><span class="p">:</span> <span class="n">MobileAnalyticsRequest</span><span class="p">,</span>
    <span class="n">user_id</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_mobile_user</span><span class="p">),</span>
    <span class="n">device_id</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_device_id</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Track mobile app usage analytics.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">mobile_service</span> <span class="o">=</span> <span class="n">MobileAPIService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        <span class="n">result</span> <span class="o">=</span> <span class="n">mobile_service</span><span class="o">.</span><span class="n">track_mobile_analytics</span><span class="p">(</span>
            <span class="n">user_id</span><span class="o">=</span><span class="n">user_id</span><span class="p">,</span>
            <span class="n">device_id</span><span class="o">=</span><span class="n">device_id</span><span class="p">,</span>
            <span class="n">analytics_data</span><span class="o">=</span><span class="n">analytics_request</span><span class="o">.</span><span class="n">dict</span><span class="p">()</span>
        <span class="p">)</span>
        
        <span class="k">if</span> <span class="s1">&#39;error&#39;</span> <span class="ow">in</span> <span class="n">result</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">400</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;error&#39;</span><span class="p">])</span>
        
        <span class="k">return</span> <span class="n">MobileAnalyticsResponse</span><span class="p">(</span><span class="o">**</span><span class="n">result</span><span class="p">)</span>
        
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error tracking mobile analytics: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error tracking mobile analytics&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="get_mobile_dashboard">
<a class="viewcode-back" href="../../../api/index.html#api.v1.mobile.get_mobile_dashboard">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/dashboard&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">MobileDashboardResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_mobile_dashboard</span><span class="p">(</span>
    <span class="n">user_id</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_mobile_user</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get mobile-optimized dashboard data.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Getting mobile dashboard data for user </span><span class="si">{</span><span class="n">user_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">mobile_service</span> <span class="o">=</span> <span class="n">MobileAPIService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        <span class="n">result</span> <span class="o">=</span> <span class="n">mobile_service</span><span class="o">.</span><span class="n">get_mobile_dashboard_data</span><span class="p">(</span><span class="n">user_id</span><span class="p">)</span>
        
        <span class="k">if</span> <span class="s1">&#39;error&#39;</span> <span class="ow">in</span> <span class="n">result</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">400</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="n">result</span><span class="p">[</span><span class="s1">&#39;error&#39;</span><span class="p">])</span>
        
        <span class="k">return</span> <span class="n">MobileDashboardResponse</span><span class="p">(</span><span class="o">**</span><span class="n">result</span><span class="p">)</span>
        
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error getting mobile dashboard data: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error getting mobile dashboard data&quot;</span><span class="p">)</span></div>



<span class="c1"># Mobile-Specific Features</span>

<div class="viewcode-block" id="quick_start_study_session">
<a class="viewcode-back" href="../../../api/index.html#api.v1.mobile.quick_start_study_session">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/study-session/quick-start&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">quick_start_study_session</span><span class="p">(</span>
    <span class="n">certification_id</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Specific certification to study&quot;</span><span class="p">),</span>
    <span class="n">duration_minutes</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">30</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">5</span><span class="p">,</span> <span class="n">le</span><span class="o">=</span><span class="mi">180</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Study session duration&quot;</span><span class="p">),</span>
    <span class="n">user_id</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_mobile_user</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Quick start a mobile-optimized study session.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Quick starting study session for user </span><span class="si">{</span><span class="n">user_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="c1"># Create optimized study session for mobile</span>
        <span class="n">session_data</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s1">&#39;session_id&#39;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;mobile_</span><span class="si">{</span><span class="n">user_id</span><span class="si">}</span><span class="s2">_</span><span class="si">{</span><span class="n">datetime</span><span class="o">.</span><span class="n">utcnow</span><span class="p">()</span><span class="o">.</span><span class="n">strftime</span><span class="p">(</span><span class="s1">&#39;%Y%m</span><span class="si">%d</span><span class="s1">_%H%M%S&#39;</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
            <span class="s1">&#39;user_id&#39;</span><span class="p">:</span> <span class="n">user_id</span><span class="p">,</span>
            <span class="s1">&#39;certification_id&#39;</span><span class="p">:</span> <span class="n">certification_id</span><span class="p">,</span>
            <span class="s1">&#39;duration_minutes&#39;</span><span class="p">:</span> <span class="n">duration_minutes</span><span class="p">,</span>
            <span class="s1">&#39;session_type&#39;</span><span class="p">:</span> <span class="s1">&#39;mobile_quick_study&#39;</span><span class="p">,</span>
            <span class="s1">&#39;started_at&#39;</span><span class="p">:</span> <span class="n">datetime</span><span class="o">.</span><span class="n">utcnow</span><span class="p">()</span><span class="o">.</span><span class="n">isoformat</span><span class="p">(),</span>
            <span class="s1">&#39;mobile_optimized&#39;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
            <span class="s1">&#39;offline_capable&#39;</span><span class="p">:</span> <span class="kc">True</span>
        <span class="p">}</span>
        
        <span class="c1"># Get study materials optimized for mobile</span>
        <span class="n">mobile_service</span> <span class="o">=</span> <span class="n">MobileAPIService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        <span class="n">study_materials</span> <span class="o">=</span> <span class="n">mobile_service</span><span class="o">.</span><span class="n">_get_mobile_study_materials</span><span class="p">(</span><span class="n">user_id</span><span class="p">,</span> <span class="n">certification_id</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="p">{</span>
            <span class="s1">&#39;session_data&#39;</span><span class="p">:</span> <span class="n">session_data</span><span class="p">,</span>
            <span class="s1">&#39;study_materials&#39;</span><span class="p">:</span> <span class="n">study_materials</span><span class="p">,</span>
            <span class="s1">&#39;estimated_completion&#39;</span><span class="p">:</span> <span class="p">(</span><span class="n">datetime</span><span class="o">.</span><span class="n">utcnow</span><span class="p">()</span> <span class="o">+</span> <span class="n">timedelta</span><span class="p">(</span><span class="n">minutes</span><span class="o">=</span><span class="n">duration_minutes</span><span class="p">))</span><span class="o">.</span><span class="n">isoformat</span><span class="p">(),</span>
            <span class="s1">&#39;offline_available&#39;</span><span class="p">:</span> <span class="kc">True</span>
        <span class="p">}</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error starting quick study session: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error starting study session&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="get_mobile_practice_test">
<a class="viewcode-back" href="../../../api/index.html#api.v1.mobile.get_mobile_practice_test">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/practice-test/mobile&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_mobile_practice_test</span><span class="p">(</span>
    <span class="n">certification_id</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Certification ID&quot;</span><span class="p">),</span>
    <span class="n">question_count</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">10</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">5</span><span class="p">,</span> <span class="n">le</span><span class="o">=</span><span class="mi">50</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Number of questions&quot;</span><span class="p">),</span>
    <span class="n">difficulty</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="s2">&quot;mixed&quot;</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Question difficulty level&quot;</span><span class="p">),</span>
    <span class="n">user_id</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_mobile_user</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get mobile-optimized practice test.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Getting mobile practice test for user </span><span class="si">{</span><span class="n">user_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="c1"># Generate mobile-optimized practice test</span>
        <span class="n">practice_test</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s1">&#39;test_id&#39;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;mobile_test_</span><span class="si">{</span><span class="n">user_id</span><span class="si">}</span><span class="s2">_</span><span class="si">{</span><span class="n">datetime</span><span class="o">.</span><span class="n">utcnow</span><span class="p">()</span><span class="o">.</span><span class="n">strftime</span><span class="p">(</span><span class="s1">&#39;%Y%m</span><span class="si">%d</span><span class="s1">_%H%M%S&#39;</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
            <span class="s1">&#39;certification_id&#39;</span><span class="p">:</span> <span class="n">certification_id</span><span class="p">,</span>
            <span class="s1">&#39;question_count&#39;</span><span class="p">:</span> <span class="n">question_count</span><span class="p">,</span>
            <span class="s1">&#39;difficulty&#39;</span><span class="p">:</span> <span class="n">difficulty</span><span class="p">,</span>
            <span class="s1">&#39;estimated_duration_minutes&#39;</span><span class="p">:</span> <span class="n">question_count</span> <span class="o">*</span> <span class="mi">2</span><span class="p">,</span>  <span class="c1"># 2 minutes per question</span>
            <span class="s1">&#39;mobile_optimized&#39;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
            <span class="s1">&#39;offline_capable&#39;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
            <span class="s1">&#39;adaptive_scoring&#39;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
            <span class="s1">&#39;created_at&#39;</span><span class="p">:</span> <span class="n">datetime</span><span class="o">.</span><span class="n">utcnow</span><span class="p">()</span><span class="o">.</span><span class="n">isoformat</span><span class="p">()</span>
        <span class="p">}</span>
        
        <span class="c1"># Get questions optimized for mobile display</span>
        <span class="n">questions</span> <span class="o">=</span> <span class="p">[]</span>  <span class="c1"># Would generate actual questions</span>
        <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">question_count</span><span class="p">):</span>
            <span class="n">questions</span><span class="o">.</span><span class="n">append</span><span class="p">({</span>
                <span class="s1">&#39;question_id&#39;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;q_</span><span class="si">{</span><span class="n">i</span><span class="o">+</span><span class="mi">1</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
                <span class="s1">&#39;question_text&#39;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;Sample mobile-optimized question </span><span class="si">{</span><span class="n">i</span><span class="o">+</span><span class="mi">1</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
                <span class="s1">&#39;question_type&#39;</span><span class="p">:</span> <span class="s1">&#39;multiple_choice&#39;</span><span class="p">,</span>
                <span class="s1">&#39;options&#39;</span><span class="p">:</span> <span class="p">[</span><span class="s1">&#39;Option A&#39;</span><span class="p">,</span> <span class="s1">&#39;Option B&#39;</span><span class="p">,</span> <span class="s1">&#39;Option C&#39;</span><span class="p">,</span> <span class="s1">&#39;Option D&#39;</span><span class="p">],</span>
                <span class="s1">&#39;mobile_formatted&#39;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
                <span class="s1">&#39;images_optimized&#39;</span><span class="p">:</span> <span class="kc">True</span>
            <span class="p">})</span>
        
        <span class="k">return</span> <span class="p">{</span>
            <span class="s1">&#39;practice_test&#39;</span><span class="p">:</span> <span class="n">practice_test</span><span class="p">,</span>
            <span class="s1">&#39;questions&#39;</span><span class="p">:</span> <span class="n">questions</span><span class="p">,</span>
            <span class="s1">&#39;instructions&#39;</span><span class="p">:</span> <span class="p">{</span>
                <span class="s1">&#39;mobile_specific&#39;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
                <span class="s1">&#39;touch_optimized&#39;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
                <span class="s1">&#39;offline_submission&#39;</span><span class="p">:</span> <span class="kc">True</span>
            <span class="p">}</span>
        <span class="p">}</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error getting mobile practice test: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error getting practice test&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="get_offline_ai_recommendations">
<a class="viewcode-back" href="../../../api/index.html#api.v1.mobile.get_offline_ai_recommendations">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/offline-ai/recommendations&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_offline_ai_recommendations</span><span class="p">(</span>
    <span class="n">user_id</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_mobile_user</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get AI recommendations optimized for offline mobile use.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Getting offline AI recommendations for user </span><span class="si">{</span><span class="n">user_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">mobile_service</span> <span class="o">=</span> <span class="n">MobileAPIService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        
        <span class="c1"># Get lightweight AI recommendations for offline use</span>
        <span class="n">recommendations</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s1">&#39;study_recommendations&#39;</span><span class="p">:</span> <span class="p">[</span>
                <span class="p">{</span>
                    <span class="s1">&#39;type&#39;</span><span class="p">:</span> <span class="s1">&#39;study_focus&#39;</span><span class="p">,</span>
                    <span class="s1">&#39;title&#39;</span><span class="p">:</span> <span class="s1">&#39;Focus on Network Security&#39;</span><span class="p">,</span>
                    <span class="s1">&#39;description&#39;</span><span class="p">:</span> <span class="s1">&#39;Based on your recent performance, spend more time on network security topics&#39;</span><span class="p">,</span>
                    <span class="s1">&#39;priority&#39;</span><span class="p">:</span> <span class="s1">&#39;high&#39;</span><span class="p">,</span>
                    <span class="s1">&#39;estimated_time_minutes&#39;</span><span class="p">:</span> <span class="mi">45</span><span class="p">,</span>
                    <span class="s1">&#39;offline_available&#39;</span><span class="p">:</span> <span class="kc">True</span>
                <span class="p">},</span>
                <span class="p">{</span>
                    <span class="s1">&#39;type&#39;</span><span class="p">:</span> <span class="s1">&#39;practice_test&#39;</span><span class="p">,</span>
                    <span class="s1">&#39;title&#39;</span><span class="p">:</span> <span class="s1">&#39;Take Practice Test&#39;</span><span class="p">,</span>
                    <span class="s1">&#39;description&#39;</span><span class="p">:</span> <span class="s1">&#39;You</span><span class="se">\&#39;</span><span class="s1">re ready for a practice test to assess your progress&#39;</span><span class="p">,</span>
                    <span class="s1">&#39;priority&#39;</span><span class="p">:</span> <span class="s1">&#39;medium&#39;</span><span class="p">,</span>
                    <span class="s1">&#39;estimated_time_minutes&#39;</span><span class="p">:</span> <span class="mi">30</span><span class="p">,</span>
                    <span class="s1">&#39;offline_available&#39;</span><span class="p">:</span> <span class="kc">True</span>
                <span class="p">}</span>
            <span class="p">],</span>
            <span class="s1">&#39;learning_insights&#39;</span><span class="p">:</span> <span class="p">[</span>
                <span class="p">{</span>
                    <span class="s1">&#39;insight&#39;</span><span class="p">:</span> <span class="s1">&#39;Your study consistency has improved 25% this week&#39;</span><span class="p">,</span>
                    <span class="s1">&#39;confidence&#39;</span><span class="p">:</span> <span class="mf">0.89</span><span class="p">,</span>
                    <span class="s1">&#39;actionable&#39;</span><span class="p">:</span> <span class="kc">True</span>
                <span class="p">},</span>
                <span class="p">{</span>
                    <span class="s1">&#39;insight&#39;</span><span class="p">:</span> <span class="s1">&#39;Morning study sessions show 15% better retention&#39;</span><span class="p">,</span>
                    <span class="s1">&#39;confidence&#39;</span><span class="p">:</span> <span class="mf">0.76</span><span class="p">,</span>
                    <span class="s1">&#39;actionable&#39;</span><span class="p">:</span> <span class="kc">True</span>
                <span class="p">}</span>
            <span class="p">],</span>
            <span class="s1">&#39;next_actions&#39;</span><span class="p">:</span> <span class="p">[</span>
                <span class="s1">&#39;Complete Chapter 5 review&#39;</span><span class="p">,</span>
                <span class="s1">&#39;Take network security practice quiz&#39;</span><span class="p">,</span>
                <span class="s1">&#39;Review weak areas from last test&#39;</span>
            <span class="p">],</span>
            <span class="s1">&#39;generated_at&#39;</span><span class="p">:</span> <span class="n">datetime</span><span class="o">.</span><span class="n">utcnow</span><span class="p">()</span><span class="o">.</span><span class="n">isoformat</span><span class="p">(),</span>
            <span class="s1">&#39;offline_valid_until&#39;</span><span class="p">:</span> <span class="p">(</span><span class="n">datetime</span><span class="o">.</span><span class="n">utcnow</span><span class="p">()</span> <span class="o">+</span> <span class="n">timedelta</span><span class="p">(</span><span class="n">days</span><span class="o">=</span><span class="mi">3</span><span class="p">))</span><span class="o">.</span><span class="n">isoformat</span><span class="p">()</span>
        <span class="p">}</span>
        
        <span class="k">return</span> <span class="n">recommendations</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error getting offline AI recommendations: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error getting AI recommendations&quot;</span><span class="p">)</span></div>



<span class="c1"># Health and Status</span>

<div class="viewcode-block" id="mobile_health_check">
<a class="viewcode-back" href="../../../api/index.html#api.v1.mobile.mobile_health_check">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/health&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">MobileHealthResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">mobile_health_check</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Health check endpoint for mobile API service.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">MobileHealthResponse</span><span class="p">(</span>
            <span class="n">status</span><span class="o">=</span><span class="s2">&quot;healthy&quot;</span><span class="p">,</span>
            <span class="n">service</span><span class="o">=</span><span class="s2">&quot;Mobile Enterprise API&quot;</span><span class="p">,</span>
            <span class="n">version</span><span class="o">=</span><span class="s2">&quot;1.0.0&quot;</span><span class="p">,</span>
            <span class="n">features</span><span class="o">=</span><span class="p">[</span>
                <span class="s2">&quot;device_registration&quot;</span><span class="p">,</span>
                <span class="s2">&quot;mobile_authentication&quot;</span><span class="p">,</span>
                <span class="s2">&quot;offline_data_sync&quot;</span><span class="p">,</span>
                <span class="s2">&quot;push_notifications&quot;</span><span class="p">,</span>
                <span class="s2">&quot;mobile_analytics&quot;</span><span class="p">,</span>
                <span class="s2">&quot;offline_ai&quot;</span><span class="p">,</span>
                <span class="s2">&quot;mobile_dashboard&quot;</span><span class="p">,</span>
                <span class="s2">&quot;quick_study_sessions&quot;</span><span class="p">,</span>
                <span class="s2">&quot;mobile_practice_tests&quot;</span>
            <span class="p">],</span>
            <span class="n">mobile_capabilities</span><span class="o">=</span><span class="p">{</span>
                <span class="s2">&quot;offline_support&quot;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
                <span class="s2">&quot;push_notifications&quot;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
                <span class="s2">&quot;background_sync&quot;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
                <span class="s2">&quot;ai_recommendations&quot;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
                <span class="s2">&quot;biometric_auth&quot;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
                <span class="s2">&quot;adaptive_ui&quot;</span><span class="p">:</span> <span class="kc">True</span>
            <span class="p">},</span>
            <span class="n">supported_platforms</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;iOS&quot;</span><span class="p">,</span> <span class="s2">&quot;Android&quot;</span><span class="p">,</span> <span class="s2">&quot;Progressive Web App&quot;</span><span class="p">],</span>
            <span class="n">api_version</span><span class="o">=</span><span class="s2">&quot;v1&quot;</span><span class="p">,</span>
            <span class="n">timestamp</span><span class="o">=</span><span class="n">datetime</span><span class="o">.</span><span class="n">now</span><span class="p">()</span><span class="o">.</span><span class="n">isoformat</span><span class="p">()</span>
        <span class="p">)</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error in mobile health check: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Mobile service health check failed&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="get_mobile_app_config">
<a class="viewcode-back" href="../../../api/index.html#api.v1.mobile.get_mobile_app_config">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/app-config&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_mobile_app_config</span><span class="p">(</span>
    <span class="n">platform</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Mobile platform (ios/android)&quot;</span><span class="p">),</span>
    <span class="n">app_version</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;App version&quot;</span><span class="p">),</span>
    <span class="n">user_id</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_mobile_user</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get mobile app configuration and feature flags.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Getting mobile app config for </span><span class="si">{</span><span class="n">platform</span><span class="si">}</span><span class="s2"> v</span><span class="si">{</span><span class="n">app_version</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="c1"># Get user-specific and platform-specific configuration</span>
        <span class="n">config</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s1">&#39;app_config&#39;</span><span class="p">:</span> <span class="p">{</span>
                <span class="s1">&#39;theme&#39;</span><span class="p">:</span> <span class="s1">&#39;adaptive&#39;</span><span class="p">,</span>
                <span class="s1">&#39;offline_storage_limit_mb&#39;</span><span class="p">:</span> <span class="mi">500</span><span class="p">,</span>
                <span class="s1">&#39;sync_frequency_hours&#39;</span><span class="p">:</span> <span class="mi">6</span><span class="p">,</span>
                <span class="s1">&#39;notification_settings&#39;</span><span class="p">:</span> <span class="p">{</span>
                    <span class="s1">&#39;study_reminders&#39;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
                    <span class="s1">&#39;achievement_alerts&#39;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
                    <span class="s1">&#39;ai_insights&#39;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
                    <span class="s1">&#39;system_updates&#39;</span><span class="p">:</span> <span class="kc">True</span>
                <span class="p">},</span>
                <span class="s1">&#39;ai_features&#39;</span><span class="p">:</span> <span class="p">{</span>
                    <span class="s1">&#39;offline_recommendations&#39;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
                    <span class="s1">&#39;adaptive_learning&#39;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
                    <span class="s1">&#39;smart_notifications&#39;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
                    <span class="s1">&#39;performance_prediction&#39;</span><span class="p">:</span> <span class="kc">True</span>
                <span class="p">}</span>
            <span class="p">},</span>
            <span class="s1">&#39;feature_flags&#39;</span><span class="p">:</span> <span class="p">{</span>
                <span class="s1">&#39;biometric_auth&#39;</span><span class="p">:</span> <span class="n">platform</span> <span class="ow">in</span> <span class="p">[</span><span class="s1">&#39;ios&#39;</span><span class="p">,</span> <span class="s1">&#39;android&#39;</span><span class="p">],</span>
                <span class="s1">&#39;background_sync&#39;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
                <span class="s1">&#39;offline_ai&#39;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
                <span class="s1">&#39;social_features&#39;</span><span class="p">:</span> <span class="kc">False</span><span class="p">,</span>  <span class="c1"># Coming soon</span>
                <span class="s1">&#39;advanced_analytics&#39;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
                <span class="s1">&#39;voice_commands&#39;</span><span class="p">:</span> <span class="n">platform</span> <span class="o">==</span> <span class="s1">&#39;ios&#39;</span>  <span class="c1"># iOS first</span>
            <span class="p">},</span>
            <span class="s1">&#39;api_endpoints&#39;</span><span class="p">:</span> <span class="p">{</span>
                <span class="s1">&#39;base_url&#39;</span><span class="p">:</span> <span class="s1">&#39;/api/v1/mobile&#39;</span><span class="p">,</span>
                <span class="s1">&#39;websocket_url&#39;</span><span class="p">:</span> <span class="s1">&#39;/ws/mobile&#39;</span><span class="p">,</span>
                <span class="s1">&#39;cdn_url&#39;</span><span class="p">:</span> <span class="s1">&#39;/cdn/mobile&#39;</span>
            <span class="p">},</span>
            <span class="s1">&#39;security_settings&#39;</span><span class="p">:</span> <span class="p">{</span>
                <span class="s1">&#39;session_timeout_minutes&#39;</span><span class="p">:</span> <span class="mi">1440</span><span class="p">,</span>  <span class="c1"># 24 hours</span>
                <span class="s1">&#39;biometric_timeout_minutes&#39;</span><span class="p">:</span> <span class="mi">15</span><span class="p">,</span>
                <span class="s1">&#39;auto_lock_minutes&#39;</span><span class="p">:</span> <span class="mi">5</span><span class="p">,</span>
                <span class="s1">&#39;require_pin_backup&#39;</span><span class="p">:</span> <span class="kc">True</span>
            <span class="p">},</span>
            <span class="s1">&#39;performance_settings&#39;</span><span class="p">:</span> <span class="p">{</span>
                <span class="s1">&#39;image_quality&#39;</span><span class="p">:</span> <span class="s1">&#39;adaptive&#39;</span><span class="p">,</span>
                <span class="s1">&#39;video_quality&#39;</span><span class="p">:</span> <span class="s1">&#39;medium&#39;</span><span class="p">,</span>
                <span class="s1">&#39;cache_size_mb&#39;</span><span class="p">:</span> <span class="mi">100</span><span class="p">,</span>
                <span class="s1">&#39;preload_content&#39;</span><span class="p">:</span> <span class="kc">True</span>
            <span class="p">}</span>
        <span class="p">}</span>
        
        <span class="k">return</span> <span class="n">config</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error getting mobile app config: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error getting app configuration&quot;</span><span class="p">)</span></div>

</pre></div>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, CertPathFinder Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>