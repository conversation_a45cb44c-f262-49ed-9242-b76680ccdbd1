

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../../../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>api.v1.career_transition &mdash; CertPathFinder 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../../../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../../../_static/custom.css?v=c14262df" />

  
      <script src="../../../_static/jquery.js?v=5d32c60e"></script>
      <script src="../../../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../../../_static/documentation_options.js?v=8d563738"></script>
      <script src="../../../_static/doctools.js?v=9bcbadda"></script>
      <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../../../index.html" class="icon icon-home">
            CertPathFinder
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../quickstart.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../api/index.html">API Reference</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../../index.html">CertPathFinder</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../../index.html">Module code</a></li>
      <li class="breadcrumb-item active">api.v1.career_transition</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>Source code for api.v1.career_transition</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;FastAPI endpoints for career transition planning and pathfinding.</span>

<span class="sd">This module provides comprehensive API endpoints for career transition planning,</span>
<span class="sd">budget-aware pathfinding, and transition plan management.</span>
<span class="sd">&quot;&quot;&quot;</span>

<span class="kn">import</span><span class="w"> </span><span class="nn">logging</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">typing</span><span class="w"> </span><span class="kn">import</span> <span class="n">List</span><span class="p">,</span> <span class="n">Optional</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">fastapi</span><span class="w"> </span><span class="kn">import</span> <span class="n">APIRouter</span><span class="p">,</span> <span class="n">Depends</span><span class="p">,</span> <span class="n">HTTPException</span><span class="p">,</span> <span class="n">Query</span><span class="p">,</span> <span class="n">Path</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">fastapi.responses</span><span class="w"> </span><span class="kn">import</span> <span class="n">Response</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">sqlalchemy.orm</span><span class="w"> </span><span class="kn">import</span> <span class="n">Session</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">datetime</span><span class="w"> </span><span class="kn">import</span> <span class="n">datetime</span>

<span class="kn">from</span><span class="w"> </span><span class="nn">database</span><span class="w"> </span><span class="kn">import</span> <span class="n">get_db</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">schemas.career_transition</span><span class="w"> </span><span class="kn">import</span> <span class="p">(</span>
    <span class="n">CareerPathfindingRequest</span><span class="p">,</span> <span class="n">CareerPathfindingResponse</span><span class="p">,</span>
    <span class="n">CareerTransitionPlanCreate</span><span class="p">,</span> <span class="n">CareerTransitionPlanUpdate</span><span class="p">,</span> <span class="n">CareerTransitionPlanResponse</span><span class="p">,</span>
    <span class="n">StepProgressUpdate</span><span class="p">,</span> <span class="n">TransitionStepResponse</span><span class="p">,</span>
    <span class="n">CareerRoleResponse</span><span class="p">,</span> <span class="n">CareerTransitionSummary</span>
<span class="p">)</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">services.career_transition</span><span class="w"> </span><span class="kn">import</span> <span class="n">CareerTransitionService</span><span class="p">,</span> <span class="n">PathfindingConstraints</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">services.pdf_report_generator</span><span class="w"> </span><span class="kn">import</span> <span class="n">EnhancedPDFReportGenerator</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">models.career_transition</span><span class="w"> </span><span class="kn">import</span> <span class="n">CareerRole</span><span class="p">,</span> <span class="n">CareerTransitionPlan</span><span class="p">,</span> <span class="n">CareerTransitionStep</span>

<span class="n">logger</span> <span class="o">=</span> <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="vm">__name__</span><span class="p">)</span>
<span class="n">router</span> <span class="o">=</span> <span class="n">APIRouter</span><span class="p">(</span><span class="n">prefix</span><span class="o">=</span><span class="s2">&quot;/career-transition&quot;</span><span class="p">,</span> <span class="n">tags</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;Career Transition&quot;</span><span class="p">])</span>


<div class="viewcode-block" id="get_current_user_id">
<a class="viewcode-back" href="../../../api/index.html#api.v1.career_transition.get_current_user_id">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">get_current_user_id</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get current user ID from authentication context.&quot;&quot;&quot;</span>
    <span class="c1"># TODO: Implement proper authentication</span>
    <span class="k">return</span> <span class="s2">&quot;test_user_1&quot;</span></div>



<div class="viewcode-block" id="find_career_paths">
<a class="viewcode-back" href="../../../api/index.html#api.v1.career_transition.find_career_paths">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/pathfinding&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">CareerPathfindingResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">find_career_paths</span><span class="p">(</span>
    <span class="n">request</span><span class="p">:</span> <span class="n">CareerPathfindingRequest</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Find optimal career transition paths with budget and timeline constraints.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Finding career paths from </span><span class="si">{</span><span class="n">request</span><span class="o">.</span><span class="n">current_role_id</span><span class="si">}</span><span class="s2"> to </span><span class="si">{</span><span class="n">request</span><span class="o">.</span><span class="n">target_role_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">service</span> <span class="o">=</span> <span class="n">CareerTransitionService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        
        <span class="c1"># Create constraints from request</span>
        <span class="n">constraints</span> <span class="o">=</span> <span class="n">PathfindingConstraints</span><span class="p">(</span>
            <span class="n">max_budget</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">max_budget</span><span class="p">,</span>
            <span class="n">max_timeline_months</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">max_timeline_months</span><span class="p">,</span>
            <span class="n">max_difficulty</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">max_difficulty</span><span class="o">.</span><span class="n">value</span><span class="p">,</span>
            <span class="n">preferred_learning_style</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">learning_style</span><span class="o">.</span><span class="n">value</span><span class="p">,</span>
            <span class="n">study_hours_per_week</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">study_hours_per_week</span><span class="p">,</span>
            <span class="n">currency</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">currency</span>
        <span class="p">)</span>
        
        <span class="c1"># Find paths</span>
        <span class="n">path_options</span> <span class="o">=</span> <span class="n">service</span><span class="o">.</span><span class="n">find_career_paths</span><span class="p">(</span>
            <span class="n">source_role_id</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">current_role_id</span><span class="p">,</span>
            <span class="n">target_role_id</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">target_role_id</span><span class="p">,</span>
            <span class="n">constraints</span><span class="o">=</span><span class="n">constraints</span><span class="p">,</span>
            <span class="n">max_paths</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">max_paths</span>
        <span class="p">)</span>
        
        <span class="k">if</span> <span class="ow">not</span> <span class="n">path_options</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span>
                <span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span>
                <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;No viable career transition paths found with the given constraints&quot;</span>
            <span class="p">)</span>
        
        <span class="c1"># Get role information</span>
        <span class="n">target_role</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">CareerRole</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">CareerRole</span><span class="o">.</span><span class="n">id</span> <span class="o">==</span> <span class="n">request</span><span class="o">.</span><span class="n">target_role_id</span><span class="p">)</span><span class="o">.</span><span class="n">first</span><span class="p">()</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">target_role</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Target role not found&quot;</span><span class="p">)</span>
        
        <span class="n">source_role</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="k">if</span> <span class="n">request</span><span class="o">.</span><span class="n">current_role_id</span><span class="p">:</span>
            <span class="n">source_role</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">CareerRole</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">CareerRole</span><span class="o">.</span><span class="n">id</span> <span class="o">==</span> <span class="n">request</span><span class="o">.</span><span class="n">current_role_id</span><span class="p">)</span><span class="o">.</span><span class="n">first</span><span class="p">()</span>
        
        <span class="c1"># Calculate summary statistics</span>
        <span class="n">costs</span> <span class="o">=</span> <span class="p">[</span><span class="n">p</span><span class="o">.</span><span class="n">total_cost</span> <span class="k">for</span> <span class="n">p</span> <span class="ow">in</span> <span class="n">path_options</span><span class="p">]</span>
        <span class="n">durations</span> <span class="o">=</span> <span class="p">[</span><span class="n">p</span><span class="o">.</span><span class="n">total_duration_months</span> <span class="k">for</span> <span class="n">p</span> <span class="ow">in</span> <span class="n">path_options</span><span class="p">]</span>
        
        <span class="c1"># Convert to response format</span>
        <span class="n">path_responses</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">path</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">path_options</span><span class="p">):</span>
            <span class="n">steps</span> <span class="o">=</span> <span class="p">[</span>
                <span class="p">{</span>
                    <span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="n">step</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">],</span>
                    <span class="s2">&quot;description&quot;</span><span class="p">:</span> <span class="n">step</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;description&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">),</span>
                    <span class="s2">&quot;step_type&quot;</span><span class="p">:</span> <span class="n">step</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;type&quot;</span><span class="p">,</span> <span class="s2">&quot;transition&quot;</span><span class="p">),</span>
                    <span class="s2">&quot;duration_months&quot;</span><span class="p">:</span> <span class="n">step</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;duration_months&quot;</span><span class="p">,</span> <span class="mi">1</span><span class="p">),</span>
                    <span class="s2">&quot;cost&quot;</span><span class="p">:</span> <span class="n">step</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;cost&quot;</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">),</span>
                    <span class="s2">&quot;certifications&quot;</span><span class="p">:</span> <span class="n">step</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;certifications&quot;</span><span class="p">,</span> <span class="p">[]),</span>
                    <span class="s2">&quot;prerequisites&quot;</span><span class="p">:</span> <span class="n">step</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;prerequisites&quot;</span><span class="p">,</span> <span class="p">[])</span>
                <span class="p">}</span>
                <span class="k">for</span> <span class="n">step</span> <span class="ow">in</span> <span class="n">path</span><span class="o">.</span><span class="n">steps</span>
            <span class="p">]</span>
            
            <span class="n">path_responses</span><span class="o">.</span><span class="n">append</span><span class="p">({</span>
                <span class="s2">&quot;path_id&quot;</span><span class="p">:</span> <span class="nb">str</span><span class="p">(</span><span class="n">path</span><span class="o">.</span><span class="n">path_id</span><span class="p">),</span>
                <span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;Path </span><span class="si">{</span><span class="n">i</span><span class="o">+</span><span class="mi">1</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
                <span class="s2">&quot;description&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;Career transition path with </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">path</span><span class="o">.</span><span class="n">steps</span><span class="p">)</span><span class="si">}</span><span class="s2"> steps&quot;</span><span class="p">,</span>
                <span class="s2">&quot;total_cost&quot;</span><span class="p">:</span> <span class="n">path</span><span class="o">.</span><span class="n">total_cost</span><span class="p">,</span>
                <span class="s2">&quot;total_duration_months&quot;</span><span class="p">:</span> <span class="n">path</span><span class="o">.</span><span class="n">total_duration_months</span><span class="p">,</span>
                <span class="s2">&quot;difficulty_level&quot;</span><span class="p">:</span> <span class="p">[</span><span class="s2">&quot;Easy&quot;</span><span class="p">,</span> <span class="s2">&quot;Medium&quot;</span><span class="p">,</span> <span class="s2">&quot;Hard&quot;</span><span class="p">,</span> <span class="s2">&quot;Expert&quot;</span><span class="p">][</span><span class="nb">min</span><span class="p">(</span><span class="nb">int</span><span class="p">(</span><span class="n">path</span><span class="o">.</span><span class="n">difficulty_score</span><span class="p">),</span> <span class="mi">3</span><span class="p">)],</span>
                <span class="s2">&quot;success_probability&quot;</span><span class="p">:</span> <span class="n">path</span><span class="o">.</span><span class="n">success_probability</span><span class="p">,</span>
                <span class="s2">&quot;steps&quot;</span><span class="p">:</span> <span class="n">steps</span><span class="p">,</span>
                <span class="s2">&quot;certifications_required&quot;</span><span class="p">:</span> <span class="n">path</span><span class="o">.</span><span class="n">certifications_required</span><span class="p">,</span>
                <span class="s2">&quot;estimated_salary_increase&quot;</span><span class="p">:</span> <span class="n">path</span><span class="o">.</span><span class="n">estimated_salary_increase</span><span class="p">,</span>
                <span class="s2">&quot;cost_score&quot;</span><span class="p">:</span> <span class="n">path</span><span class="o">.</span><span class="n">total_cost</span> <span class="o">/</span> <span class="nb">max</span><span class="p">(</span><span class="n">costs</span><span class="p">)</span> <span class="k">if</span> <span class="n">costs</span> <span class="k">else</span> <span class="mf">0.0</span><span class="p">,</span>
                <span class="s2">&quot;time_score&quot;</span><span class="p">:</span> <span class="n">path</span><span class="o">.</span><span class="n">total_duration_months</span> <span class="o">/</span> <span class="nb">max</span><span class="p">(</span><span class="n">durations</span><span class="p">)</span> <span class="k">if</span> <span class="n">durations</span> <span class="k">else</span> <span class="mf">0.0</span><span class="p">,</span>
                <span class="s2">&quot;difficulty_score&quot;</span><span class="p">:</span> <span class="n">path</span><span class="o">.</span><span class="n">difficulty_score</span> <span class="o">/</span> <span class="mf">4.0</span><span class="p">,</span>
                <span class="s2">&quot;overall_score&quot;</span><span class="p">:</span> <span class="n">service</span><span class="o">.</span><span class="n">_calculate_path_score</span><span class="p">(</span><span class="n">path</span><span class="p">,</span> <span class="n">constraints</span><span class="p">)</span>
            <span class="p">})</span>
        
        <span class="c1"># Generate recommendations</span>
        <span class="n">recommendations</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">if</span> <span class="n">path_options</span><span class="p">:</span>
            <span class="n">best_path</span> <span class="o">=</span> <span class="n">path_options</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
            <span class="k">if</span> <span class="n">best_path</span><span class="o">.</span><span class="n">total_cost</span> <span class="o">&lt;</span> <span class="p">(</span><span class="n">request</span><span class="o">.</span><span class="n">max_budget</span> <span class="ow">or</span> <span class="nb">float</span><span class="p">(</span><span class="s1">&#39;inf&#39;</span><span class="p">))</span> <span class="o">*</span> <span class="mf">0.8</span><span class="p">:</span>
                <span class="n">recommendations</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="s2">&quot;This path fits well within your budget with room for unexpected costs&quot;</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">best_path</span><span class="o">.</span><span class="n">success_probability</span> <span class="o">&gt;</span> <span class="mf">0.8</span><span class="p">:</span>
                <span class="n">recommendations</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="s2">&quot;This path has a high success rate based on historical data&quot;</span><span class="p">)</span>
            <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">best_path</span><span class="o">.</span><span class="n">certifications_required</span><span class="p">)</span> <span class="o">&lt;=</span> <span class="mi">3</span><span class="p">:</span>
                <span class="n">recommendations</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="s2">&quot;This path requires a manageable number of certifications&quot;</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="n">CareerPathfindingResponse</span><span class="p">(</span>
            <span class="n">source_role</span><span class="o">=</span><span class="n">source_role</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span> <span class="k">if</span> <span class="n">source_role</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
            <span class="n">target_role</span><span class="o">=</span><span class="n">target_role</span><span class="o">.</span><span class="n">to_dict</span><span class="p">(),</span>
            <span class="n">path_options</span><span class="o">=</span><span class="n">path_responses</span><span class="p">,</span>
            <span class="n">total_paths_found</span><span class="o">=</span><span class="nb">len</span><span class="p">(</span><span class="n">path_options</span><span class="p">),</span>
            <span class="n">cost_range</span><span class="o">=</span><span class="p">{</span>
                <span class="s2">&quot;min&quot;</span><span class="p">:</span> <span class="nb">min</span><span class="p">(</span><span class="n">costs</span><span class="p">)</span> <span class="k">if</span> <span class="n">costs</span> <span class="k">else</span> <span class="mf">0.0</span><span class="p">,</span>
                <span class="s2">&quot;max&quot;</span><span class="p">:</span> <span class="nb">max</span><span class="p">(</span><span class="n">costs</span><span class="p">)</span> <span class="k">if</span> <span class="n">costs</span> <span class="k">else</span> <span class="mf">0.0</span><span class="p">,</span>
                <span class="s2">&quot;currency&quot;</span><span class="p">:</span> <span class="n">request</span><span class="o">.</span><span class="n">currency</span>
            <span class="p">},</span>
            <span class="n">duration_range</span><span class="o">=</span><span class="p">{</span>
                <span class="s2">&quot;estimated_months&quot;</span><span class="p">:</span> <span class="nb">int</span><span class="p">(</span><span class="nb">sum</span><span class="p">(</span><span class="n">durations</span><span class="p">)</span> <span class="o">/</span> <span class="nb">len</span><span class="p">(</span><span class="n">durations</span><span class="p">))</span> <span class="k">if</span> <span class="n">durations</span> <span class="k">else</span> <span class="mi">0</span><span class="p">,</span>
                <span class="s2">&quot;min_months&quot;</span><span class="p">:</span> <span class="nb">min</span><span class="p">(</span><span class="n">durations</span><span class="p">)</span> <span class="k">if</span> <span class="n">durations</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
                <span class="s2">&quot;max_months&quot;</span><span class="p">:</span> <span class="nb">max</span><span class="p">(</span><span class="n">durations</span><span class="p">)</span> <span class="k">if</span> <span class="n">durations</span> <span class="k">else</span> <span class="kc">None</span>
            <span class="p">},</span>
            <span class="n">recommended_path_id</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">path_options</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span><span class="o">.</span><span class="n">path_id</span><span class="p">)</span> <span class="k">if</span> <span class="n">path_options</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
            <span class="n">recommendations</span><span class="o">=</span><span class="n">recommendations</span><span class="p">,</span>
            <span class="n">generated_at</span><span class="o">=</span><span class="n">datetime</span><span class="o">.</span><span class="n">utcnow</span><span class="p">()</span>
        <span class="p">)</span>
        
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error in career pathfinding: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Internal server error during pathfinding&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="get_career_roles">
<a class="viewcode-back" href="../../../api/index.html#api.v1.career_transition.get_career_roles">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/roles&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">List</span><span class="p">[</span><span class="n">CareerRoleResponse</span><span class="p">])</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_career_roles</span><span class="p">(</span>
    <span class="n">domain</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by domain&quot;</span><span class="p">),</span>
    <span class="n">level</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by career level&quot;</span><span class="p">),</span>
    <span class="n">min_salary</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Minimum salary filter&quot;</span><span class="p">),</span>
    <span class="n">max_salary</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">float</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Maximum salary filter&quot;</span><span class="p">),</span>
    <span class="n">page</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Page number&quot;</span><span class="p">),</span>
    <span class="n">page_size</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">20</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">le</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Page size&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get available career roles with filtering options.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">query</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">CareerRole</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">CareerRole</span><span class="o">.</span><span class="n">is_active</span> <span class="o">==</span> <span class="kc">True</span><span class="p">)</span>
        
        <span class="c1"># Apply filters</span>
        <span class="k">if</span> <span class="n">domain</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">CareerRole</span><span class="o">.</span><span class="n">domain</span><span class="o">.</span><span class="n">ilike</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;%</span><span class="si">{</span><span class="n">domain</span><span class="si">}</span><span class="s2">%&quot;</span><span class="p">))</span>
        <span class="k">if</span> <span class="n">level</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">CareerRole</span><span class="o">.</span><span class="n">level</span> <span class="o">==</span> <span class="n">level</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">min_salary</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">CareerRole</span><span class="o">.</span><span class="n">salary_min</span> <span class="o">&gt;=</span> <span class="n">min_salary</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">max_salary</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">CareerRole</span><span class="o">.</span><span class="n">salary_max</span> <span class="o">&lt;=</span> <span class="n">max_salary</span><span class="p">)</span>
        
        <span class="c1"># Apply pagination</span>
        <span class="n">offset</span> <span class="o">=</span> <span class="p">(</span><span class="n">page</span> <span class="o">-</span> <span class="mi">1</span><span class="p">)</span> <span class="o">*</span> <span class="n">page_size</span>
        <span class="n">roles</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">offset</span><span class="p">(</span><span class="n">offset</span><span class="p">)</span><span class="o">.</span><span class="n">limit</span><span class="p">(</span><span class="n">page_size</span><span class="p">)</span><span class="o">.</span><span class="n">all</span><span class="p">()</span>
        
        <span class="k">return</span> <span class="p">[</span><span class="n">role</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span> <span class="k">for</span> <span class="n">role</span> <span class="ow">in</span> <span class="n">roles</span><span class="p">]</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error retrieving career roles: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error retrieving career roles&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="create_transition_plan">
<a class="viewcode-back" href="../../../api/index.html#api.v1.career_transition.create_transition_plan">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/plans&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">CareerTransitionPlanResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">create_transition_plan</span><span class="p">(</span>
    <span class="n">request</span><span class="p">:</span> <span class="n">CareerTransitionPlanCreate</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Create a new career transition plan.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Creating transition plan for user </span><span class="si">{</span><span class="n">user_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">service</span> <span class="o">=</span> <span class="n">CareerTransitionService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        
        <span class="c1"># Create constraints from request</span>
        <span class="n">constraints</span> <span class="o">=</span> <span class="n">PathfindingConstraints</span><span class="p">(</span>
            <span class="n">max_budget</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">budget_max</span><span class="p">,</span>
            <span class="n">max_timeline_months</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">max_timeline_months</span><span class="p">,</span>
            <span class="n">max_difficulty</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">difficulty_preference</span><span class="o">.</span><span class="n">value</span><span class="p">,</span>
            <span class="n">preferred_learning_style</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">learning_style</span><span class="o">.</span><span class="n">value</span><span class="p">,</span>
            <span class="n">study_hours_per_week</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">study_hours_per_week</span><span class="p">,</span>
            <span class="n">currency</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">currency</span>
        <span class="p">)</span>
        
        <span class="c1"># Create the plan</span>
        <span class="n">plan</span> <span class="o">=</span> <span class="n">service</span><span class="o">.</span><span class="n">create_transition_plan</span><span class="p">(</span>
            <span class="n">user_id</span><span class="o">=</span><span class="n">user_id</span><span class="p">,</span>
            <span class="n">name</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">name</span><span class="p">,</span>
            <span class="n">current_role_id</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">current_role_id</span><span class="p">,</span>
            <span class="n">target_role_id</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">target_role_id</span><span class="p">,</span>
            <span class="n">constraints</span><span class="o">=</span><span class="n">constraints</span><span class="p">,</span>
            <span class="n">description</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">description</span>
        <span class="p">)</span>
        
        <span class="c1"># Get plan with related data</span>
        <span class="k">return</span> <span class="k">await</span> <span class="n">get_transition_plan</span><span class="p">(</span><span class="n">plan</span><span class="o">.</span><span class="n">id</span><span class="p">,</span> <span class="n">db</span><span class="p">)</span>
        
    <span class="k">except</span> <span class="ne">ValueError</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">400</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">))</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error creating transition plan: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error creating transition plan&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="get_user_transition_plans">
<a class="viewcode-back" href="../../../api/index.html#api.v1.career_transition.get_user_transition_plans">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/plans&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">List</span><span class="p">[</span><span class="n">CareerTransitionPlanResponse</span><span class="p">])</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_user_transition_plans</span><span class="p">(</span>
    <span class="n">status</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by plan status&quot;</span><span class="p">),</span>
    <span class="n">page</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Page number&quot;</span><span class="p">),</span>
    <span class="n">page_size</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">20</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">le</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Page size&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get user&#39;s career transition plans.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        
        <span class="n">query</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">CareerTransitionPlan</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
            <span class="n">CareerTransitionPlan</span><span class="o">.</span><span class="n">user_id</span> <span class="o">==</span> <span class="n">user_id</span><span class="p">,</span>
            <span class="n">CareerTransitionPlan</span><span class="o">.</span><span class="n">is_active</span> <span class="o">==</span> <span class="kc">True</span>
        <span class="p">)</span>
        
        <span class="k">if</span> <span class="n">status</span><span class="p">:</span>
            <span class="n">query</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span><span class="n">CareerTransitionPlan</span><span class="o">.</span><span class="n">status</span> <span class="o">==</span> <span class="n">status</span><span class="p">)</span>
        
        <span class="c1"># Apply pagination</span>
        <span class="n">offset</span> <span class="o">=</span> <span class="p">(</span><span class="n">page</span> <span class="o">-</span> <span class="mi">1</span><span class="p">)</span> <span class="o">*</span> <span class="n">page_size</span>
        <span class="n">plans</span> <span class="o">=</span> <span class="n">query</span><span class="o">.</span><span class="n">offset</span><span class="p">(</span><span class="n">offset</span><span class="p">)</span><span class="o">.</span><span class="n">limit</span><span class="p">(</span><span class="n">page_size</span><span class="p">)</span><span class="o">.</span><span class="n">all</span><span class="p">()</span>
        
        <span class="c1"># Convert to response format</span>
        <span class="n">plan_responses</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">for</span> <span class="n">plan</span> <span class="ow">in</span> <span class="n">plans</span><span class="p">:</span>
            <span class="n">plan_dict</span> <span class="o">=</span> <span class="n">plan</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span>
            
            <span class="c1"># Add related data</span>
            <span class="k">if</span> <span class="n">plan</span><span class="o">.</span><span class="n">current_role</span><span class="p">:</span>
                <span class="n">plan_dict</span><span class="p">[</span><span class="s1">&#39;current_role&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">plan</span><span class="o">.</span><span class="n">current_role</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span>
            <span class="k">if</span> <span class="n">plan</span><span class="o">.</span><span class="n">target_role</span><span class="p">:</span>
                <span class="n">plan_dict</span><span class="p">[</span><span class="s1">&#39;target_role&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">plan</span><span class="o">.</span><span class="n">target_role</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span>
            
            <span class="c1"># Get steps</span>
            <span class="n">steps</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">CareerTransitionStep</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
                <span class="n">CareerTransitionStep</span><span class="o">.</span><span class="n">plan_id</span> <span class="o">==</span> <span class="n">plan</span><span class="o">.</span><span class="n">id</span>
            <span class="p">)</span><span class="o">.</span><span class="n">order_by</span><span class="p">(</span><span class="n">CareerTransitionStep</span><span class="o">.</span><span class="n">sequence</span><span class="p">)</span><span class="o">.</span><span class="n">all</span><span class="p">()</span>
            <span class="n">plan_dict</span><span class="p">[</span><span class="s1">&#39;steps&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">step</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span> <span class="k">for</span> <span class="n">step</span> <span class="ow">in</span> <span class="n">steps</span><span class="p">]</span>
            
            <span class="n">plan_responses</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">plan_dict</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="n">plan_responses</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error retrieving transition plans: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error retrieving transition plans&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="get_transition_plan">
<a class="viewcode-back" href="../../../api/index.html#api.v1.career_transition.get_transition_plan">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/plans/</span><span class="si">{plan_id}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">CareerTransitionPlanResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_transition_plan</span><span class="p">(</span>
    <span class="n">plan_id</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Transition plan ID&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get a specific career transition plan.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        
        <span class="n">plan</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">CareerTransitionPlan</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
            <span class="n">CareerTransitionPlan</span><span class="o">.</span><span class="n">id</span> <span class="o">==</span> <span class="n">plan_id</span><span class="p">,</span>
            <span class="n">CareerTransitionPlan</span><span class="o">.</span><span class="n">user_id</span> <span class="o">==</span> <span class="n">user_id</span><span class="p">,</span>
            <span class="n">CareerTransitionPlan</span><span class="o">.</span><span class="n">is_active</span> <span class="o">==</span> <span class="kc">True</span>
        <span class="p">)</span><span class="o">.</span><span class="n">first</span><span class="p">()</span>
        
        <span class="k">if</span> <span class="ow">not</span> <span class="n">plan</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Transition plan not found&quot;</span><span class="p">)</span>
        
        <span class="n">plan_dict</span> <span class="o">=</span> <span class="n">plan</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span>
        
        <span class="c1"># Add related data</span>
        <span class="k">if</span> <span class="n">plan</span><span class="o">.</span><span class="n">current_role</span><span class="p">:</span>
            <span class="n">plan_dict</span><span class="p">[</span><span class="s1">&#39;current_role&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">plan</span><span class="o">.</span><span class="n">current_role</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span>
        <span class="k">if</span> <span class="n">plan</span><span class="o">.</span><span class="n">target_role</span><span class="p">:</span>
            <span class="n">plan_dict</span><span class="p">[</span><span class="s1">&#39;target_role&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">plan</span><span class="o">.</span><span class="n">target_role</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span>
        
        <span class="c1"># Get steps</span>
        <span class="n">steps</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">CareerTransitionStep</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
            <span class="n">CareerTransitionStep</span><span class="o">.</span><span class="n">plan_id</span> <span class="o">==</span> <span class="n">plan</span><span class="o">.</span><span class="n">id</span>
        <span class="p">)</span><span class="o">.</span><span class="n">order_by</span><span class="p">(</span><span class="n">CareerTransitionStep</span><span class="o">.</span><span class="n">sequence</span><span class="p">)</span><span class="o">.</span><span class="n">all</span><span class="p">()</span>
        <span class="n">plan_dict</span><span class="p">[</span><span class="s1">&#39;steps&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="n">step</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span> <span class="k">for</span> <span class="n">step</span> <span class="ow">in</span> <span class="n">steps</span><span class="p">]</span>
        
        <span class="k">return</span> <span class="n">plan_dict</span>
        
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error retrieving transition plan </span><span class="si">{</span><span class="n">plan_id</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error retrieving transition plan&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="update_transition_plan">
<a class="viewcode-back" href="../../../api/index.html#api.v1.career_transition.update_transition_plan">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">put</span><span class="p">(</span><span class="s2">&quot;/plans/</span><span class="si">{plan_id}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">CareerTransitionPlanResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">update_transition_plan</span><span class="p">(</span>
    <span class="n">plan_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span>
    <span class="n">request</span><span class="p">:</span> <span class="n">CareerTransitionPlanUpdate</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Update a career transition plan.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        
        <span class="n">plan</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">CareerTransitionPlan</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
            <span class="n">CareerTransitionPlan</span><span class="o">.</span><span class="n">id</span> <span class="o">==</span> <span class="n">plan_id</span><span class="p">,</span>
            <span class="n">CareerTransitionPlan</span><span class="o">.</span><span class="n">user_id</span> <span class="o">==</span> <span class="n">user_id</span><span class="p">,</span>
            <span class="n">CareerTransitionPlan</span><span class="o">.</span><span class="n">is_active</span> <span class="o">==</span> <span class="kc">True</span>
        <span class="p">)</span><span class="o">.</span><span class="n">first</span><span class="p">()</span>
        
        <span class="k">if</span> <span class="ow">not</span> <span class="n">plan</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Transition plan not found&quot;</span><span class="p">)</span>
        
        <span class="c1"># Update fields</span>
        <span class="n">update_data</span> <span class="o">=</span> <span class="n">request</span><span class="o">.</span><span class="n">dict</span><span class="p">(</span><span class="n">exclude_unset</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">field</span><span class="p">,</span> <span class="n">value</span> <span class="ow">in</span> <span class="n">update_data</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
            <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">plan</span><span class="p">,</span> <span class="n">field</span><span class="p">):</span>
                <span class="nb">setattr</span><span class="p">(</span><span class="n">plan</span><span class="p">,</span> <span class="n">field</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
        
        <span class="n">db</span><span class="o">.</span><span class="n">commit</span><span class="p">()</span>
        
        <span class="k">return</span> <span class="k">await</span> <span class="n">get_transition_plan</span><span class="p">(</span><span class="n">plan_id</span><span class="p">,</span> <span class="n">db</span><span class="p">)</span>
        
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error updating transition plan </span><span class="si">{</span><span class="n">plan_id</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error updating transition plan&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="update_step_progress">
<a class="viewcode-back" href="../../../api/index.html#api.v1.career_transition.update_step_progress">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">put</span><span class="p">(</span><span class="s2">&quot;/plans/</span><span class="si">{plan_id}</span><span class="s2">/steps/</span><span class="si">{step_id}</span><span class="s2">/progress&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">update_step_progress</span><span class="p">(</span>
    <span class="n">plan_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span>
    <span class="n">step_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span>
    <span class="n">request</span><span class="p">:</span> <span class="n">StepProgressUpdate</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Update progress for a transition plan step.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        
        <span class="c1"># Verify plan ownership</span>
        <span class="n">plan</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">CareerTransitionPlan</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
            <span class="n">CareerTransitionPlan</span><span class="o">.</span><span class="n">id</span> <span class="o">==</span> <span class="n">plan_id</span><span class="p">,</span>
            <span class="n">CareerTransitionPlan</span><span class="o">.</span><span class="n">user_id</span> <span class="o">==</span> <span class="n">user_id</span><span class="p">,</span>
            <span class="n">CareerTransitionPlan</span><span class="o">.</span><span class="n">is_active</span> <span class="o">==</span> <span class="kc">True</span>
        <span class="p">)</span><span class="o">.</span><span class="n">first</span><span class="p">()</span>
        
        <span class="k">if</span> <span class="ow">not</span> <span class="n">plan</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Transition plan not found&quot;</span><span class="p">)</span>
        
        <span class="n">service</span> <span class="o">=</span> <span class="n">CareerTransitionService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        <span class="n">success</span> <span class="o">=</span> <span class="n">service</span><span class="o">.</span><span class="n">update_plan_progress</span><span class="p">(</span><span class="n">plan_id</span><span class="p">,</span> <span class="n">step_id</span><span class="p">,</span> <span class="n">request</span><span class="o">.</span><span class="n">progress_percentage</span><span class="p">)</span>
        
        <span class="k">if</span> <span class="ow">not</span> <span class="n">success</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Transition step not found&quot;</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="p">{</span><span class="s2">&quot;message&quot;</span><span class="p">:</span> <span class="s2">&quot;Step progress updated successfully&quot;</span><span class="p">}</span>
        
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error updating step progress: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error updating step progress&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="delete_transition_plan">
<a class="viewcode-back" href="../../../api/index.html#api.v1.career_transition.delete_transition_plan">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">delete</span><span class="p">(</span><span class="s2">&quot;/plans/</span><span class="si">{plan_id}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">delete_transition_plan</span><span class="p">(</span>
    <span class="n">plan_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Delete (deactivate) a career transition plan.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        
        <span class="n">plan</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">CareerTransitionPlan</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
            <span class="n">CareerTransitionPlan</span><span class="o">.</span><span class="n">id</span> <span class="o">==</span> <span class="n">plan_id</span><span class="p">,</span>
            <span class="n">CareerTransitionPlan</span><span class="o">.</span><span class="n">user_id</span> <span class="o">==</span> <span class="n">user_id</span><span class="p">,</span>
            <span class="n">CareerTransitionPlan</span><span class="o">.</span><span class="n">is_active</span> <span class="o">==</span> <span class="kc">True</span>
        <span class="p">)</span><span class="o">.</span><span class="n">first</span><span class="p">()</span>
        
        <span class="k">if</span> <span class="ow">not</span> <span class="n">plan</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Transition plan not found&quot;</span><span class="p">)</span>
        
        <span class="n">plan</span><span class="o">.</span><span class="n">is_active</span> <span class="o">=</span> <span class="kc">False</span>
        <span class="n">db</span><span class="o">.</span><span class="n">commit</span><span class="p">()</span>
        
        <span class="k">return</span> <span class="p">{</span><span class="s2">&quot;message&quot;</span><span class="p">:</span> <span class="s2">&quot;Transition plan deleted successfully&quot;</span><span class="p">}</span>
        
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error deleting transition plan </span><span class="si">{</span><span class="n">plan_id</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error deleting transition plan&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="get_transition_summary">
<a class="viewcode-back" href="../../../api/index.html#api.v1.career_transition.get_transition_summary">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/summary&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">CareerTransitionSummary</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_transition_summary</span><span class="p">(</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get career transition summary statistics for the user.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        
        <span class="c1"># Get user&#39;s plans</span>
        <span class="n">plans</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">CareerTransitionPlan</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
            <span class="n">CareerTransitionPlan</span><span class="o">.</span><span class="n">user_id</span> <span class="o">==</span> <span class="n">user_id</span><span class="p">,</span>
            <span class="n">CareerTransitionPlan</span><span class="o">.</span><span class="n">is_active</span> <span class="o">==</span> <span class="kc">True</span>
        <span class="p">)</span><span class="o">.</span><span class="n">all</span><span class="p">()</span>
        
        <span class="n">total_plans</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">plans</span><span class="p">)</span>
        <span class="n">active_plans</span> <span class="o">=</span> <span class="nb">len</span><span class="p">([</span><span class="n">p</span> <span class="k">for</span> <span class="n">p</span> <span class="ow">in</span> <span class="n">plans</span> <span class="k">if</span> <span class="n">p</span><span class="o">.</span><span class="n">status</span> <span class="o">==</span> <span class="s1">&#39;active&#39;</span><span class="p">])</span>
        <span class="n">completed_plans</span> <span class="o">=</span> <span class="nb">len</span><span class="p">([</span><span class="n">p</span> <span class="k">for</span> <span class="n">p</span> <span class="ow">in</span> <span class="n">plans</span> <span class="k">if</span> <span class="n">p</span><span class="o">.</span><span class="n">status</span> <span class="o">==</span> <span class="s1">&#39;completed&#39;</span><span class="p">])</span>
        
        <span class="n">total_budget_allocated</span> <span class="o">=</span> <span class="nb">sum</span><span class="p">(</span><span class="n">p</span><span class="o">.</span><span class="n">budget_max</span> <span class="ow">or</span> <span class="mf">0.0</span> <span class="k">for</span> <span class="n">p</span> <span class="ow">in</span> <span class="n">plans</span><span class="p">)</span>
        
        <span class="c1"># Calculate average completion time for completed plans</span>
        <span class="n">completed_durations</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">for</span> <span class="n">plan</span> <span class="ow">in</span> <span class="n">plans</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">plan</span><span class="o">.</span><span class="n">status</span> <span class="o">==</span> <span class="s1">&#39;completed&#39;</span> <span class="ow">and</span> <span class="n">plan</span><span class="o">.</span><span class="n">created_at</span><span class="p">:</span>
                <span class="c1"># This is a simplified calculation - in practice, you&#39;d track actual completion dates</span>
                <span class="n">completed_durations</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">plan</span><span class="o">.</span><span class="n">timeline_months</span> <span class="ow">or</span> <span class="mi">12</span><span class="p">)</span>
        
        <span class="n">avg_completion_time</span> <span class="o">=</span> <span class="nb">sum</span><span class="p">(</span><span class="n">completed_durations</span><span class="p">)</span> <span class="o">/</span> <span class="nb">len</span><span class="p">(</span><span class="n">completed_durations</span><span class="p">)</span> <span class="k">if</span> <span class="n">completed_durations</span> <span class="k">else</span> <span class="kc">None</span>
        <span class="n">success_rate</span> <span class="o">=</span> <span class="n">completed_plans</span> <span class="o">/</span> <span class="n">total_plans</span> <span class="k">if</span> <span class="n">total_plans</span> <span class="o">&gt;</span> <span class="mi">0</span> <span class="k">else</span> <span class="kc">None</span>
        
        <span class="k">return</span> <span class="n">CareerTransitionSummary</span><span class="p">(</span>
            <span class="n">total_plans</span><span class="o">=</span><span class="n">total_plans</span><span class="p">,</span>
            <span class="n">active_plans</span><span class="o">=</span><span class="n">active_plans</span><span class="p">,</span>
            <span class="n">completed_plans</span><span class="o">=</span><span class="n">completed_plans</span><span class="p">,</span>
            <span class="n">total_budget_allocated</span><span class="o">=</span><span class="n">total_budget_allocated</span><span class="p">,</span>
            <span class="n">total_budget_spent</span><span class="o">=</span><span class="mf">0.0</span><span class="p">,</span>  <span class="c1"># TODO: Calculate actual spending</span>
            <span class="n">average_completion_time_months</span><span class="o">=</span><span class="n">avg_completion_time</span><span class="p">,</span>
            <span class="n">success_rate</span><span class="o">=</span><span class="n">success_rate</span><span class="p">,</span>
            <span class="n">popular_target_roles</span><span class="o">=</span><span class="p">[],</span>  <span class="c1"># TODO: Implement analytics</span>
            <span class="n">trending_certifications</span><span class="o">=</span><span class="p">[]</span>  <span class="c1"># TODO: Implement analytics</span>
        <span class="p">)</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error generating transition summary: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error generating transition summary&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="generate_career_summary_pdf">
<a class="viewcode-back" href="../../../api/index.html#api.v1.career_transition.generate_career_summary_pdf">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/reports/career-summary&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">generate_career_summary_pdf</span><span class="p">(</span>
    <span class="n">include_transition_plans</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">True</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Include transition plans in report&quot;</span><span class="p">),</span>
    <span class="n">include_cost_analysis</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">True</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Include cost analysis in report&quot;</span><span class="p">),</span>
    <span class="n">include_recommendations</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">True</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Include recommendations in report&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Generate comprehensive career summary PDF report.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Generating career summary PDF for user </span><span class="si">{</span><span class="n">user_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

        <span class="n">pdf_generator</span> <span class="o">=</span> <span class="n">EnhancedPDFReportGenerator</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>

        <span class="n">pdf_bytes</span> <span class="o">=</span> <span class="n">pdf_generator</span><span class="o">.</span><span class="n">generate_career_summary_report</span><span class="p">(</span>
            <span class="n">user_id</span><span class="o">=</span><span class="n">user_id</span><span class="p">,</span>
            <span class="n">include_transition_plans</span><span class="o">=</span><span class="n">include_transition_plans</span><span class="p">,</span>
            <span class="n">include_cost_analysis</span><span class="o">=</span><span class="n">include_cost_analysis</span><span class="p">,</span>
            <span class="n">include_recommendations</span><span class="o">=</span><span class="n">include_recommendations</span>
        <span class="p">)</span>

        <span class="c1"># Generate filename with timestamp</span>
        <span class="n">timestamp</span> <span class="o">=</span> <span class="n">datetime</span><span class="o">.</span><span class="n">now</span><span class="p">()</span><span class="o">.</span><span class="n">strftime</span><span class="p">(</span><span class="s2">&quot;%Y%m</span><span class="si">%d</span><span class="s2">_%H%M%S&quot;</span><span class="p">)</span>
        <span class="n">filename</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;career_summary_</span><span class="si">{</span><span class="n">user_id</span><span class="si">}</span><span class="s2">_</span><span class="si">{</span><span class="n">timestamp</span><span class="si">}</span><span class="s2">.pdf&quot;</span>

        <span class="k">return</span> <span class="n">Response</span><span class="p">(</span>
            <span class="n">content</span><span class="o">=</span><span class="n">pdf_bytes</span><span class="p">,</span>
            <span class="n">media_type</span><span class="o">=</span><span class="s2">&quot;application/pdf&quot;</span><span class="p">,</span>
            <span class="n">headers</span><span class="o">=</span><span class="p">{</span>
                <span class="s2">&quot;Content-Disposition&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;attachment; filename=</span><span class="si">{</span><span class="n">filename</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
                <span class="s2">&quot;Content-Length&quot;</span><span class="p">:</span> <span class="nb">str</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">pdf_bytes</span><span class="p">))</span>
            <span class="p">}</span>
        <span class="p">)</span>

    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error generating career summary PDF: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error generating career summary PDF&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="generate_transition_plan_pdf">
<a class="viewcode-back" href="../../../api/index.html#api.v1.career_transition.generate_transition_plan_pdf">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/plans/</span><span class="si">{plan_id}</span><span class="s2">/report&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">generate_transition_plan_pdf</span><span class="p">(</span>
    <span class="n">plan_id</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Transition plan ID&quot;</span><span class="p">),</span>
    <span class="n">include_detailed_steps</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">True</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Include detailed steps in report&quot;</span><span class="p">),</span>
    <span class="n">include_cost_projections</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">True</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Include cost projections in report&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Generate detailed PDF report for a specific transition plan.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Generating transition plan PDF for plan </span><span class="si">{</span><span class="n">plan_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

        <span class="c1"># Verify plan ownership</span>
        <span class="n">plan</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">CareerTransitionPlan</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
            <span class="n">CareerTransitionPlan</span><span class="o">.</span><span class="n">id</span> <span class="o">==</span> <span class="n">plan_id</span><span class="p">,</span>
            <span class="n">CareerTransitionPlan</span><span class="o">.</span><span class="n">user_id</span> <span class="o">==</span> <span class="n">user_id</span><span class="p">,</span>
            <span class="n">CareerTransitionPlan</span><span class="o">.</span><span class="n">is_active</span> <span class="o">==</span> <span class="kc">True</span>
        <span class="p">)</span><span class="o">.</span><span class="n">first</span><span class="p">()</span>

        <span class="k">if</span> <span class="ow">not</span> <span class="n">plan</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Transition plan not found&quot;</span><span class="p">)</span>

        <span class="n">pdf_generator</span> <span class="o">=</span> <span class="n">EnhancedPDFReportGenerator</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>

        <span class="n">pdf_bytes</span> <span class="o">=</span> <span class="n">pdf_generator</span><span class="o">.</span><span class="n">generate_transition_plan_report</span><span class="p">(</span>
            <span class="n">plan_id</span><span class="o">=</span><span class="n">plan_id</span><span class="p">,</span>
            <span class="n">user_id</span><span class="o">=</span><span class="n">user_id</span><span class="p">,</span>
            <span class="n">include_detailed_steps</span><span class="o">=</span><span class="n">include_detailed_steps</span><span class="p">,</span>
            <span class="n">include_cost_projections</span><span class="o">=</span><span class="n">include_cost_projections</span>
        <span class="p">)</span>

        <span class="c1"># Generate filename with plan name and timestamp</span>
        <span class="n">timestamp</span> <span class="o">=</span> <span class="n">datetime</span><span class="o">.</span><span class="n">now</span><span class="p">()</span><span class="o">.</span><span class="n">strftime</span><span class="p">(</span><span class="s2">&quot;%Y%m</span><span class="si">%d</span><span class="s2">_%H%M%S&quot;</span><span class="p">)</span>
        <span class="n">safe_plan_name</span> <span class="o">=</span> <span class="s2">&quot;&quot;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">c</span> <span class="k">for</span> <span class="n">c</span> <span class="ow">in</span> <span class="n">plan</span><span class="o">.</span><span class="n">name</span> <span class="k">if</span> <span class="n">c</span><span class="o">.</span><span class="n">isalnum</span><span class="p">()</span> <span class="ow">or</span> <span class="n">c</span> <span class="ow">in</span> <span class="p">(</span><span class="s1">&#39; &#39;</span><span class="p">,</span> <span class="s1">&#39;-&#39;</span><span class="p">,</span> <span class="s1">&#39;_&#39;</span><span class="p">))</span><span class="o">.</span><span class="n">rstrip</span><span class="p">()</span>
        <span class="n">safe_plan_name</span> <span class="o">=</span> <span class="n">safe_plan_name</span><span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="s1">&#39; &#39;</span><span class="p">,</span> <span class="s1">&#39;_&#39;</span><span class="p">)[:</span><span class="mi">30</span><span class="p">]</span>  <span class="c1"># Limit length</span>
        <span class="n">filename</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;transition_plan_</span><span class="si">{</span><span class="n">safe_plan_name</span><span class="si">}</span><span class="s2">_</span><span class="si">{</span><span class="n">timestamp</span><span class="si">}</span><span class="s2">.pdf&quot;</span>

        <span class="k">return</span> <span class="n">Response</span><span class="p">(</span>
            <span class="n">content</span><span class="o">=</span><span class="n">pdf_bytes</span><span class="p">,</span>
            <span class="n">media_type</span><span class="o">=</span><span class="s2">&quot;application/pdf&quot;</span><span class="p">,</span>
            <span class="n">headers</span><span class="o">=</span><span class="p">{</span>
                <span class="s2">&quot;Content-Disposition&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;attachment; filename=</span><span class="si">{</span><span class="n">filename</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
                <span class="s2">&quot;Content-Length&quot;</span><span class="p">:</span> <span class="nb">str</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">pdf_bytes</span><span class="p">))</span>
            <span class="p">}</span>
        <span class="p">)</span>

    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error generating transition plan PDF: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error generating transition plan PDF&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="generate_path_comparison_pdf">
<a class="viewcode-back" href="../../../api/index.html#api.v1.career_transition.generate_path_comparison_pdf">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/reports/comparison&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">generate_path_comparison_pdf</span><span class="p">(</span>
    <span class="n">plan_ids</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;List of plan IDs to compare&quot;</span><span class="p">),</span>
    <span class="n">comparison_currency</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="s2">&quot;USD&quot;</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Currency for cost comparison&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Generate PDF report comparing multiple career transition paths.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">user_id</span> <span class="o">=</span> <span class="n">get_current_user_id</span><span class="p">()</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Generating path comparison PDF for plans </span><span class="si">{</span><span class="n">plan_ids</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">plan_ids</span><span class="p">)</span> <span class="o">&lt;</span> <span class="mi">2</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">400</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;At least 2 plans required for comparison&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">plan_ids</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">5</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">400</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Maximum 5 plans allowed for comparison&quot;</span><span class="p">)</span>

        <span class="c1"># Verify plan ownership</span>
        <span class="n">plans</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">CareerTransitionPlan</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
            <span class="n">CareerTransitionPlan</span><span class="o">.</span><span class="n">id</span><span class="o">.</span><span class="n">in_</span><span class="p">(</span><span class="n">plan_ids</span><span class="p">),</span>
            <span class="n">CareerTransitionPlan</span><span class="o">.</span><span class="n">user_id</span> <span class="o">==</span> <span class="n">user_id</span><span class="p">,</span>
            <span class="n">CareerTransitionPlan</span><span class="o">.</span><span class="n">is_active</span> <span class="o">==</span> <span class="kc">True</span>
        <span class="p">)</span><span class="o">.</span><span class="n">all</span><span class="p">()</span>

        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">plans</span><span class="p">)</span> <span class="o">!=</span> <span class="nb">len</span><span class="p">(</span><span class="n">plan_ids</span><span class="p">):</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;One or more transition plans not found&quot;</span><span class="p">)</span>

        <span class="c1"># Generate comparison report (simplified version)</span>
        <span class="n">pdf_generator</span> <span class="o">=</span> <span class="n">EnhancedPDFReportGenerator</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>

        <span class="c1"># For now, generate individual reports for each plan</span>
        <span class="c1"># TODO: Implement dedicated comparison report</span>
        <span class="n">pdf_bytes</span> <span class="o">=</span> <span class="n">pdf_generator</span><span class="o">.</span><span class="n">generate_career_summary_report</span><span class="p">(</span>
            <span class="n">user_id</span><span class="o">=</span><span class="n">user_id</span><span class="p">,</span>
            <span class="n">include_transition_plans</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
            <span class="n">include_cost_analysis</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
            <span class="n">include_recommendations</span><span class="o">=</span><span class="kc">True</span>
        <span class="p">)</span>

        <span class="c1"># Generate filename</span>
        <span class="n">timestamp</span> <span class="o">=</span> <span class="n">datetime</span><span class="o">.</span><span class="n">now</span><span class="p">()</span><span class="o">.</span><span class="n">strftime</span><span class="p">(</span><span class="s2">&quot;%Y%m</span><span class="si">%d</span><span class="s2">_%H%M%S&quot;</span><span class="p">)</span>
        <span class="n">filename</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;path_comparison_</span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">plans</span><span class="p">)</span><span class="si">}</span><span class="s2">_plans_</span><span class="si">{</span><span class="n">timestamp</span><span class="si">}</span><span class="s2">.pdf&quot;</span>

        <span class="k">return</span> <span class="n">Response</span><span class="p">(</span>
            <span class="n">content</span><span class="o">=</span><span class="n">pdf_bytes</span><span class="p">,</span>
            <span class="n">media_type</span><span class="o">=</span><span class="s2">&quot;application/pdf&quot;</span><span class="p">,</span>
            <span class="n">headers</span><span class="o">=</span><span class="p">{</span>
                <span class="s2">&quot;Content-Disposition&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;attachment; filename=</span><span class="si">{</span><span class="n">filename</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
                <span class="s2">&quot;Content-Length&quot;</span><span class="p">:</span> <span class="nb">str</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">pdf_bytes</span><span class="p">))</span>
            <span class="p">}</span>
        <span class="p">)</span>

    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error generating path comparison PDF: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error generating path comparison PDF&quot;</span><span class="p">)</span></div>

</pre></div>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, CertPathFinder Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>