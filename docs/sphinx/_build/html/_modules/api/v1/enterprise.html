

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../../../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>api.v1.enterprise &mdash; CertPathFinder 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../../../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../../../_static/custom.css?v=c14262df" />

  
      <script src="../../../_static/jquery.js?v=5d32c60e"></script>
      <script src="../../../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../../../_static/documentation_options.js?v=8d563738"></script>
      <script src="../../../_static/doctools.js?v=9bcbadda"></script>
      <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../../../index.html" class="icon icon-home">
            CertPathFinder
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../../../quickstart.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../../api/index.html">API Reference</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../../index.html">CertPathFinder</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../../index.html">Module code</a></li>
      <li class="breadcrumb-item active">api.v1.enterprise</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>Source code for api.v1.enterprise</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;FastAPI endpoints for enterprise dashboard and organizational management.</span>

<span class="sd">This module provides comprehensive API endpoints for enterprise deployment</span>
<span class="sd">including organization management, user administration, analytics, and licensing.</span>
<span class="sd">&quot;&quot;&quot;</span>

<span class="kn">import</span><span class="w"> </span><span class="nn">logging</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">typing</span><span class="w"> </span><span class="kn">import</span> <span class="n">List</span><span class="p">,</span> <span class="n">Optional</span><span class="p">,</span> <span class="n">Dict</span><span class="p">,</span> <span class="n">Any</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">fastapi</span><span class="w"> </span><span class="kn">import</span> <span class="n">APIRouter</span><span class="p">,</span> <span class="n">Depends</span><span class="p">,</span> <span class="n">HTTPException</span><span class="p">,</span> <span class="n">Query</span><span class="p">,</span> <span class="n">Path</span><span class="p">,</span> <span class="n">status</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">sqlalchemy.orm</span><span class="w"> </span><span class="kn">import</span> <span class="n">Session</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">datetime</span><span class="w"> </span><span class="kn">import</span> <span class="n">datetime</span><span class="p">,</span> <span class="n">timedelta</span>

<span class="kn">from</span><span class="w"> </span><span class="nn">database</span><span class="w"> </span><span class="kn">import</span> <span class="n">get_db</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">services.enterprise_service</span><span class="w"> </span><span class="kn">import</span> <span class="n">EnterpriseService</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">schemas.enterprise</span><span class="w"> </span><span class="kn">import</span> <span class="p">(</span>
    <span class="n">OrganizationResponse</span><span class="p">,</span> <span class="n">OrganizationCreate</span><span class="p">,</span> <span class="n">OrganizationUpdate</span><span class="p">,</span>
    <span class="n">EnterpriseUserResponse</span><span class="p">,</span> <span class="n">EnterpriseUserCreate</span><span class="p">,</span> <span class="n">EnterpriseUserUpdate</span><span class="p">,</span>
    <span class="n">DepartmentResponse</span><span class="p">,</span> <span class="n">DepartmentCreate</span><span class="p">,</span> <span class="n">DepartmentUpdate</span><span class="p">,</span>
    <span class="n">LicenseResponse</span><span class="p">,</span> <span class="n">LicenseAssignment</span><span class="p">,</span>
    <span class="n">OrganizationAnalyticsResponse</span><span class="p">,</span> <span class="n">DashboardResponse</span><span class="p">,</span>
    <span class="n">OrganizationListResponse</span><span class="p">,</span> <span class="n">UserListResponse</span>
<span class="p">)</span>

<span class="n">logger</span> <span class="o">=</span> <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="vm">__name__</span><span class="p">)</span>
<span class="n">router</span> <span class="o">=</span> <span class="n">APIRouter</span><span class="p">(</span><span class="n">prefix</span><span class="o">=</span><span class="s2">&quot;/enterprise&quot;</span><span class="p">,</span> <span class="n">tags</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;Enterprise Dashboard&quot;</span><span class="p">])</span>


<div class="viewcode-block" id="get_current_admin_user">
<a class="viewcode-back" href="../../../api/index.html#api.v1.enterprise.get_current_admin_user">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">get_current_admin_user</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get current admin user ID from authentication context.&quot;&quot;&quot;</span>
    <span class="c1"># TODO: Implement proper admin authentication</span>
    <span class="k">return</span> <span class="s2">&quot;admin_user_1&quot;</span></div>



<div class="viewcode-block" id="require_super_admin">
<a class="viewcode-back" href="../../../api/index.html#api.v1.enterprise.require_super_admin">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">require_super_admin</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Require super admin privileges.&quot;&quot;&quot;</span>
    <span class="c1"># TODO: Implement proper role-based access control</span>
    <span class="k">pass</span></div>



<div class="viewcode-block" id="require_org_admin">
<a class="viewcode-back" href="../../../api/index.html#api.v1.enterprise.require_org_admin">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">require_org_admin</span><span class="p">(</span><span class="n">org_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Require organization admin privileges.&quot;&quot;&quot;</span>
    <span class="c1"># TODO: Implement proper organization-level access control</span>
    <span class="k">pass</span></div>



<span class="c1"># Organization Management Endpoints</span>

<div class="viewcode-block" id="create_organization">
<a class="viewcode-back" href="../../../api/index.html#api.v1.enterprise.create_organization">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/organizations&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">OrganizationResponse</span><span class="p">,</span> <span class="n">status_code</span><span class="o">=</span><span class="n">status</span><span class="o">.</span><span class="n">HTTP_201_CREATED</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">create_organization</span><span class="p">(</span>
    <span class="n">organization_data</span><span class="p">:</span> <span class="n">OrganizationCreate</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">),</span>
    <span class="n">_</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_admin_user</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Create a new enterprise organization.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">require_super_admin</span><span class="p">()</span>
        
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Creating new organization: </span><span class="si">{</span><span class="n">organization_data</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">enterprise_service</span> <span class="o">=</span> <span class="n">EnterpriseService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        <span class="n">organization</span> <span class="o">=</span> <span class="n">enterprise_service</span><span class="o">.</span><span class="n">create_organization</span><span class="p">(</span><span class="n">organization_data</span><span class="o">.</span><span class="n">dict</span><span class="p">())</span>
        
        <span class="k">return</span> <span class="n">OrganizationResponse</span><span class="p">(</span><span class="o">**</span><span class="n">organization</span><span class="o">.</span><span class="n">to_dict</span><span class="p">())</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error creating organization: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error creating organization&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="list_organizations">
<a class="viewcode-back" href="../../../api/index.html#api.v1.enterprise.list_organizations">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/organizations&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">OrganizationListResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">list_organizations</span><span class="p">(</span>
    <span class="n">page</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Page number&quot;</span><span class="p">),</span>
    <span class="n">page_size</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">20</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">le</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Items per page&quot;</span><span class="p">),</span>
    <span class="n">organization_type</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by organization type&quot;</span><span class="p">),</span>
    <span class="n">subscription_tier</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by subscription tier&quot;</span><span class="p">),</span>
    <span class="n">is_active</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by active status&quot;</span><span class="p">),</span>
    <span class="n">search</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Search organizations&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">),</span>
    <span class="n">_</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_admin_user</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;List all organizations with pagination and filtering.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">require_super_admin</span><span class="p">()</span>
        
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Listing organizations - page: </span><span class="si">{</span><span class="n">page</span><span class="si">}</span><span class="s2">, size: </span><span class="si">{</span><span class="n">page_size</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">enterprise_service</span> <span class="o">=</span> <span class="n">EnterpriseService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        
        <span class="n">filters</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="k">if</span> <span class="n">organization_type</span><span class="p">:</span>
            <span class="n">filters</span><span class="p">[</span><span class="s1">&#39;organization_type&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">organization_type</span>
        <span class="k">if</span> <span class="n">subscription_tier</span><span class="p">:</span>
            <span class="n">filters</span><span class="p">[</span><span class="s1">&#39;subscription_tier&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">subscription_tier</span>
        <span class="k">if</span> <span class="n">is_active</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">filters</span><span class="p">[</span><span class="s1">&#39;is_active&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">is_active</span>
        <span class="k">if</span> <span class="n">search</span><span class="p">:</span>
            <span class="n">filters</span><span class="p">[</span><span class="s1">&#39;search&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">search</span>
        
        <span class="n">organizations</span><span class="p">,</span> <span class="n">total_count</span> <span class="o">=</span> <span class="n">enterprise_service</span><span class="o">.</span><span class="n">list_organizations</span><span class="p">(</span>
            <span class="n">page</span><span class="o">=</span><span class="n">page</span><span class="p">,</span>
            <span class="n">page_size</span><span class="o">=</span><span class="n">page_size</span><span class="p">,</span>
            <span class="n">filters</span><span class="o">=</span><span class="n">filters</span>
        <span class="p">)</span>
        
        <span class="k">return</span> <span class="n">OrganizationListResponse</span><span class="p">(</span>
            <span class="n">organizations</span><span class="o">=</span><span class="p">[</span><span class="n">OrganizationResponse</span><span class="p">(</span><span class="o">**</span><span class="n">org</span><span class="o">.</span><span class="n">to_dict</span><span class="p">())</span> <span class="k">for</span> <span class="n">org</span> <span class="ow">in</span> <span class="n">organizations</span><span class="p">],</span>
            <span class="n">total_count</span><span class="o">=</span><span class="n">total_count</span><span class="p">,</span>
            <span class="n">page</span><span class="o">=</span><span class="n">page</span><span class="p">,</span>
            <span class="n">page_size</span><span class="o">=</span><span class="n">page_size</span><span class="p">,</span>
            <span class="n">total_pages</span><span class="o">=</span><span class="p">(</span><span class="n">total_count</span> <span class="o">+</span> <span class="n">page_size</span> <span class="o">-</span> <span class="mi">1</span><span class="p">)</span> <span class="o">//</span> <span class="n">page_size</span>
        <span class="p">)</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error listing organizations: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error listing organizations&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="get_organization">
<a class="viewcode-back" href="../../../api/index.html#api.v1.enterprise.get_organization">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/organizations/</span><span class="si">{org_id}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">OrganizationResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_organization</span><span class="p">(</span>
    <span class="n">org_id</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Organization ID&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">),</span>
    <span class="n">_</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_admin_user</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get organization by ID.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">require_org_admin</span><span class="p">(</span><span class="n">org_id</span><span class="p">)</span>
        
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Getting organization: </span><span class="si">{</span><span class="n">org_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">enterprise_service</span> <span class="o">=</span> <span class="n">EnterpriseService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        <span class="n">organization</span> <span class="o">=</span> <span class="n">enterprise_service</span><span class="o">.</span><span class="n">get_organization</span><span class="p">(</span><span class="n">org_id</span><span class="p">)</span>
        
        <span class="k">if</span> <span class="ow">not</span> <span class="n">organization</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Organization not found&quot;</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="n">OrganizationResponse</span><span class="p">(</span><span class="o">**</span><span class="n">organization</span><span class="o">.</span><span class="n">to_dict</span><span class="p">())</span>
        
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error getting organization </span><span class="si">{</span><span class="n">org_id</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error getting organization&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="update_organization">
<a class="viewcode-back" href="../../../api/index.html#api.v1.enterprise.update_organization">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">put</span><span class="p">(</span><span class="s2">&quot;/organizations/</span><span class="si">{org_id}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">OrganizationResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">update_organization</span><span class="p">(</span>
    <span class="n">org_id</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Organization ID&quot;</span><span class="p">),</span>
    <span class="n">update_data</span><span class="p">:</span> <span class="n">OrganizationUpdate</span> <span class="o">=</span> <span class="o">...</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">),</span>
    <span class="n">_</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_admin_user</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Update organization.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">require_org_admin</span><span class="p">(</span><span class="n">org_id</span><span class="p">)</span>
        
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Updating organization: </span><span class="si">{</span><span class="n">org_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">enterprise_service</span> <span class="o">=</span> <span class="n">EnterpriseService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        <span class="n">organization</span> <span class="o">=</span> <span class="n">enterprise_service</span><span class="o">.</span><span class="n">update_organization</span><span class="p">(</span>
            <span class="n">org_id</span><span class="p">,</span> 
            <span class="n">update_data</span><span class="o">.</span><span class="n">dict</span><span class="p">(</span><span class="n">exclude_unset</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
        <span class="p">)</span>
        
        <span class="k">if</span> <span class="ow">not</span> <span class="n">organization</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Organization not found&quot;</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="n">OrganizationResponse</span><span class="p">(</span><span class="o">**</span><span class="n">organization</span><span class="o">.</span><span class="n">to_dict</span><span class="p">())</span>
        
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error updating organization </span><span class="si">{</span><span class="n">org_id</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error updating organization&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="delete_organization">
<a class="viewcode-back" href="../../../api/index.html#api.v1.enterprise.delete_organization">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">delete</span><span class="p">(</span><span class="s2">&quot;/organizations/</span><span class="si">{org_id}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">delete_organization</span><span class="p">(</span>
    <span class="n">org_id</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Organization ID&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">),</span>
    <span class="n">_</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_admin_user</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Delete organization (soft delete).&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">require_super_admin</span><span class="p">()</span>
        
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Deleting organization: </span><span class="si">{</span><span class="n">org_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">enterprise_service</span> <span class="o">=</span> <span class="n">EnterpriseService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        <span class="n">success</span> <span class="o">=</span> <span class="n">enterprise_service</span><span class="o">.</span><span class="n">delete_organization</span><span class="p">(</span><span class="n">org_id</span><span class="p">)</span>
        
        <span class="k">if</span> <span class="ow">not</span> <span class="n">success</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Organization not found&quot;</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="p">{</span><span class="s2">&quot;message&quot;</span><span class="p">:</span> <span class="s2">&quot;Organization deleted successfully&quot;</span><span class="p">}</span>
        
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error deleting organization </span><span class="si">{</span><span class="n">org_id</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error deleting organization&quot;</span><span class="p">)</span></div>



<span class="c1"># User Management Endpoints</span>

<div class="viewcode-block" id="create_user">
<a class="viewcode-back" href="../../../api/index.html#api.v1.enterprise.create_user">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/organizations/</span><span class="si">{org_id}</span><span class="s2">/users&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">EnterpriseUserResponse</span><span class="p">,</span> <span class="n">status_code</span><span class="o">=</span><span class="n">status</span><span class="o">.</span><span class="n">HTTP_201_CREATED</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">create_user</span><span class="p">(</span>
    <span class="n">org_id</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Organization ID&quot;</span><span class="p">),</span>
    <span class="n">user_data</span><span class="p">:</span> <span class="n">EnterpriseUserCreate</span> <span class="o">=</span> <span class="o">...</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">),</span>
    <span class="n">_</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_admin_user</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Create a new user in an organization.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">require_org_admin</span><span class="p">(</span><span class="n">org_id</span><span class="p">)</span>
        
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Creating user in organization </span><span class="si">{</span><span class="n">org_id</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">user_data</span><span class="o">.</span><span class="n">email</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">enterprise_service</span> <span class="o">=</span> <span class="n">EnterpriseService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        
        <span class="c1"># Add organization ID to user data</span>
        <span class="n">user_dict</span> <span class="o">=</span> <span class="n">user_data</span><span class="o">.</span><span class="n">dict</span><span class="p">()</span>
        <span class="n">user_dict</span><span class="p">[</span><span class="s1">&#39;organization_id&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">org_id</span>
        
        <span class="n">user</span> <span class="o">=</span> <span class="n">enterprise_service</span><span class="o">.</span><span class="n">create_user</span><span class="p">(</span><span class="n">user_dict</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="n">EnterpriseUserResponse</span><span class="p">(</span><span class="o">**</span><span class="n">user</span><span class="o">.</span><span class="n">to_dict</span><span class="p">())</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error creating user: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error creating user&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="list_organization_users">
<a class="viewcode-back" href="../../../api/index.html#api.v1.enterprise.list_organization_users">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/organizations/</span><span class="si">{org_id}</span><span class="s2">/users&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">UserListResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">list_organization_users</span><span class="p">(</span>
    <span class="n">org_id</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Organization ID&quot;</span><span class="p">),</span>
    <span class="n">page</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Page number&quot;</span><span class="p">),</span>
    <span class="n">page_size</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="mi">50</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">le</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Items per page&quot;</span><span class="p">),</span>
    <span class="n">role</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by user role&quot;</span><span class="p">),</span>
    <span class="n">department_id</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by department&quot;</span><span class="p">),</span>
    <span class="n">is_active</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Filter by active status&quot;</span><span class="p">),</span>
    <span class="n">search</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Search users&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">),</span>
    <span class="n">_</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_admin_user</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;List users in an organization.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">require_org_admin</span><span class="p">(</span><span class="n">org_id</span><span class="p">)</span>
        
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Listing users for organization </span><span class="si">{</span><span class="n">org_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">enterprise_service</span> <span class="o">=</span> <span class="n">EnterpriseService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        
        <span class="n">filters</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="k">if</span> <span class="n">role</span><span class="p">:</span>
            <span class="n">filters</span><span class="p">[</span><span class="s1">&#39;role&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">role</span>
        <span class="k">if</span> <span class="n">department_id</span><span class="p">:</span>
            <span class="n">filters</span><span class="p">[</span><span class="s1">&#39;department_id&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">department_id</span>
        <span class="k">if</span> <span class="n">is_active</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">filters</span><span class="p">[</span><span class="s1">&#39;is_active&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">is_active</span>
        <span class="k">if</span> <span class="n">search</span><span class="p">:</span>
            <span class="n">filters</span><span class="p">[</span><span class="s1">&#39;search&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">search</span>
        
        <span class="n">users</span><span class="p">,</span> <span class="n">total_count</span> <span class="o">=</span> <span class="n">enterprise_service</span><span class="o">.</span><span class="n">list_organization_users</span><span class="p">(</span>
            <span class="n">org_id</span><span class="o">=</span><span class="n">org_id</span><span class="p">,</span>
            <span class="n">page</span><span class="o">=</span><span class="n">page</span><span class="p">,</span>
            <span class="n">page_size</span><span class="o">=</span><span class="n">page_size</span><span class="p">,</span>
            <span class="n">filters</span><span class="o">=</span><span class="n">filters</span>
        <span class="p">)</span>
        
        <span class="k">return</span> <span class="n">UserListResponse</span><span class="p">(</span>
            <span class="n">users</span><span class="o">=</span><span class="p">[</span><span class="n">EnterpriseUserResponse</span><span class="p">(</span><span class="o">**</span><span class="n">user</span><span class="o">.</span><span class="n">to_dict</span><span class="p">())</span> <span class="k">for</span> <span class="n">user</span> <span class="ow">in</span> <span class="n">users</span><span class="p">],</span>
            <span class="n">total_count</span><span class="o">=</span><span class="n">total_count</span><span class="p">,</span>
            <span class="n">page</span><span class="o">=</span><span class="n">page</span><span class="p">,</span>
            <span class="n">page_size</span><span class="o">=</span><span class="n">page_size</span><span class="p">,</span>
            <span class="n">total_pages</span><span class="o">=</span><span class="p">(</span><span class="n">total_count</span> <span class="o">+</span> <span class="n">page_size</span> <span class="o">-</span> <span class="mi">1</span><span class="p">)</span> <span class="o">//</span> <span class="n">page_size</span>
        <span class="p">)</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error listing users for organization </span><span class="si">{</span><span class="n">org_id</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error listing users&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="get_user">
<a class="viewcode-back" href="../../../api/index.html#api.v1.enterprise.get_user">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/users/</span><span class="si">{user_id}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">EnterpriseUserResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_user</span><span class="p">(</span>
    <span class="n">user_id</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;User ID&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">),</span>
    <span class="n">_</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_admin_user</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get user by ID.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Getting user: </span><span class="si">{</span><span class="n">user_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">enterprise_service</span> <span class="o">=</span> <span class="n">EnterpriseService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        <span class="n">user</span> <span class="o">=</span> <span class="n">enterprise_service</span><span class="o">.</span><span class="n">get_user</span><span class="p">(</span><span class="n">user_id</span><span class="p">)</span>
        
        <span class="k">if</span> <span class="ow">not</span> <span class="n">user</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;User not found&quot;</span><span class="p">)</span>
        
        <span class="n">require_org_admin</span><span class="p">(</span><span class="n">user</span><span class="o">.</span><span class="n">organization_id</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="n">EnterpriseUserResponse</span><span class="p">(</span><span class="o">**</span><span class="n">user</span><span class="o">.</span><span class="n">to_dict</span><span class="p">())</span>
        
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error getting user </span><span class="si">{</span><span class="n">user_id</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error getting user&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="update_user">
<a class="viewcode-back" href="../../../api/index.html#api.v1.enterprise.update_user">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">put</span><span class="p">(</span><span class="s2">&quot;/users/</span><span class="si">{user_id}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">EnterpriseUserResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">update_user</span><span class="p">(</span>
    <span class="n">user_id</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;User ID&quot;</span><span class="p">),</span>
    <span class="n">update_data</span><span class="p">:</span> <span class="n">EnterpriseUserUpdate</span> <span class="o">=</span> <span class="o">...</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">),</span>
    <span class="n">_</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_admin_user</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Update user.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Updating user: </span><span class="si">{</span><span class="n">user_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">enterprise_service</span> <span class="o">=</span> <span class="n">EnterpriseService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        
        <span class="c1"># Check user exists and get org ID for permission check</span>
        <span class="n">existing_user</span> <span class="o">=</span> <span class="n">enterprise_service</span><span class="o">.</span><span class="n">get_user</span><span class="p">(</span><span class="n">user_id</span><span class="p">)</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">existing_user</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;User not found&quot;</span><span class="p">)</span>
        
        <span class="n">require_org_admin</span><span class="p">(</span><span class="n">existing_user</span><span class="o">.</span><span class="n">organization_id</span><span class="p">)</span>
        
        <span class="n">user</span> <span class="o">=</span> <span class="n">enterprise_service</span><span class="o">.</span><span class="n">update_user</span><span class="p">(</span>
            <span class="n">user_id</span><span class="p">,</span> 
            <span class="n">update_data</span><span class="o">.</span><span class="n">dict</span><span class="p">(</span><span class="n">exclude_unset</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
        <span class="p">)</span>
        
        <span class="k">return</span> <span class="n">EnterpriseUserResponse</span><span class="p">(</span><span class="o">**</span><span class="n">user</span><span class="o">.</span><span class="n">to_dict</span><span class="p">())</span>
        
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error updating user </span><span class="si">{</span><span class="n">user_id</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error updating user&quot;</span><span class="p">)</span></div>



<span class="c1"># Department Management Endpoints</span>

<div class="viewcode-block" id="create_department">
<a class="viewcode-back" href="../../../api/index.html#api.v1.enterprise.create_department">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/organizations/</span><span class="si">{org_id}</span><span class="s2">/departments&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">DepartmentResponse</span><span class="p">,</span> <span class="n">status_code</span><span class="o">=</span><span class="n">status</span><span class="o">.</span><span class="n">HTTP_201_CREATED</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">create_department</span><span class="p">(</span>
    <span class="n">org_id</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Organization ID&quot;</span><span class="p">),</span>
    <span class="n">department_data</span><span class="p">:</span> <span class="n">DepartmentCreate</span> <span class="o">=</span> <span class="o">...</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">),</span>
    <span class="n">_</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_admin_user</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Create a new department in an organization.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">require_org_admin</span><span class="p">(</span><span class="n">org_id</span><span class="p">)</span>
        
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Creating department in organization </span><span class="si">{</span><span class="n">org_id</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">department_data</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">enterprise_service</span> <span class="o">=</span> <span class="n">EnterpriseService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        
        <span class="c1"># Add organization ID to department data</span>
        <span class="n">dept_dict</span> <span class="o">=</span> <span class="n">department_data</span><span class="o">.</span><span class="n">dict</span><span class="p">()</span>
        <span class="n">dept_dict</span><span class="p">[</span><span class="s1">&#39;organization_id&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">org_id</span>
        
        <span class="n">department</span> <span class="o">=</span> <span class="n">enterprise_service</span><span class="o">.</span><span class="n">create_department</span><span class="p">(</span><span class="n">dept_dict</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="n">DepartmentResponse</span><span class="p">(</span><span class="o">**</span><span class="n">department</span><span class="o">.</span><span class="n">to_dict</span><span class="p">())</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error creating department: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error creating department&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="list_organization_departments">
<a class="viewcode-back" href="../../../api/index.html#api.v1.enterprise.list_organization_departments">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/organizations/</span><span class="si">{org_id}</span><span class="s2">/departments&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">List</span><span class="p">[</span><span class="n">DepartmentResponse</span><span class="p">])</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">list_organization_departments</span><span class="p">(</span>
    <span class="n">org_id</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Organization ID&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">),</span>
    <span class="n">_</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_admin_user</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;List departments in an organization.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">require_org_admin</span><span class="p">(</span><span class="n">org_id</span><span class="p">)</span>
        
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Listing departments for organization </span><span class="si">{</span><span class="n">org_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">enterprise_service</span> <span class="o">=</span> <span class="n">EnterpriseService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        <span class="n">departments</span> <span class="o">=</span> <span class="n">enterprise_service</span><span class="o">.</span><span class="n">list_organization_departments</span><span class="p">(</span><span class="n">org_id</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="p">[</span><span class="n">DepartmentResponse</span><span class="p">(</span><span class="o">**</span><span class="n">dept</span><span class="o">.</span><span class="n">to_dict</span><span class="p">())</span> <span class="k">for</span> <span class="n">dept</span> <span class="ow">in</span> <span class="n">departments</span><span class="p">]</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error listing departments for organization </span><span class="si">{</span><span class="n">org_id</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error listing departments&quot;</span><span class="p">)</span></div>



<span class="c1"># License Management Endpoints</span>

<div class="viewcode-block" id="assign_license">
<a class="viewcode-back" href="../../../api/index.html#api.v1.enterprise.assign_license">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/users/</span><span class="si">{user_id}</span><span class="s2">/license&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">LicenseResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">assign_license</span><span class="p">(</span>
    <span class="n">user_id</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;User ID&quot;</span><span class="p">),</span>
    <span class="n">license_data</span><span class="p">:</span> <span class="n">LicenseAssignment</span> <span class="o">=</span> <span class="o">...</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">),</span>
    <span class="n">_</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_admin_user</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Assign a license to a user.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Assigning license to user: </span><span class="si">{</span><span class="n">user_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">enterprise_service</span> <span class="o">=</span> <span class="n">EnterpriseService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        
        <span class="c1"># Check user exists and get org ID for permission check</span>
        <span class="n">user</span> <span class="o">=</span> <span class="n">enterprise_service</span><span class="o">.</span><span class="n">get_user</span><span class="p">(</span><span class="n">user_id</span><span class="p">)</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">user</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;User not found&quot;</span><span class="p">)</span>
        
        <span class="n">require_org_admin</span><span class="p">(</span><span class="n">user</span><span class="o">.</span><span class="n">organization_id</span><span class="p">)</span>
        
        <span class="n">license</span> <span class="o">=</span> <span class="n">enterprise_service</span><span class="o">.</span><span class="n">assign_license</span><span class="p">(</span>
            <span class="n">user_id</span><span class="o">=</span><span class="n">user_id</span><span class="p">,</span>
            <span class="n">license_type</span><span class="o">=</span><span class="n">license_data</span><span class="o">.</span><span class="n">license_type</span><span class="p">,</span>
            <span class="n">expiration_date</span><span class="o">=</span><span class="n">license_data</span><span class="o">.</span><span class="n">expiration_date</span>
        <span class="p">)</span>
        
        <span class="k">return</span> <span class="n">LicenseResponse</span><span class="p">(</span><span class="o">**</span><span class="n">license</span><span class="o">.</span><span class="n">to_dict</span><span class="p">())</span>
        
    <span class="k">except</span> <span class="n">HTTPException</span><span class="p">:</span>
        <span class="k">raise</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error assigning license to user </span><span class="si">{</span><span class="n">user_id</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error assigning license&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="get_license_usage">
<a class="viewcode-back" href="../../../api/index.html#api.v1.enterprise.get_license_usage">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/organizations/</span><span class="si">{org_id}</span><span class="s2">/license-usage&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_license_usage</span><span class="p">(</span>
    <span class="n">org_id</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Organization ID&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">),</span>
    <span class="n">_</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_admin_user</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get license usage statistics for an organization.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">require_org_admin</span><span class="p">(</span><span class="n">org_id</span><span class="p">)</span>
        
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Getting license usage for organization </span><span class="si">{</span><span class="n">org_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">enterprise_service</span> <span class="o">=</span> <span class="n">EnterpriseService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        <span class="n">usage_stats</span> <span class="o">=</span> <span class="n">enterprise_service</span><span class="o">.</span><span class="n">get_organization_license_usage</span><span class="p">(</span><span class="n">org_id</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="n">usage_stats</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error getting license usage for organization </span><span class="si">{</span><span class="n">org_id</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error getting license usage&quot;</span><span class="p">)</span></div>



<span class="c1"># Analytics and Dashboard Endpoints</span>

<div class="viewcode-block" id="get_organization_analytics">
<a class="viewcode-back" href="../../../api/index.html#api.v1.enterprise.get_organization_analytics">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/organizations/</span><span class="si">{org_id}</span><span class="s2">/analytics&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">OrganizationAnalyticsResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_organization_analytics</span><span class="p">(</span>
    <span class="n">org_id</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Organization ID&quot;</span><span class="p">),</span>
    <span class="n">period_type</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="s2">&quot;monthly&quot;</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Analytics period type&quot;</span><span class="p">),</span>
    <span class="n">start_date</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Start date (ISO format)&quot;</span><span class="p">),</span>
    <span class="n">end_date</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">Query</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;End date (ISO format)&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">),</span>
    <span class="n">_</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_admin_user</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get comprehensive analytics for an organization.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">require_org_admin</span><span class="p">(</span><span class="n">org_id</span><span class="p">)</span>
        
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Getting analytics for organization </span><span class="si">{</span><span class="n">org_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">enterprise_service</span> <span class="o">=</span> <span class="n">EnterpriseService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        
        <span class="c1"># Parse dates if provided</span>
        <span class="n">start_dt</span> <span class="o">=</span> <span class="n">datetime</span><span class="o">.</span><span class="n">fromisoformat</span><span class="p">(</span><span class="n">start_date</span><span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="s1">&#39;Z&#39;</span><span class="p">,</span> <span class="s1">&#39;+00:00&#39;</span><span class="p">))</span> <span class="k">if</span> <span class="n">start_date</span> <span class="k">else</span> <span class="kc">None</span>
        <span class="n">end_dt</span> <span class="o">=</span> <span class="n">datetime</span><span class="o">.</span><span class="n">fromisoformat</span><span class="p">(</span><span class="n">end_date</span><span class="o">.</span><span class="n">replace</span><span class="p">(</span><span class="s1">&#39;Z&#39;</span><span class="p">,</span> <span class="s1">&#39;+00:00&#39;</span><span class="p">))</span> <span class="k">if</span> <span class="n">end_date</span> <span class="k">else</span> <span class="kc">None</span>
        
        <span class="n">analytics</span> <span class="o">=</span> <span class="n">enterprise_service</span><span class="o">.</span><span class="n">generate_organization_analytics</span><span class="p">(</span>
            <span class="n">org_id</span><span class="o">=</span><span class="n">org_id</span><span class="p">,</span>
            <span class="n">period_type</span><span class="o">=</span><span class="n">period_type</span><span class="p">,</span>
            <span class="n">start_date</span><span class="o">=</span><span class="n">start_dt</span><span class="p">,</span>
            <span class="n">end_date</span><span class="o">=</span><span class="n">end_dt</span>
        <span class="p">)</span>
        
        <span class="k">return</span> <span class="n">OrganizationAnalyticsResponse</span><span class="p">(</span><span class="o">**</span><span class="n">analytics</span><span class="p">)</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error getting analytics for organization </span><span class="si">{</span><span class="n">org_id</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error getting analytics&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="get_organization_dashboard">
<a class="viewcode-back" href="../../../api/index.html#api.v1.enterprise.get_organization_dashboard">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/organizations/</span><span class="si">{org_id}</span><span class="s2">/dashboard&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">DashboardResponse</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_organization_dashboard</span><span class="p">(</span>
    <span class="n">org_id</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Organization ID&quot;</span><span class="p">),</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">),</span>
    <span class="n">_</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_admin_user</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get comprehensive dashboard data for an organization.&quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">require_org_admin</span><span class="p">(</span><span class="n">org_id</span><span class="p">)</span>
        
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Getting dashboard data for organization </span><span class="si">{</span><span class="n">org_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="n">enterprise_service</span> <span class="o">=</span> <span class="n">EnterpriseService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        <span class="n">dashboard_data</span> <span class="o">=</span> <span class="n">enterprise_service</span><span class="o">.</span><span class="n">get_organization_dashboard_data</span><span class="p">(</span><span class="n">org_id</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="n">DashboardResponse</span><span class="p">(</span><span class="o">**</span><span class="n">dashboard_data</span><span class="p">)</span>
        
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error getting dashboard data for organization </span><span class="si">{</span><span class="n">org_id</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Error getting dashboard data&quot;</span><span class="p">)</span></div>



<div class="viewcode-block" id="health_check">
<a class="viewcode-back" href="../../../api/index.html#api.v1.enterprise.health_check">[docs]</a>
<span class="nd">@router</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/health&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">health_check</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Health check endpoint for enterprise service.&quot;&quot;&quot;</span>
    <span class="k">return</span> <span class="p">{</span>
        <span class="s1">&#39;status&#39;</span><span class="p">:</span> <span class="s1">&#39;healthy&#39;</span><span class="p">,</span>
        <span class="s1">&#39;service&#39;</span><span class="p">:</span> <span class="s1">&#39;Enterprise Dashboard&#39;</span><span class="p">,</span>
        <span class="s1">&#39;version&#39;</span><span class="p">:</span> <span class="s1">&#39;1.0.0&#39;</span><span class="p">,</span>
        <span class="s1">&#39;features&#39;</span><span class="p">:</span> <span class="p">[</span>
            <span class="s1">&#39;organization_management&#39;</span><span class="p">,</span>
            <span class="s1">&#39;user_administration&#39;</span><span class="p">,</span>
            <span class="s1">&#39;department_management&#39;</span><span class="p">,</span>
            <span class="s1">&#39;license_management&#39;</span><span class="p">,</span>
            <span class="s1">&#39;analytics_reporting&#39;</span><span class="p">,</span>
            <span class="s1">&#39;dashboard_insights&#39;</span>
        <span class="p">],</span>
        <span class="s1">&#39;timestamp&#39;</span><span class="p">:</span> <span class="n">datetime</span><span class="o">.</span><span class="n">now</span><span class="p">()</span><span class="o">.</span><span class="n">isoformat</span><span class="p">()</span>
    <span class="p">}</span></div>

</pre></div>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, CertPathFinder Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>