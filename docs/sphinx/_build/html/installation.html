

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Installation Guide &mdash; CertPathFinder 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=c14262df" />

  
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="_static/documentation_options.js?v=8d563738"></script>
      <script src="_static/doctools.js?v=9bcbadda"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Quick Start Guide" href="quickstart.html" />
    <link rel="prev" title="CertPathFinder Documentation" href="index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="index.html" class="icon icon-home">
            CertPathFinder
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started:</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">Installation Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#prerequisites">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="#quick-start-with-docker">Quick Start with Docker</a></li>
<li class="toctree-l2"><a class="reference internal" href="#manual-installation">Manual Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="#environment-configuration">Environment Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="#verification">Verification</a></li>
<li class="toctree-l2"><a class="reference internal" href="#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l2"><a class="reference internal" href="#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="quickstart.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="api/index.html">API Reference</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">CertPathFinder</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Installation Guide</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/installation.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="installation-guide">
<h1>Installation Guide<a class="headerlink" href="#installation-guide" title="Link to this heading"></a></h1>
<p>This guide will help you install and set up CertPathFinder on your system.</p>
<section id="prerequisites">
<h2>Prerequisites<a class="headerlink" href="#prerequisites" title="Link to this heading"></a></h2>
<p>Before installing CertPathFinder, ensure you have the following prerequisites:</p>
<ul class="simple">
<li><p><strong>Python 3.10+</strong> - Required for running the application</p></li>
<li><p><strong>PostgreSQL 13+</strong> - Primary database (SQLite supported for development)</p></li>
<li><p><strong>Redis 6+</strong> - For caching and background tasks</p></li>
<li><p><strong>Docker &amp; Docker Compose</strong> - For containerized deployment (optional)</p></li>
<li><p><strong>Git</strong> - For cloning the repository</p></li>
</ul>
</section>
<section id="quick-start-with-docker">
<h2>Quick Start with Docker<a class="headerlink" href="#quick-start-with-docker" title="Link to this heading"></a></h2>
<p>The fastest way to get CertPathFinder running is using Docker Compose:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Clone the repository</span>
git<span class="w"> </span>clone<span class="w"> </span>https://github.com/forkrul/replit-CertPathFinder.git
<span class="nb">cd</span><span class="w"> </span>replit-CertPathFinder

<span class="c1"># Start all services</span>
docker-compose<span class="w"> </span>up<span class="w"> </span>-d

<span class="c1"># Access the application</span>
<span class="c1"># FastAPI: http://localhost:8000</span>
<span class="c1"># Streamlit: http://localhost:8501</span>
</pre></div>
</div>
</section>
<section id="manual-installation">
<h2>Manual Installation<a class="headerlink" href="#manual-installation" title="Link to this heading"></a></h2>
<p>For development or custom deployments, you can install manually:</p>
<ol class="arabic simple">
<li><p><strong>Clone the Repository</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>git<span class="w"> </span>clone<span class="w"> </span>https://github.com/forkrul/replit-CertPathFinder.git
<span class="nb">cd</span><span class="w"> </span>replit-CertPathFinder
</pre></div>
</div>
<ol class="arabic simple" start="2">
<li><p><strong>Create Virtual Environment</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>python<span class="w"> </span>-m<span class="w"> </span>venv<span class="w"> </span>venv
<span class="nb">source</span><span class="w"> </span>venv/bin/activate<span class="w">  </span><span class="c1"># On Windows: venv\Scripts\activate</span>
</pre></div>
</div>
<ol class="arabic simple" start="3">
<li><p><strong>Install Dependencies</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>pip<span class="w"> </span>install<span class="w"> </span>-r<span class="w"> </span>requirements.txt
</pre></div>
</div>
<ol class="arabic simple" start="4">
<li><p><strong>Set Up Database</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># For PostgreSQL</span>
createdb<span class="w"> </span>certpathfinder

<span class="c1"># For SQLite (development)</span>
<span class="c1"># Database will be created automatically</span>
</pre></div>
</div>
<ol class="arabic simple" start="5">
<li><p><strong>Configure Environment</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>cp<span class="w"> </span>.env.example<span class="w"> </span>.env
<span class="c1"># Edit .env with your configuration</span>
</pre></div>
</div>
<ol class="arabic simple" start="6">
<li><p><strong>Run Database Migrations</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>alembic<span class="w"> </span>upgrade<span class="w"> </span>head
</pre></div>
</div>
<ol class="arabic simple" start="7">
<li><p><strong>Start the Services</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Start FastAPI backend</span>
python<span class="w"> </span>run_api.py

<span class="c1"># Start Streamlit frontend (in another terminal)</span>
streamlit<span class="w"> </span>run<span class="w"> </span>main.py
</pre></div>
</div>
</section>
<section id="environment-configuration">
<h2>Environment Configuration<a class="headerlink" href="#environment-configuration" title="Link to this heading"></a></h2>
<p>Create a <cite>.env</cite> file with the following configuration:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Database Configuration</span>
<span class="nv">DATABASE_URL</span><span class="o">=</span>postgresql://user:password@localhost:5432/certpathfinder
<span class="c1"># Or for SQLite: DATABASE_URL=sqlite:///./certpathfinder.db</span>

<span class="c1"># Redis Configuration</span>
<span class="nv">REDIS_URL</span><span class="o">=</span>redis://localhost:6379

<span class="c1"># Security</span>
<span class="nv">SECRET_KEY</span><span class="o">=</span>your-secret-key-here
<span class="nv">ALGORITHM</span><span class="o">=</span>HS256
<span class="nv">ACCESS_TOKEN_EXPIRE_MINUTES</span><span class="o">=</span><span class="m">30</span>

<span class="c1"># API Configuration</span>
<span class="nv">API_V1_STR</span><span class="o">=</span>/api/v1
<span class="nv">PROJECT_NAME</span><span class="o">=</span>CertPathFinder

<span class="c1"># External APIs (optional)</span>
<span class="nv">OPENAI_API_KEY</span><span class="o">=</span>your-openai-key
<span class="nv">ANTHROPIC_API_KEY</span><span class="o">=</span>your-anthropic-key

<span class="c1"># Email Configuration (optional)</span>
<span class="nv">SMTP_TLS</span><span class="o">=</span>True
<span class="nv">SMTP_PORT</span><span class="o">=</span><span class="m">587</span>
<span class="nv">SMTP_HOST</span><span class="o">=</span>smtp.gmail.com
<span class="nv">SMTP_USER</span><span class="o">=</span><EMAIL>
<span class="nv">SMTP_PASSWORD</span><span class="o">=</span>your-app-password
</pre></div>
</div>
</section>
<section id="verification">
<h2>Verification<a class="headerlink" href="#verification" title="Link to this heading"></a></h2>
<p>After installation, verify that everything is working:</p>
<ol class="arabic simple">
<li><p><strong>Check API Health</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>http://localhost:8000/health
</pre></div>
</div>
<ol class="arabic" start="2">
<li><p><strong>Access API Documentation</strong></p>
<p>Visit <a class="reference external" href="http://localhost:8000/docs">http://localhost:8000/docs</a> for interactive API documentation</p>
</li>
<li><p><strong>Access Frontend</strong></p>
<p>Visit <a class="reference external" href="http://localhost:8501">http://localhost:8501</a> for the Streamlit interface</p>
</li>
<li><p><strong>Run Tests</strong></p></li>
</ol>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>pytest<span class="w"> </span>tests/
</pre></div>
</div>
</section>
<section id="troubleshooting">
<h2>Troubleshooting<a class="headerlink" href="#troubleshooting" title="Link to this heading"></a></h2>
<p><strong>Common Issues:</strong></p>
<ul class="simple">
<li><p><strong>Database Connection Error</strong>: Ensure PostgreSQL is running and credentials are correct</p></li>
<li><p><strong>Redis Connection Error</strong>: Ensure Redis server is running</p></li>
<li><p><strong>Port Already in Use</strong>: Change ports in configuration or stop conflicting services</p></li>
<li><p><strong>Import Errors</strong>: Ensure all dependencies are installed in the virtual environment</p></li>
</ul>
<p><strong>Getting Help:</strong></p>
<ul class="simple">
<li><p>Check the logs for detailed error messages</p></li>
<li><p>Review the configuration in <cite>.env</cite> file</p></li>
<li><p>Ensure all prerequisites are properly installed</p></li>
<li><p>Consult the development documentation for advanced setup</p></li>
</ul>
</section>
<section id="next-steps">
<h2>Next Steps<a class="headerlink" href="#next-steps" title="Link to this heading"></a></h2>
<p>After successful installation:</p>
<ul class="simple">
<li><p>Read the <a class="reference internal" href="quickstart.html"><span class="doc">Quick Start Guide</span></a> guide for basic usage</p></li>
<li><p>Review the <span class="xref std std-doc">configuration</span> for advanced settings</p></li>
<li><p>Explore the <a class="reference internal" href="api/index.html"><span class="doc">API Reference</span></a> for API documentation</p></li>
<li><p>Check out <span class="xref std std-doc">guides/user_guide</span> for detailed usage instructions</p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="CertPathFinder Documentation" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="quickstart.html" class="btn btn-neutral float-right" title="Quick Start Guide" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, CertPathFinder Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>