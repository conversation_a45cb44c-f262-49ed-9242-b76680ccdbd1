

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>API Reference &mdash; CertPathFinder 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=c14262df" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="prev" title="Quick Start Guide" href="../quickstart.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            CertPathFinder
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../quickstart.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference:</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">API Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#base-url">Base URL</a></li>
<li class="toctree-l2"><a class="reference internal" href="#interactive-documentation">Interactive Documentation</a></li>
<li class="toctree-l2"><a class="reference internal" href="#authentication">Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="#api-modules">API Modules</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#cost-calculator-api">Cost Calculator API</a></li>
<li class="toctree-l3"><a class="reference internal" href="#enhanced-security-taxonomy-api">Enhanced Security Taxonomy API</a></li>
<li class="toctree-l3"><a class="reference internal" href="#security-career-framework-api">Security Career Framework API</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.security_career_framework.get_security_job_types"><code class="docutils literal notranslate"><span class="pre">get_security_job_types()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.security_career_framework.get_security_job_type"><code class="docutils literal notranslate"><span class="pre">get_security_job_type()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.security_career_framework.create_security_job_type"><code class="docutils literal notranslate"><span class="pre">create_security_job_type()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.security_career_framework.update_security_job_type"><code class="docutils literal notranslate"><span class="pre">update_security_job_type()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.security_career_framework.get_security_career_paths"><code class="docutils literal notranslate"><span class="pre">get_security_career_paths()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.security_career_framework.get_security_career_path"><code class="docutils literal notranslate"><span class="pre">get_security_career_path()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.security_career_framework.get_security_skill_matrix"><code class="docutils literal notranslate"><span class="pre">get_security_skill_matrix()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.security_career_framework.get_career_recommendations"><code class="docutils literal notranslate"><span class="pre">get_career_recommendations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.security_career_framework.get_security_career_analytics"><code class="docutils literal notranslate"><span class="pre">get_security_career_analytics()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.security_career_framework.get_security_areas"><code class="docutils literal notranslate"><span class="pre">get_security_areas()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.security_career_framework.get_seniority_levels"><code class="docutils literal notranslate"><span class="pre">get_seniority_levels()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.security_career_framework.get_job_families_by_area"><code class="docutils literal notranslate"><span class="pre">get_job_families_by_area()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#study-timer-api">Study Timer API</a></li>
<li class="toctree-l3"><a class="reference internal" href="#integration-hub-api">Integration Hub API</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.integration_hub.get_current_user"><code class="docutils literal notranslate"><span class="pre">get_current_user()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.integration_hub.get_organization_id"><code class="docutils literal notranslate"><span class="pre">get_organization_id()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.integration_hub.configure_sso_integration"><code class="docutils literal notranslate"><span class="pre">configure_sso_integration()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.integration_hub.authenticate_sso_user"><code class="docutils literal notranslate"><span class="pre">authenticate_sso_user()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.integration_hub.get_sso_metadata"><code class="docutils literal notranslate"><span class="pre">get_sso_metadata()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.integration_hub.configure_ldap_integration"><code class="docutils literal notranslate"><span class="pre">configure_ldap_integration()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.integration_hub.sync_ldap_users"><code class="docutils literal notranslate"><span class="pre">sync_ldap_users()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.integration_hub.configure_lms_integration"><code class="docutils literal notranslate"><span class="pre">configure_lms_integration()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.integration_hub.sync_lms_data"><code class="docutils literal notranslate"><span class="pre">sync_lms_data()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.integration_hub.configure_hr_integration"><code class="docutils literal notranslate"><span class="pre">configure_hr_integration()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.integration_hub.sync_hr_data"><code class="docutils literal notranslate"><span class="pre">sync_hr_data()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.integration_hub.configure_webhook_integration"><code class="docutils literal notranslate"><span class="pre">configure_webhook_integration()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.integration_hub.list_integrations"><code class="docutils literal notranslate"><span class="pre">list_integrations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.integration_hub.get_integration_status"><code class="docutils literal notranslate"><span class="pre">get_integration_status()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.integration_hub.test_integration_connection"><code class="docutils literal notranslate"><span class="pre">test_integration_connection()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.integration_hub.integration_hub_health"><code class="docutils literal notranslate"><span class="pre">integration_hub_health()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#ai-study-assistant-api">AI Study Assistant API</a></li>
<li class="toctree-l3"><a class="reference internal" href="#progress-tracking-api">Progress Tracking API</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.progress_tracking.get_current_user_id"><code class="docutils literal notranslate"><span class="pre">get_current_user_id()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.progress_tracking.start_study_session"><code class="docutils literal notranslate"><span class="pre">start_study_session()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.progress_tracking.end_study_session"><code class="docutils literal notranslate"><span class="pre">end_study_session()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.progress_tracking.get_study_sessions"><code class="docutils literal notranslate"><span class="pre">get_study_sessions()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.progress_tracking.record_practice_test_result"><code class="docutils literal notranslate"><span class="pre">record_practice_test_result()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.progress_tracking.get_practice_test_results"><code class="docutils literal notranslate"><span class="pre">get_practice_test_results()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.progress_tracking.create_learning_goal"><code class="docutils literal notranslate"><span class="pre">create_learning_goal()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.progress_tracking.get_learning_goals"><code class="docutils literal notranslate"><span class="pre">get_learning_goals()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.progress_tracking.update_learning_goal"><code class="docutils literal notranslate"><span class="pre">update_learning_goal()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.progress_tracking.update_goal_progress"><code class="docutils literal notranslate"><span class="pre">update_goal_progress()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.progress_tracking.get_achievements"><code class="docutils literal notranslate"><span class="pre">get_achievements()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.progress_tracking.get_progress_summary"><code class="docutils literal notranslate"><span class="pre">get_progress_summary()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.progress_tracking.get_study_insights"><code class="docutils literal notranslate"><span class="pre">get_study_insights()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.progress_tracking.get_learning_analytics"><code class="docutils literal notranslate"><span class="pre">get_learning_analytics()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.progress_tracking.get_progress_dashboard"><code class="docutils literal notranslate"><span class="pre">get_progress_dashboard()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.progress_tracking.delete_study_session"><code class="docutils literal notranslate"><span class="pre">delete_study_session()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.progress_tracking.delete_learning_goal"><code class="docutils literal notranslate"><span class="pre">delete_learning_goal()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#enterprise-dashboard-api">Enterprise Dashboard API</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.enterprise.get_current_admin_user"><code class="docutils literal notranslate"><span class="pre">get_current_admin_user()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.enterprise.require_super_admin"><code class="docutils literal notranslate"><span class="pre">require_super_admin()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.enterprise.require_org_admin"><code class="docutils literal notranslate"><span class="pre">require_org_admin()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.enterprise.create_organization"><code class="docutils literal notranslate"><span class="pre">create_organization()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.enterprise.list_organizations"><code class="docutils literal notranslate"><span class="pre">list_organizations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.enterprise.get_organization"><code class="docutils literal notranslate"><span class="pre">get_organization()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.enterprise.update_organization"><code class="docutils literal notranslate"><span class="pre">update_organization()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.enterprise.delete_organization"><code class="docutils literal notranslate"><span class="pre">delete_organization()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.enterprise.create_user"><code class="docutils literal notranslate"><span class="pre">create_user()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.enterprise.list_organization_users"><code class="docutils literal notranslate"><span class="pre">list_organization_users()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.enterprise.get_user"><code class="docutils literal notranslate"><span class="pre">get_user()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.enterprise.update_user"><code class="docutils literal notranslate"><span class="pre">update_user()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.enterprise.create_department"><code class="docutils literal notranslate"><span class="pre">create_department()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.enterprise.list_organization_departments"><code class="docutils literal notranslate"><span class="pre">list_organization_departments()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.enterprise.assign_license"><code class="docutils literal notranslate"><span class="pre">assign_license()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.enterprise.get_license_usage"><code class="docutils literal notranslate"><span class="pre">get_license_usage()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.enterprise.get_organization_analytics"><code class="docutils literal notranslate"><span class="pre">get_organization_analytics()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.enterprise.get_organization_dashboard"><code class="docutils literal notranslate"><span class="pre">get_organization_dashboard()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.enterprise.health_check"><code class="docutils literal notranslate"><span class="pre">health_check()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#mobile-enterprise-api">Mobile Enterprise API</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.mobile.get_current_mobile_user"><code class="docutils literal notranslate"><span class="pre">get_current_mobile_user()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.mobile.get_device_id"><code class="docutils literal notranslate"><span class="pre">get_device_id()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.mobile.register_mobile_device"><code class="docutils literal notranslate"><span class="pre">register_mobile_device()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.mobile.authenticate_mobile_user"><code class="docutils literal notranslate"><span class="pre">authenticate_mobile_user()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.mobile.get_offline_data_package"><code class="docutils literal notranslate"><span class="pre">get_offline_data_package()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.mobile.sync_mobile_data"><code class="docutils literal notranslate"><span class="pre">sync_mobile_data()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.mobile.send_push_notification"><code class="docutils literal notranslate"><span class="pre">send_push_notification()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.mobile.schedule_smart_notifications"><code class="docutils literal notranslate"><span class="pre">schedule_smart_notifications()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.mobile.track_mobile_analytics"><code class="docutils literal notranslate"><span class="pre">track_mobile_analytics()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.mobile.get_mobile_dashboard"><code class="docutils literal notranslate"><span class="pre">get_mobile_dashboard()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.mobile.quick_start_study_session"><code class="docutils literal notranslate"><span class="pre">quick_start_study_session()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.mobile.get_mobile_practice_test"><code class="docutils literal notranslate"><span class="pre">get_mobile_practice_test()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.mobile.get_offline_ai_recommendations"><code class="docutils literal notranslate"><span class="pre">get_offline_ai_recommendations()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.mobile.mobile_health_check"><code class="docutils literal notranslate"><span class="pre">mobile_health_check()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.mobile.get_mobile_app_config"><code class="docutils literal notranslate"><span class="pre">get_mobile_app_config()</span></code></a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#career-transition-api">Career Transition API</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.career_transition.get_current_user_id"><code class="docutils literal notranslate"><span class="pre">get_current_user_id()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.career_transition.find_career_paths"><code class="docutils literal notranslate"><span class="pre">find_career_paths()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.career_transition.get_career_roles"><code class="docutils literal notranslate"><span class="pre">get_career_roles()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.career_transition.create_transition_plan"><code class="docutils literal notranslate"><span class="pre">create_transition_plan()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.career_transition.get_user_transition_plans"><code class="docutils literal notranslate"><span class="pre">get_user_transition_plans()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.career_transition.get_transition_plan"><code class="docutils literal notranslate"><span class="pre">get_transition_plan()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.career_transition.update_transition_plan"><code class="docutils literal notranslate"><span class="pre">update_transition_plan()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.career_transition.update_step_progress"><code class="docutils literal notranslate"><span class="pre">update_step_progress()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.career_transition.delete_transition_plan"><code class="docutils literal notranslate"><span class="pre">delete_transition_plan()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.career_transition.get_transition_summary"><code class="docutils literal notranslate"><span class="pre">get_transition_summary()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.career_transition.generate_career_summary_pdf"><code class="docutils literal notranslate"><span class="pre">generate_career_summary_pdf()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.career_transition.generate_transition_plan_pdf"><code class="docutils literal notranslate"><span class="pre">generate_transition_plan_pdf()</span></code></a></li>
<li class="toctree-l4"><a class="reference internal" href="#api.v1.career_transition.generate_path_comparison_pdf"><code class="docutils literal notranslate"><span class="pre">generate_path_comparison_pdf()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#common-response-formats">Common Response Formats</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#success-response">Success Response</a></li>
<li class="toctree-l3"><a class="reference internal" href="#error-response">Error Response</a></li>
<li class="toctree-l3"><a class="reference internal" href="#pagination">Pagination</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l2"><a class="reference internal" href="#error-codes">Error Codes</a></li>
<li class="toctree-l2"><a class="reference internal" href="#sdk-and-client-libraries">SDK and Client Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="#webhooks">Webhooks</a></li>
</ul>
</li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">CertPathFinder</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">API Reference</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/api/index.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="api-reference">
<h1>API Reference<a class="headerlink" href="#api-reference" title="Link to this heading"></a></h1>
<p>CertPathFinder provides a comprehensive REST API for all platform functionality. The API is built with FastAPI and provides automatic OpenAPI documentation.</p>
<section id="base-url">
<h2>Base URL<a class="headerlink" href="#base-url" title="Link to this heading"></a></h2>
<p>All API endpoints are available under the base URL:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>http://localhost:8000/api/v1
</pre></div>
</div>
</section>
<section id="interactive-documentation">
<h2>Interactive Documentation<a class="headerlink" href="#interactive-documentation" title="Link to this heading"></a></h2>
<p>FastAPI provides interactive API documentation at:</p>
<ul class="simple">
<li><p><strong>Swagger UI</strong>: <a class="reference external" href="http://localhost:8000/docs">http://localhost:8000/docs</a></p></li>
<li><p><strong>ReDoc</strong>: <a class="reference external" href="http://localhost:8000/redoc">http://localhost:8000/redoc</a></p></li>
</ul>
</section>
<section id="authentication">
<h2>Authentication<a class="headerlink" href="#authentication" title="Link to this heading"></a></h2>
<p>Most API endpoints require authentication using JWT tokens:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Login to get access token</span>
<span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/api/v1/auth/login&quot;</span><span class="p">,</span> <span class="n">data</span><span class="o">=</span><span class="p">{</span>
    <span class="s2">&quot;username&quot;</span><span class="p">:</span> <span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
    <span class="s2">&quot;password&quot;</span><span class="p">:</span> <span class="s2">&quot;password&quot;</span>
<span class="p">})</span>
<span class="n">token</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()[</span><span class="s2">&quot;access_token&quot;</span><span class="p">]</span>

<span class="c1"># Use token in requests</span>
<span class="n">headers</span> <span class="o">=</span> <span class="p">{</span><span class="s2">&quot;Authorization&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;Bearer </span><span class="si">{</span><span class="n">token</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">}</span>
</pre></div>
</div>
</section>
<section id="api-modules">
<h2>API Modules<a class="headerlink" href="#api-modules" title="Link to this heading"></a></h2>
<section id="cost-calculator-api">
<h3>Cost Calculator API<a class="headerlink" href="#cost-calculator-api" title="Link to this heading"></a></h3>
<p>Calculate certification costs and ROI analysis.</p>
</section>
<section id="enhanced-security-taxonomy-api">
<h3>Enhanced Security Taxonomy API<a class="headerlink" href="#enhanced-security-taxonomy-api" title="Link to this heading"></a></h3>
<p>Comprehensive security taxonomy with skills, certifications, and job titles.</p>
</section>
<section id="security-career-framework-api">
<h3>Security Career Framework API<a class="headerlink" href="#security-career-framework-api" title="Link to this heading"></a></h3>
<p>Career progression and framework based on Paul Jerimy’s security areas.</p>
<p id="module-api.v1.security_career_framework">Security Career Framework API endpoints.</p>
<p>This module provides comprehensive API endpoints for security career paths,
job types, seniority levels, and skill matrices based on Paul Jerimy’s
8 security areas and industry standards.</p>
<dl class="py function">
<dt class="sig sig-object py" id="api.v1.security_career_framework.get_security_job_types">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.security_career_framework.</span></span><span class="sig-name descname"><span class="pre">get_security_job_types</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">security_area</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">SecurityAreaEnum</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">seniority_level</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">SeniorityLevelEnum</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">job_family</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">demand_level</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">DemandLevelEnum</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">remote_friendly</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">min_salary</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><span class="pre">float</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_salary</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><span class="pre">float</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">min_experience</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_experience</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">skip</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(0)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">limit</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(100)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/security_career_framework.html#get_security_job_types"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.security_career_framework.get_security_job_types" title="Link to this definition"></a></dt>
<dd><p>Get security job types with optional filtering.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.security_career_framework.get_security_job_type">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.security_career_framework.</span></span><span class="sig-name descname"><span class="pre">get_security_job_type</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">job_type_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/security_career_framework.html#get_security_job_type"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.security_career_framework.get_security_job_type" title="Link to this definition"></a></dt>
<dd><p>Get a specific security job type by ID.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.security_career_framework.create_security_job_type">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.security_career_framework.</span></span><span class="sig-name descname"><span class="pre">create_security_job_type</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">job_type_data</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">SecurityJobTypeCreate</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/security_career_framework.html#create_security_job_type"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.security_career_framework.create_security_job_type" title="Link to this definition"></a></dt>
<dd><p>Create a new security job type.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.security_career_framework.update_security_job_type">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.security_career_framework.</span></span><span class="sig-name descname"><span class="pre">update_security_job_type</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">job_type_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">job_type_data</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">SecurityJobTypeUpdate</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/security_career_framework.html#update_security_job_type"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.security_career_framework.update_security_job_type" title="Link to this definition"></a></dt>
<dd><p>Update a security job type.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.security_career_framework.get_security_career_paths">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.security_career_framework.</span></span><span class="sig-name descname"><span class="pre">get_security_career_paths</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">security_area</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">SecurityAreaEnum</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">skip</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(0)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">limit</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(100)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/security_career_framework.html#get_security_career_paths"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.security_career_framework.get_security_career_paths" title="Link to this definition"></a></dt>
<dd><p>Get security career paths with optional filtering.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.security_career_framework.get_security_career_path">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.security_career_framework.</span></span><span class="sig-name descname"><span class="pre">get_security_career_path</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/security_career_framework.html#get_security_career_path"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.security_career_framework.get_security_career_path" title="Link to this definition"></a></dt>
<dd><p>Get a specific security career path by ID.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.security_career_framework.get_security_skill_matrix">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.security_career_framework.</span></span><span class="sig-name descname"><span class="pre">get_security_skill_matrix</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">security_area</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">SecurityAreaEnum</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">job_family</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">seniority_level</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">SeniorityLevelEnum</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/security_career_framework.html#get_security_skill_matrix"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.security_career_framework.get_security_skill_matrix" title="Link to this definition"></a></dt>
<dd><p>Get security skill matrix with optional filtering.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.security_career_framework.get_career_recommendations">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.security_career_framework.</span></span><span class="sig-name descname"><span class="pre">get_career_recommendations</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">CareerRecommendationRequest</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/security_career_framework.html#get_career_recommendations"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.security_career_framework.get_career_recommendations" title="Link to this definition"></a></dt>
<dd><p>Get personalized career recommendations based on user profile.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.security_career_framework.get_security_career_analytics">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.security_career_framework.</span></span><span class="sig-name descname"><span class="pre">get_security_career_analytics</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/security_career_framework.html#get_security_career_analytics"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.security_career_framework.get_security_career_analytics" title="Link to this definition"></a></dt>
<dd><p>Get comprehensive analytics for security careers.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.security_career_framework.get_security_areas">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.security_career_framework.</span></span><span class="sig-name descname"><span class="pre">get_security_areas</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/security_career_framework.html#get_security_areas"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.security_career_framework.get_security_areas" title="Link to this definition"></a></dt>
<dd><p>Get list of all security areas.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.security_career_framework.get_seniority_levels">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.security_career_framework.</span></span><span class="sig-name descname"><span class="pre">get_seniority_levels</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/security_career_framework.html#get_seniority_levels"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.security_career_framework.get_seniority_levels" title="Link to this definition"></a></dt>
<dd><p>Get list of all seniority levels.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.security_career_framework.get_job_families_by_area">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.security_career_framework.</span></span><span class="sig-name descname"><span class="pre">get_job_families_by_area</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">security_area</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">SecurityAreaEnum</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/security_career_framework.html#get_job_families_by_area"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.security_career_framework.get_job_families_by_area" title="Link to this definition"></a></dt>
<dd><p>Get job families for a specific security area.</p>
</dd></dl>

</section>
<section id="study-timer-api">
<h3>Study Timer API<a class="headerlink" href="#study-timer-api" title="Link to this heading"></a></h3>
<p>Study session tracking and productivity management.</p>
</section>
<section id="integration-hub-api">
<h3>Integration Hub API<a class="headerlink" href="#integration-hub-api" title="Link to this heading"></a></h3>
<p>Enterprise integrations for SSO, LDAP, LMS, and HR systems.</p>
<p id="module-api.v1.integration_hub">FastAPI endpoints for Integration Hub.</p>
<p>This module provides comprehensive integration endpoints for SSO, LDAP,
LMS, HR systems, and other enterprise integrations with real-time
synchronization and monitoring capabilities.</p>
<dl class="py function">
<dt class="sig sig-object py" id="api.v1.integration_hub.get_current_user">
<span class="sig-prename descclassname"><span class="pre">api.v1.integration_hub.</span></span><span class="sig-name descname"><span class="pre">get_current_user</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">authorization</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Header(None)</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></span><a class="reference internal" href="../_modules/api/v1/integration_hub.html#get_current_user"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.integration_hub.get_current_user" title="Link to this definition"></a></dt>
<dd><p>Get current user from authorization header.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.integration_hub.get_organization_id">
<span class="sig-prename descclassname"><span class="pre">api.v1.integration_hub.</span></span><span class="sig-name descname"><span class="pre">get_organization_id</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x_organization_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Header(None)</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span></span><a class="reference internal" href="../_modules/api/v1/integration_hub.html#get_organization_id"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.integration_hub.get_organization_id" title="Link to this definition"></a></dt>
<dd><p>Get organization ID from request headers.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.integration_hub.configure_sso_integration">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.integration_hub.</span></span><span class="sig-name descname"><span class="pre">configure_sso_integration</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sso_config</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">SSOConfigRequest</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">organization_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_organization_id)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">current_user</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_user)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/integration_hub.html#configure_sso_integration"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.integration_hub.configure_sso_integration" title="Link to this definition"></a></dt>
<dd><p>Configure SSO integration for an organization.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.integration_hub.authenticate_sso_user">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.integration_hub.</span></span><span class="sig-name descname"><span class="pre">authenticate_sso_user</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">integration_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">saml_response</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/integration_hub.html#authenticate_sso_user"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.integration_hub.authenticate_sso_user" title="Link to this definition"></a></dt>
<dd><p>Authenticate user via SSO SAML response.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.integration_hub.get_sso_metadata">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.integration_hub.</span></span><span class="sig-name descname"><span class="pre">get_sso_metadata</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">integration_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/integration_hub.html#get_sso_metadata"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.integration_hub.get_sso_metadata" title="Link to this definition"></a></dt>
<dd><p>Get SAML metadata for SSO integration.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.integration_hub.configure_ldap_integration">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.integration_hub.</span></span><span class="sig-name descname"><span class="pre">configure_ldap_integration</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">ldap_config</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">LDAPConfigRequest</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">organization_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_organization_id)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">current_user</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_user)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/integration_hub.html#configure_ldap_integration"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.integration_hub.configure_ldap_integration" title="Link to this definition"></a></dt>
<dd><p>Configure LDAP/Active Directory integration.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.integration_hub.sync_ldap_users">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.integration_hub.</span></span><span class="sig-name descname"><span class="pre">sync_ldap_users</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">integration_id:</span> <span class="pre">str</span> <span class="pre">=</span> <span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">background_tasks:</span> <span class="pre">~fastapi.background.BackgroundTasks</span> <span class="pre">=</span> <span class="pre">&lt;fastapi.background.BackgroundTasks</span> <span class="pre">object&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">current_user:</span> <span class="pre">str</span> <span class="pre">=</span> <span class="pre">Depends(get_current_user)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db:</span> <span class="pre">~sqlalchemy.orm.session.Session</span> <span class="pre">=</span> <span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/integration_hub.html#sync_ldap_users"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.integration_hub.sync_ldap_users" title="Link to this definition"></a></dt>
<dd><p>Synchronize users from LDAP/Active Directory.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.integration_hub.configure_lms_integration">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.integration_hub.</span></span><span class="sig-name descname"><span class="pre">configure_lms_integration</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">lms_config</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">LMSConfigRequest</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">organization_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_organization_id)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">current_user</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_user)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/integration_hub.html#configure_lms_integration"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.integration_hub.configure_lms_integration" title="Link to this definition"></a></dt>
<dd><p>Configure LMS integration (Canvas, Moodle, Blackboard).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.integration_hub.sync_lms_data">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.integration_hub.</span></span><span class="sig-name descname"><span class="pre">sync_lms_data</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sync_request:</span> <span class="pre">~schemas.integration_hub.SyncRequest</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">integration_id:</span> <span class="pre">str</span> <span class="pre">=</span> <span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">background_tasks:</span> <span class="pre">~fastapi.background.BackgroundTasks</span> <span class="pre">=</span> <span class="pre">&lt;fastapi.background.BackgroundTasks</span> <span class="pre">object&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">current_user:</span> <span class="pre">str</span> <span class="pre">=</span> <span class="pre">Depends(get_current_user)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db:</span> <span class="pre">~sqlalchemy.orm.session.Session</span> <span class="pre">=</span> <span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/integration_hub.html#sync_lms_data"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.integration_hub.sync_lms_data" title="Link to this definition"></a></dt>
<dd><p>Synchronize data with LMS system.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.integration_hub.configure_hr_integration">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.integration_hub.</span></span><span class="sig-name descname"><span class="pre">configure_hr_integration</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">hr_config</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">HRConfigRequest</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">organization_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_organization_id)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">current_user</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_user)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/integration_hub.html#configure_hr_integration"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.integration_hub.configure_hr_integration" title="Link to this definition"></a></dt>
<dd><p>Configure HR system integration (Workday, BambooHR, ADP).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.integration_hub.sync_hr_data">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.integration_hub.</span></span><span class="sig-name descname"><span class="pre">sync_hr_data</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sync_request:</span> <span class="pre">~schemas.integration_hub.SyncRequest</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">integration_id:</span> <span class="pre">str</span> <span class="pre">=</span> <span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">background_tasks:</span> <span class="pre">~fastapi.background.BackgroundTasks</span> <span class="pre">=</span> <span class="pre">&lt;fastapi.background.BackgroundTasks</span> <span class="pre">object&gt;</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">current_user:</span> <span class="pre">str</span> <span class="pre">=</span> <span class="pre">Depends(get_current_user)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db:</span> <span class="pre">~sqlalchemy.orm.session.Session</span> <span class="pre">=</span> <span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/integration_hub.html#sync_hr_data"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.integration_hub.sync_hr_data" title="Link to this definition"></a></dt>
<dd><p>Synchronize data with HR system.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.integration_hub.configure_webhook_integration">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.integration_hub.</span></span><span class="sig-name descname"><span class="pre">configure_webhook_integration</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">webhook_config</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">WebhookConfigRequest</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">organization_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_organization_id)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">current_user</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_user)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/integration_hub.html#configure_webhook_integration"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.integration_hub.configure_webhook_integration" title="Link to this definition"></a></dt>
<dd><p>Configure webhook integration for real-time data updates.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.integration_hub.list_integrations">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.integration_hub.</span></span><span class="sig-name descname"><span class="pre">list_integrations</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">organization_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_organization_id)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">integration_type</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">status</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">current_user</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_user)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/integration_hub.html#list_integrations"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.integration_hub.list_integrations" title="Link to this definition"></a></dt>
<dd><p>List all integrations for an organization.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.integration_hub.get_integration_status">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.integration_hub.</span></span><span class="sig-name descname"><span class="pre">get_integration_status</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">integration_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">current_user</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_user)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/integration_hub.html#get_integration_status"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.integration_hub.get_integration_status" title="Link to this definition"></a></dt>
<dd><p>Get detailed status of a specific integration.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.integration_hub.test_integration_connection">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.integration_hub.</span></span><span class="sig-name descname"><span class="pre">test_integration_connection</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">integration_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">current_user</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_user)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/integration_hub.html#test_integration_connection"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.integration_hub.test_integration_connection" title="Link to this definition"></a></dt>
<dd><p>Test connection for a specific integration.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.integration_hub.integration_hub_health">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.integration_hub.</span></span><span class="sig-name descname"><span class="pre">integration_hub_health</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/integration_hub.html#integration_hub_health"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.integration_hub.integration_hub_health" title="Link to this definition"></a></dt>
<dd><p>Health check endpoint for Integration Hub service.</p>
</dd></dl>

</section>
<section id="ai-study-assistant-api">
<h3>AI Study Assistant API<a class="headerlink" href="#ai-study-assistant-api" title="Link to this heading"></a></h3>
<p>AI-powered study recommendations and assistance.</p>
</section>
<section id="progress-tracking-api">
<h3>Progress Tracking API<a class="headerlink" href="#progress-tracking-api" title="Link to this heading"></a></h3>
<p>Learning progress, goals, and achievement tracking.</p>
<p id="module-api.v1.progress_tracking">FastAPI endpoints for progress tracking and learning analytics.</p>
<p>This module provides comprehensive API endpoints for study session tracking,
practice test results, learning goals, achievements, and analytics.</p>
<dl class="py function">
<dt class="sig sig-object py" id="api.v1.progress_tracking.get_current_user_id">
<span class="sig-prename descclassname"><span class="pre">api.v1.progress_tracking.</span></span><span class="sig-name descname"><span class="pre">get_current_user_id</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></span><a class="reference internal" href="../_modules/api/v1/progress_tracking.html#get_current_user_id"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.progress_tracking.get_current_user_id" title="Link to this definition"></a></dt>
<dd><p>Get current user ID from authentication context.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.progress_tracking.start_study_session">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.progress_tracking.</span></span><span class="sig-name descname"><span class="pre">start_study_session</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">StudySessionStart</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/progress_tracking.html#start_study_session"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.progress_tracking.start_study_session" title="Link to this definition"></a></dt>
<dd><p>Start a new study session.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.progress_tracking.end_study_session">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.progress_tracking.</span></span><span class="sig-name descname"><span class="pre">end_study_session</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">session_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">StudySessionEnd</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/progress_tracking.html#end_study_session"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.progress_tracking.end_study_session" title="Link to this definition"></a></dt>
<dd><p>End a study session and record progress.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.progress_tracking.get_study_sessions">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.progress_tracking.</span></span><span class="sig-name descname"><span class="pre">get_study_sessions</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">certification_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">session_type</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">days_back</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(30)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">page</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(1)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">page_size</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(20)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/progress_tracking.html#get_study_sessions"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.progress_tracking.get_study_sessions" title="Link to this definition"></a></dt>
<dd><p>Get user’s study sessions with filtering options.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.progress_tracking.record_practice_test_result">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.progress_tracking.</span></span><span class="sig-name descname"><span class="pre">record_practice_test_result</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">PracticeTestResultCreate</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/progress_tracking.html#record_practice_test_result"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.progress_tracking.record_practice_test_result" title="Link to this definition"></a></dt>
<dd><p>Record a practice test result.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.progress_tracking.get_practice_test_results">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.progress_tracking.</span></span><span class="sig-name descname"><span class="pre">get_practice_test_results</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">certification_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">test_type</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">days_back</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(90)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">page</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(1)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">page_size</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(20)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/progress_tracking.html#get_practice_test_results"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.progress_tracking.get_practice_test_results" title="Link to this definition"></a></dt>
<dd><p>Get user’s practice test results with filtering options.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.progress_tracking.create_learning_goal">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.progress_tracking.</span></span><span class="sig-name descname"><span class="pre">create_learning_goal</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">LearningGoalCreate</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/progress_tracking.html#create_learning_goal"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.progress_tracking.create_learning_goal" title="Link to this definition"></a></dt>
<dd><p>Create a new learning goal.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.progress_tracking.get_learning_goals">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.progress_tracking.</span></span><span class="sig-name descname"><span class="pre">get_learning_goals</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">status</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">goal_type</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">certification_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">page</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(1)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">page_size</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(20)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/progress_tracking.html#get_learning_goals"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.progress_tracking.get_learning_goals" title="Link to this definition"></a></dt>
<dd><p>Get user’s learning goals with filtering options.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.progress_tracking.update_learning_goal">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.progress_tracking.</span></span><span class="sig-name descname"><span class="pre">update_learning_goal</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">goal_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">LearningGoalUpdate</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/progress_tracking.html#update_learning_goal"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.progress_tracking.update_learning_goal" title="Link to this definition"></a></dt>
<dd><p>Update a learning goal.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.progress_tracking.update_goal_progress">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.progress_tracking.</span></span><span class="sig-name descname"><span class="pre">update_goal_progress</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">goal_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">GoalProgressUpdate</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/progress_tracking.html#update_goal_progress"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.progress_tracking.update_goal_progress" title="Link to this definition"></a></dt>
<dd><p>Update progress on a learning goal.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.progress_tracking.get_achievements">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.progress_tracking.</span></span><span class="sig-name descname"><span class="pre">get_achievements</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">earned_only</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(False)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">category</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">page</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(1)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">page_size</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(50)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/progress_tracking.html#get_achievements"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.progress_tracking.get_achievements" title="Link to this definition"></a></dt>
<dd><p>Get user’s achievements.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.progress_tracking.get_progress_summary">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.progress_tracking.</span></span><span class="sig-name descname"><span class="pre">get_progress_summary</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/progress_tracking.html#get_progress_summary"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.progress_tracking.get_progress_summary" title="Link to this definition"></a></dt>
<dd><p>Get comprehensive progress summary for the user.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.progress_tracking.get_study_insights">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.progress_tracking.</span></span><span class="sig-name descname"><span class="pre">get_study_insights</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">days_back</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(30)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/progress_tracking.html#get_study_insights"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.progress_tracking.get_study_insights" title="Link to this definition"></a></dt>
<dd><p>Get intelligent study insights and recommendations.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.progress_tracking.get_learning_analytics">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.progress_tracking.</span></span><span class="sig-name descname"><span class="pre">get_learning_analytics</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">period_type</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">PeriodType</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(monthly)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/progress_tracking.html#get_learning_analytics"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.progress_tracking.get_learning_analytics" title="Link to this definition"></a></dt>
<dd><p>Get learning analytics for a specific period.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.progress_tracking.get_progress_dashboard">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.progress_tracking.</span></span><span class="sig-name descname"><span class="pre">get_progress_dashboard</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/progress_tracking.html#get_progress_dashboard"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.progress_tracking.get_progress_dashboard" title="Link to this definition"></a></dt>
<dd><p>Get comprehensive progress dashboard with all key information.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.progress_tracking.delete_study_session">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.progress_tracking.</span></span><span class="sig-name descname"><span class="pre">delete_study_session</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">session_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/progress_tracking.html#delete_study_session"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.progress_tracking.delete_study_session" title="Link to this definition"></a></dt>
<dd><p>Delete a study session.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.progress_tracking.delete_learning_goal">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.progress_tracking.</span></span><span class="sig-name descname"><span class="pre">delete_learning_goal</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">goal_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/progress_tracking.html#delete_learning_goal"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.progress_tracking.delete_learning_goal" title="Link to this definition"></a></dt>
<dd><p>Delete a learning goal.</p>
</dd></dl>

</section>
<section id="enterprise-dashboard-api">
<h3>Enterprise Dashboard API<a class="headerlink" href="#enterprise-dashboard-api" title="Link to this heading"></a></h3>
<p>Multi-tenant organization management and analytics.</p>
<p id="module-api.v1.enterprise">FastAPI endpoints for enterprise dashboard and organizational management.</p>
<p>This module provides comprehensive API endpoints for enterprise deployment
including organization management, user administration, analytics, and licensing.</p>
<dl class="py function">
<dt class="sig sig-object py" id="api.v1.enterprise.get_current_admin_user">
<span class="sig-prename descclassname"><span class="pre">api.v1.enterprise.</span></span><span class="sig-name descname"><span class="pre">get_current_admin_user</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></span><a class="reference internal" href="../_modules/api/v1/enterprise.html#get_current_admin_user"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.enterprise.get_current_admin_user" title="Link to this definition"></a></dt>
<dd><p>Get current admin user ID from authentication context.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.enterprise.require_super_admin">
<span class="sig-prename descclassname"><span class="pre">api.v1.enterprise.</span></span><span class="sig-name descname"><span class="pre">require_super_admin</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/enterprise.html#require_super_admin"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.enterprise.require_super_admin" title="Link to this definition"></a></dt>
<dd><p>Require super admin privileges.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.enterprise.require_org_admin">
<span class="sig-prename descclassname"><span class="pre">api.v1.enterprise.</span></span><span class="sig-name descname"><span class="pre">require_org_admin</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">org_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/enterprise.html#require_org_admin"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.enterprise.require_org_admin" title="Link to this definition"></a></dt>
<dd><p>Require organization admin privileges.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.enterprise.create_organization">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.enterprise.</span></span><span class="sig-name descname"><span class="pre">create_organization</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">organization_data</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">OrganizationCreate</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_admin_user)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/enterprise.html#create_organization"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.enterprise.create_organization" title="Link to this definition"></a></dt>
<dd><p>Create a new enterprise organization.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.enterprise.list_organizations">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.enterprise.</span></span><span class="sig-name descname"><span class="pre">list_organizations</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">page</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(1)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">page_size</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(20)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">organization_type</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">subscription_tier</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">is_active</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">search</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_admin_user)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/enterprise.html#list_organizations"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.enterprise.list_organizations" title="Link to this definition"></a></dt>
<dd><p>List all organizations with pagination and filtering.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.enterprise.get_organization">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.enterprise.</span></span><span class="sig-name descname"><span class="pre">get_organization</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">org_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_admin_user)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/enterprise.html#get_organization"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.enterprise.get_organization" title="Link to this definition"></a></dt>
<dd><p>Get organization by ID.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.enterprise.update_organization">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.enterprise.</span></span><span class="sig-name descname"><span class="pre">update_organization</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">org_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">update_data</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">OrganizationUpdate</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_admin_user)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/enterprise.html#update_organization"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.enterprise.update_organization" title="Link to this definition"></a></dt>
<dd><p>Update organization.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.enterprise.delete_organization">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.enterprise.</span></span><span class="sig-name descname"><span class="pre">delete_organization</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">org_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_admin_user)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/enterprise.html#delete_organization"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.enterprise.delete_organization" title="Link to this definition"></a></dt>
<dd><p>Delete organization (soft delete).</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.enterprise.create_user">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.enterprise.</span></span><span class="sig-name descname"><span class="pre">create_user</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">org_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">user_data</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">EnterpriseUserCreate</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_admin_user)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/enterprise.html#create_user"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.enterprise.create_user" title="Link to this definition"></a></dt>
<dd><p>Create a new user in an organization.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.enterprise.list_organization_users">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.enterprise.</span></span><span class="sig-name descname"><span class="pre">list_organization_users</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">org_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">page</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(1)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">page_size</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(50)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">role</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">department_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">is_active</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">search</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_admin_user)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/enterprise.html#list_organization_users"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.enterprise.list_organization_users" title="Link to this definition"></a></dt>
<dd><p>List users in an organization.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.enterprise.get_user">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.enterprise.</span></span><span class="sig-name descname"><span class="pre">get_user</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">user_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_admin_user)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/enterprise.html#get_user"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.enterprise.get_user" title="Link to this definition"></a></dt>
<dd><p>Get user by ID.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.enterprise.update_user">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.enterprise.</span></span><span class="sig-name descname"><span class="pre">update_user</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">user_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">update_data</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">EnterpriseUserUpdate</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_admin_user)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/enterprise.html#update_user"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.enterprise.update_user" title="Link to this definition"></a></dt>
<dd><p>Update user.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.enterprise.create_department">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.enterprise.</span></span><span class="sig-name descname"><span class="pre">create_department</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">org_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">department_data</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">DepartmentCreate</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_admin_user)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/enterprise.html#create_department"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.enterprise.create_department" title="Link to this definition"></a></dt>
<dd><p>Create a new department in an organization.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.enterprise.list_organization_departments">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.enterprise.</span></span><span class="sig-name descname"><span class="pre">list_organization_departments</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">org_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_admin_user)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/enterprise.html#list_organization_departments"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.enterprise.list_organization_departments" title="Link to this definition"></a></dt>
<dd><p>List departments in an organization.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.enterprise.assign_license">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.enterprise.</span></span><span class="sig-name descname"><span class="pre">assign_license</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">user_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">license_data</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">LicenseAssignment</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Ellipsis</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_admin_user)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/enterprise.html#assign_license"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.enterprise.assign_license" title="Link to this definition"></a></dt>
<dd><p>Assign a license to a user.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.enterprise.get_license_usage">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.enterprise.</span></span><span class="sig-name descname"><span class="pre">get_license_usage</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">org_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_admin_user)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/enterprise.html#get_license_usage"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.enterprise.get_license_usage" title="Link to this definition"></a></dt>
<dd><p>Get license usage statistics for an organization.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.enterprise.get_organization_analytics">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.enterprise.</span></span><span class="sig-name descname"><span class="pre">get_organization_analytics</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">org_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">period_type</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(monthly)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">start_date</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">end_date</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_admin_user)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/enterprise.html#get_organization_analytics"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.enterprise.get_organization_analytics" title="Link to this definition"></a></dt>
<dd><p>Get comprehensive analytics for an organization.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.enterprise.get_organization_dashboard">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.enterprise.</span></span><span class="sig-name descname"><span class="pre">get_organization_dashboard</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">org_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">_</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_admin_user)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/enterprise.html#get_organization_dashboard"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.enterprise.get_organization_dashboard" title="Link to this definition"></a></dt>
<dd><p>Get comprehensive dashboard data for an organization.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.enterprise.health_check">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.enterprise.</span></span><span class="sig-name descname"><span class="pre">health_check</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/enterprise.html#health_check"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.enterprise.health_check" title="Link to this definition"></a></dt>
<dd><p>Health check endpoint for enterprise service.</p>
</dd></dl>

</section>
<section id="mobile-enterprise-api">
<h3>Mobile Enterprise API<a class="headerlink" href="#mobile-enterprise-api" title="Link to this heading"></a></h3>
<p>Mobile-optimized endpoints with offline capabilities.</p>
<p id="module-api.v1.mobile">FastAPI endpoints for mobile enterprise applications.</p>
<p>This module provides comprehensive mobile API endpoints with offline capabilities,
synchronization, push notifications, and enterprise mobile device management
for iOS and Android applications.</p>
<dl class="py function">
<dt class="sig sig-object py" id="api.v1.mobile.get_current_mobile_user">
<span class="sig-prename descclassname"><span class="pre">api.v1.mobile.</span></span><span class="sig-name descname"><span class="pre">get_current_mobile_user</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">authorization</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Header(None)</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></span><a class="reference internal" href="../_modules/api/v1/mobile.html#get_current_mobile_user"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.mobile.get_current_mobile_user" title="Link to this definition"></a></dt>
<dd><p>Get current mobile user from session token.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.mobile.get_device_id">
<span class="sig-prename descclassname"><span class="pre">api.v1.mobile.</span></span><span class="sig-name descname"><span class="pre">get_device_id</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">x_device_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Header(None)</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></span><a class="reference internal" href="../_modules/api/v1/mobile.html#get_device_id"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.mobile.get_device_id" title="Link to this definition"></a></dt>
<dd><p>Get device ID from request headers.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.mobile.register_mobile_device">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.mobile.</span></span><span class="sig-name descname"><span class="pre">register_mobile_device</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">registration_request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">DeviceRegistrationRequest</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/mobile.html#register_mobile_device"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.mobile.register_mobile_device" title="Link to this definition"></a></dt>
<dd><p>Register a mobile device for enterprise access.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.mobile.authenticate_mobile_user">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.mobile.</span></span><span class="sig-name descname"><span class="pre">authenticate_mobile_user</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">auth_request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">MobileAuthRequest</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/mobile.html#authenticate_mobile_user"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.mobile.authenticate_mobile_user" title="Link to this definition"></a></dt>
<dd><p>Authenticate user for mobile application access.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.mobile.get_offline_data_package">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.mobile.</span></span><span class="sig-name descname"><span class="pre">get_offline_data_package</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">offline_request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">OfflineDataRequest</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">user_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_mobile_user)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/mobile.html#get_offline_data_package"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.mobile.get_offline_data_package" title="Link to this definition"></a></dt>
<dd><p>Get comprehensive offline data package for mobile app.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.mobile.sync_mobile_data">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.mobile.</span></span><span class="sig-name descname"><span class="pre">sync_mobile_data</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sync_request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">SyncRequest</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">user_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_mobile_user)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">device_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_device_id)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/mobile.html#sync_mobile_data"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.mobile.sync_mobile_data" title="Link to this definition"></a></dt>
<dd><p>Synchronize mobile app data with server.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.mobile.send_push_notification">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.mobile.</span></span><span class="sig-name descname"><span class="pre">send_push_notification</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">notification_request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">PushNotificationRequest</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">user_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_mobile_user)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/mobile.html#send_push_notification"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.mobile.send_push_notification" title="Link to this definition"></a></dt>
<dd><p>Send push notification to user’s mobile devices.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.mobile.schedule_smart_notifications">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.mobile.</span></span><span class="sig-name descname"><span class="pre">schedule_smart_notifications</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">user_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_mobile_user)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/mobile.html#schedule_smart_notifications"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.mobile.schedule_smart_notifications" title="Link to this definition"></a></dt>
<dd><p>Schedule intelligent notifications based on user behavior and AI insights.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.mobile.track_mobile_analytics">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.mobile.</span></span><span class="sig-name descname"><span class="pre">track_mobile_analytics</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">analytics_request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">MobileAnalyticsRequest</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">user_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_mobile_user)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">device_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_device_id)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/mobile.html#track_mobile_analytics"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.mobile.track_mobile_analytics" title="Link to this definition"></a></dt>
<dd><p>Track mobile app usage analytics.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.mobile.get_mobile_dashboard">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.mobile.</span></span><span class="sig-name descname"><span class="pre">get_mobile_dashboard</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">user_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_mobile_user)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/mobile.html#get_mobile_dashboard"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.mobile.get_mobile_dashboard" title="Link to this definition"></a></dt>
<dd><p>Get mobile-optimized dashboard data.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.mobile.quick_start_study_session">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.mobile.</span></span><span class="sig-name descname"><span class="pre">quick_start_study_session</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">certification_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">duration_minutes</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(30)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">user_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_mobile_user)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/mobile.html#quick_start_study_session"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.mobile.quick_start_study_session" title="Link to this definition"></a></dt>
<dd><p>Quick start a mobile-optimized study session.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.mobile.get_mobile_practice_test">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.mobile.</span></span><span class="sig-name descname"><span class="pre">get_mobile_practice_test</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">certification_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">question_count</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(10)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">difficulty</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(mixed)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">user_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_mobile_user)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/mobile.html#get_mobile_practice_test"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.mobile.get_mobile_practice_test" title="Link to this definition"></a></dt>
<dd><p>Get mobile-optimized practice test.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.mobile.get_offline_ai_recommendations">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.mobile.</span></span><span class="sig-name descname"><span class="pre">get_offline_ai_recommendations</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">user_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_mobile_user)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/mobile.html#get_offline_ai_recommendations"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.mobile.get_offline_ai_recommendations" title="Link to this definition"></a></dt>
<dd><p>Get AI recommendations optimized for offline mobile use.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.mobile.mobile_health_check">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.mobile.</span></span><span class="sig-name descname"><span class="pre">mobile_health_check</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/mobile.html#mobile_health_check"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.mobile.mobile_health_check" title="Link to this definition"></a></dt>
<dd><p>Health check endpoint for mobile API service.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.mobile.get_mobile_app_config">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.mobile.</span></span><span class="sig-name descname"><span class="pre">get_mobile_app_config</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">platform</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">app_version</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">user_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_current_mobile_user)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/mobile.html#get_mobile_app_config"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.mobile.get_mobile_app_config" title="Link to this definition"></a></dt>
<dd><p>Get mobile app configuration and feature flags.</p>
</dd></dl>

</section>
<section id="career-transition-api">
<h3>Career Transition API<a class="headerlink" href="#career-transition-api" title="Link to this heading"></a></h3>
<p>Career change planning and guidance.</p>
<p id="module-api.v1.career_transition">FastAPI endpoints for career transition planning and pathfinding.</p>
<p>This module provides comprehensive API endpoints for career transition planning,
budget-aware pathfinding, and transition plan management.</p>
<dl class="py function">
<dt class="sig sig-object py" id="api.v1.career_transition.get_current_user_id">
<span class="sig-prename descclassname"><span class="pre">api.v1.career_transition.</span></span><span class="sig-name descname"><span class="pre">get_current_user_id</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span></span><a class="reference internal" href="../_modules/api/v1/career_transition.html#get_current_user_id"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.career_transition.get_current_user_id" title="Link to this definition"></a></dt>
<dd><p>Get current user ID from authentication context.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.career_transition.find_career_paths">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.career_transition.</span></span><span class="sig-name descname"><span class="pre">find_career_paths</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">CareerPathfindingRequest</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/career_transition.html#find_career_paths"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.career_transition.find_career_paths" title="Link to this definition"></a></dt>
<dd><p>Find optimal career transition paths with budget and timeline constraints.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.career_transition.get_career_roles">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.career_transition.</span></span><span class="sig-name descname"><span class="pre">get_career_roles</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">domain</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">level</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">min_salary</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><span class="pre">float</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_salary</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#float" title="(in Python v3.13)"><span class="pre">float</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">page</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(1)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">page_size</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(20)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/career_transition.html#get_career_roles"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.career_transition.get_career_roles" title="Link to this definition"></a></dt>
<dd><p>Get available career roles with filtering options.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.career_transition.create_transition_plan">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.career_transition.</span></span><span class="sig-name descname"><span class="pre">create_transition_plan</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">CareerTransitionPlanCreate</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/career_transition.html#create_transition_plan"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.career_transition.create_transition_plan" title="Link to this definition"></a></dt>
<dd><p>Create a new career transition plan.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.career_transition.get_user_transition_plans">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.career_transition.</span></span><span class="sig-name descname"><span class="pre">get_user_transition_plans</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">status</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><a class="reference external" href="https://docs.python.org/3/library/constants.html#None" title="(in Python v3.13)"><span class="pre">None</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(None)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">page</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(1)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">page_size</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(20)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/career_transition.html#get_user_transition_plans"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.career_transition.get_user_transition_plans" title="Link to this definition"></a></dt>
<dd><p>Get user’s career transition plans.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.career_transition.get_transition_plan">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.career_transition.</span></span><span class="sig-name descname"><span class="pre">get_transition_plan</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">plan_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/career_transition.html#get_transition_plan"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.career_transition.get_transition_plan" title="Link to this definition"></a></dt>
<dd><p>Get a specific career transition plan.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.career_transition.update_transition_plan">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.career_transition.</span></span><span class="sig-name descname"><span class="pre">update_transition_plan</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">plan_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">CareerTransitionPlanUpdate</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/career_transition.html#update_transition_plan"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.career_transition.update_transition_plan" title="Link to this definition"></a></dt>
<dd><p>Update a career transition plan.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.career_transition.update_step_progress">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.career_transition.</span></span><span class="sig-name descname"><span class="pre">update_step_progress</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">plan_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">step_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">request</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">StepProgressUpdate</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/career_transition.html#update_step_progress"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.career_transition.update_step_progress" title="Link to this definition"></a></dt>
<dd><p>Update progress for a transition plan step.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.career_transition.delete_transition_plan">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.career_transition.</span></span><span class="sig-name descname"><span class="pre">delete_transition_plan</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">plan_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/career_transition.html#delete_transition_plan"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.career_transition.delete_transition_plan" title="Link to this definition"></a></dt>
<dd><p>Delete (deactivate) a career transition plan.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.career_transition.get_transition_summary">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.career_transition.</span></span><span class="sig-name descname"><span class="pre">get_transition_summary</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/career_transition.html#get_transition_summary"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.career_transition.get_transition_summary" title="Link to this definition"></a></dt>
<dd><p>Get career transition summary statistics for the user.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.career_transition.generate_career_summary_pdf">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.career_transition.</span></span><span class="sig-name descname"><span class="pre">generate_career_summary_pdf</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">include_transition_plans</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(True)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_cost_analysis</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(True)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_recommendations</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(True)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/career_transition.html#generate_career_summary_pdf"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.career_transition.generate_career_summary_pdf" title="Link to this definition"></a></dt>
<dd><p>Generate comprehensive career summary PDF report.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.career_transition.generate_transition_plan_pdf">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.career_transition.</span></span><span class="sig-name descname"><span class="pre">generate_transition_plan_pdf</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">plan_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Path(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_detailed_steps</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(True)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">include_cost_projections</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/functions.html#bool" title="(in Python v3.13)"><span class="pre">bool</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(True)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/career_transition.html#generate_transition_plan_pdf"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.career_transition.generate_transition_plan_pdf" title="Link to this definition"></a></dt>
<dd><p>Generate detailed PDF report for a specific transition plan.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="api.v1.career_transition.generate_path_comparison_pdf">
<em class="property"><span class="k"><span class="pre">async</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">api.v1.career_transition.</span></span><span class="sig-name descname"><span class="pre">generate_path_comparison_pdf</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">plan_ids</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/typing.html#typing.List" title="(in Python v3.13)"><span class="pre">List</span></a><span class="p"><span class="pre">[</span></span><a class="reference external" href="https://docs.python.org/3/library/functions.html#int" title="(in Python v3.13)"><span class="pre">int</span></a><span class="p"><span class="pre">]</span></span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(PydanticUndefined)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">comparison_currency</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.python.org/3/library/stdtypes.html#str" title="(in Python v3.13)"><span class="pre">str</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Query(USD)</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">db</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference external" href="https://docs.sqlalchemy.org/en/20/orm/session_api.html#sqlalchemy.orm.Session" title="(in SQLAlchemy v2.0)"><span class="pre">Session</span></a></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">Depends(get_db)</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/api/v1/career_transition.html#generate_path_comparison_pdf"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#api.v1.career_transition.generate_path_comparison_pdf" title="Link to this definition"></a></dt>
<dd><p>Generate PDF report comparing multiple career transition paths.</p>
</dd></dl>

</section>
</section>
<section id="common-response-formats">
<h2>Common Response Formats<a class="headerlink" href="#common-response-formats" title="Link to this heading"></a></h2>
<section id="success-response">
<h3>Success Response<a class="headerlink" href="#success-response" title="Link to this heading"></a></h3>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;success&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;data&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="err">...</span><span class="w"> </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Operation completed successfully&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="error-response">
<h3>Error Response<a class="headerlink" href="#error-response" title="Link to this heading"></a></h3>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;error&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;error&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;code&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;VALIDATION_ERROR&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Invalid input data&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;details&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="err">...</span><span class="w"> </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="pagination">
<h3>Pagination<a class="headerlink" href="#pagination" title="Link to this heading"></a></h3>
<p>List endpoints support pagination:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;items&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w"> </span><span class="err">...</span><span class="w"> </span><span class="p">],</span>
<span class="w">    </span><span class="nt">&quot;total&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">150</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;page&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;size&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">20</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;pages&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="rate-limiting">
<h2>Rate Limiting<a class="headerlink" href="#rate-limiting" title="Link to this heading"></a></h2>
<p>API endpoints are rate limited to ensure fair usage:</p>
<ul class="simple">
<li><p><strong>Authenticated users</strong>: 1000 requests per hour</p></li>
<li><p><strong>Anonymous users</strong>: 100 requests per hour</p></li>
<li><p><strong>Enterprise users</strong>: 10000 requests per hour</p></li>
</ul>
<p>Rate limit headers are included in responses:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
</pre></div>
</div>
</section>
<section id="error-codes">
<h2>Error Codes<a class="headerlink" href="#error-codes" title="Link to this heading"></a></h2>
<p>Common HTTP status codes used by the API:</p>
<ul class="simple">
<li><p><strong>200 OK</strong>: Request successful</p></li>
<li><p><strong>201 Created</strong>: Resource created successfully</p></li>
<li><p><strong>400 Bad Request</strong>: Invalid request data</p></li>
<li><p><strong>401 Unauthorized</strong>: Authentication required</p></li>
<li><p><strong>403 Forbidden</strong>: Insufficient permissions</p></li>
<li><p><strong>404 Not Found</strong>: Resource not found</p></li>
<li><p><strong>422 Unprocessable Entity</strong>: Validation error</p></li>
<li><p><strong>429 Too Many Requests</strong>: Rate limit exceeded</p></li>
<li><p><strong>500 Internal Server Error</strong>: Server error</p></li>
</ul>
</section>
<section id="sdk-and-client-libraries">
<h2>SDK and Client Libraries<a class="headerlink" href="#sdk-and-client-libraries" title="Link to this heading"></a></h2>
<p>Official client libraries are available for:</p>
<ul class="simple">
<li><p><strong>Python</strong>: <code class="docutils literal notranslate"><span class="pre">pip</span> <span class="pre">install</span> <span class="pre">certpathfinder-client</span></code></p></li>
<li><p><strong>JavaScript/TypeScript</strong>: <code class="docutils literal notranslate"><span class="pre">npm</span> <span class="pre">install</span> <span class="pre">certpathfinder-client</span></code></p></li>
<li><p><strong>Go</strong>: <code class="docutils literal notranslate"><span class="pre">go</span> <span class="pre">get</span> <span class="pre">github.com/certpathfinder/go-client</span></code></p></li>
</ul>
<p>Example usage with Python client:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">certpathfinder</span><span class="w"> </span><span class="kn">import</span> <span class="n">CertPathFinderClient</span>

<span class="n">client</span> <span class="o">=</span> <span class="n">CertPathFinderClient</span><span class="p">(</span>
    <span class="n">base_url</span><span class="o">=</span><span class="s2">&quot;http://localhost:8000&quot;</span><span class="p">,</span>
    <span class="n">api_key</span><span class="o">=</span><span class="s2">&quot;your-api-key&quot;</span>
<span class="p">)</span>

<span class="c1"># Get certifications</span>
<span class="n">certifications</span> <span class="o">=</span> <span class="n">client</span><span class="o">.</span><span class="n">certifications</span><span class="o">.</span><span class="n">list</span><span class="p">()</span>

<span class="c1"># Calculate costs</span>
<span class="n">cost_analysis</span> <span class="o">=</span> <span class="n">client</span><span class="o">.</span><span class="n">cost_calculator</span><span class="o">.</span><span class="n">calculate</span><span class="p">(</span>
    <span class="n">certification_id</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
    <span class="n">location</span><span class="o">=</span><span class="s2">&quot;United States&quot;</span>
<span class="p">)</span>
</pre></div>
</div>
</section>
<section id="webhooks">
<h2>Webhooks<a class="headerlink" href="#webhooks" title="Link to this heading"></a></h2>
<p>CertPathFinder supports webhooks for real-time notifications:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Register webhook</span>
<span class="n">webhook_data</span> <span class="o">=</span> <span class="p">{</span>
    <span class="s2">&quot;url&quot;</span><span class="p">:</span> <span class="s2">&quot;https://your-app.com/webhooks/certpathfinder&quot;</span><span class="p">,</span>
    <span class="s2">&quot;events&quot;</span><span class="p">:</span> <span class="p">[</span><span class="s2">&quot;certification.completed&quot;</span><span class="p">,</span> <span class="s2">&quot;goal.achieved&quot;</span><span class="p">],</span>
    <span class="s2">&quot;secret&quot;</span><span class="p">:</span> <span class="s2">&quot;your-webhook-secret&quot;</span>
<span class="p">}</span>

<span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/api/v1/webhooks&quot;</span><span class="p">,</span> <span class="n">json</span><span class="o">=</span><span class="n">webhook_data</span><span class="p">)</span>
</pre></div>
</div>
<p>Supported webhook events:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">user.registered</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">certification.started</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">certification.completed</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">goal.created</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">goal.achieved</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">study_session.completed</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">assessment.completed</span></code></p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="../quickstart.html" class="btn btn-neutral float-left" title="Quick Start Guide" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, CertPathFinder Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>