

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>CertPathFinder Documentation &mdash; CertPathFinder 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="_static/custom.css?v=c14262df" />

  
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="_static/documentation_options.js?v=8d563738"></script>
      <script src="_static/doctools.js?v=9bcbadda"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Installation Guide" href="installation.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="#" class="icon icon-home">
            CertPathFinder
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="installation.html">Installation Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="quickstart.html">Quick Start Guide</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="api/index.html">API Reference</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="#">CertPathFinder</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="#" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">CertPathFinder Documentation</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/index.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="certpathfinder-documentation">
<h1>CertPathFinder Documentation<a class="headerlink" href="#certpathfinder-documentation" title="Link to this heading"></a></h1>
<p>Welcome to CertPathFinder, the most comprehensive cybersecurity certification and career guidance platform!</p>
<p>CertPathFinder is an AI-powered platform that provides personalized career guidance, certification recommendations,
and learning paths for cybersecurity professionals. Built with modern technologies and real-world market data,
it offers enterprise-grade features for individuals and organizations.</p>
<section id="key-features">
<h2>🎯 <strong>Key Features</strong><a class="headerlink" href="#key-features" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><strong>💰 Cost Calculator API</strong> - ROI analysis for certifications with real market data</p></li>
<li><p><strong>🔗 Integration Hub</strong> - Enterprise SSO, LDAP, LMS, and HR system integrations</p></li>
<li><p><strong>🎯 Security Career Framework</strong> - Paul Jerimy’s 8 security areas implementation</p></li>
<li><p><strong>🤖 AI Study Assistant</strong> - Intelligent learning recommendations and progress tracking</p></li>
<li><p><strong>📱 Mobile Enterprise</strong> - Native mobile API platform with offline capabilities</p></li>
<li><p><strong>📊 Progress Tracking</strong> - Comprehensive analytics and achievement systems</p></li>
<li><p><strong>🏢 Enterprise Dashboard</strong> - Multi-tenant organization management</p></li>
<li><p><strong>🧠 Enhanced Taxonomy</strong> - Market-driven career guidance with 500+ skills, 200+ certifications</p></li>
<li><p><strong>⏱️ Study Timer Backend</strong> - Session management and productivity tracking</p></li>
<li><p><strong>🐳 Docker Support</strong> - Complete containerization for easy deployment</p></li>
</ul>
</section>
<section id="documentation-sections">
<h2>📚 <strong>Documentation Sections</strong><a class="headerlink" href="#documentation-sections" title="Link to this heading"></a></h2>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Getting Started:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="installation.html">Installation Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="installation.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#quick-start-with-docker">Quick Start with Docker</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#manual-installation">Manual Installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#environment-configuration">Environment Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#verification">Verification</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="quickstart.html">Quick Start Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="quickstart.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="quickstart.html#first-steps">First Steps</a></li>
<li class="toctree-l2"><a class="reference internal" href="quickstart.html#core-features">Core Features</a></li>
<li class="toctree-l2"><a class="reference internal" href="quickstart.html#web-interface-usage">Web Interface Usage</a></li>
<li class="toctree-l2"><a class="reference internal" href="quickstart.html#key-workflows">Key Workflows</a></li>
<li class="toctree-l2"><a class="reference internal" href="quickstart.html#api-integration">API Integration</a></li>
<li class="toctree-l2"><a class="reference internal" href="quickstart.html#next-steps">Next Steps</a></li>
</ul>
</li>
</ul>
</div>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">API Reference:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="api/index.html">API Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="api/index.html#base-url">Base URL</a></li>
<li class="toctree-l2"><a class="reference internal" href="api/index.html#interactive-documentation">Interactive Documentation</a></li>
<li class="toctree-l2"><a class="reference internal" href="api/index.html#authentication">Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="api/index.html#api-modules">API Modules</a></li>
<li class="toctree-l2"><a class="reference internal" href="api/index.html#common-response-formats">Common Response Formats</a></li>
<li class="toctree-l2"><a class="reference internal" href="api/index.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l2"><a class="reference internal" href="api/index.html#error-codes">Error Codes</a></li>
<li class="toctree-l2"><a class="reference internal" href="api/index.html#sdk-and-client-libraries">SDK and Client Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="api/index.html#webhooks">Webhooks</a></li>
</ul>
</li>
</ul>
</div>
<div class="toctree-wrapper compound">
</div>
<div class="toctree-wrapper compound">
</div>
<div class="toctree-wrapper compound">
</div>
</section>
<section id="quick-links">
<h2>🚀 <strong>Quick Links</strong><a class="headerlink" href="#quick-links" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="installation.html"><span class="doc">Installation Guide</span></a> - Get started with CertPathFinder</p></li>
<li><p><a class="reference internal" href="api/index.html"><span class="doc">API Reference</span></a> - Complete API documentation</p></li>
<li><p><span class="xref std std-doc">guides/user_guide</span> - User guide and tutorials</p></li>
<li><p><span class="xref std std-doc">development/architecture</span> - System architecture overview</p></li>
</ul>
</section>
<section id="platform-statistics">
<h2>📊 <strong>Platform Statistics</strong><a class="headerlink" href="#platform-statistics" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><strong>25+ API modules</strong> with comprehensive functionality</p></li>
<li><p><strong>50+ database models</strong> for complete data management</p></li>
<li><p><strong>100+ API endpoints</strong> covering all use cases</p></li>
<li><p><strong>500+ security skills</strong> with market demand data</p></li>
<li><p><strong>200+ certifications</strong> with ROI analysis</p></li>
<li><p><strong>140+ job titles</strong> from real market research</p></li>
<li><p><strong>300+ security technologies</strong> with adoption rates</p></li>
</ul>
</section>
</section>
<section id="indices-and-tables">
<h1>Indices and tables<a class="headerlink" href="#indices-and-tables" title="Link to this heading"></a></h1>
<ul class="simple">
<li><p><a class="reference internal" href="genindex.html"><span class="std std-ref">Index</span></a></p></li>
<li><p><a class="reference internal" href="py-modindex.html"><span class="std std-ref">Module Index</span></a></p></li>
<li><p><a class="reference internal" href="search.html"><span class="std std-ref">Search Page</span></a></p></li>
</ul>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="installation.html" class="btn btn-neutral float-right" title="Installation Guide" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, CertPathFinder Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>