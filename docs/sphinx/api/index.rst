API Reference
=============

CertPathFinder provides a comprehensive REST API for all platform functionality. The API is built with FastAPI and provides automatic OpenAPI documentation.

Base URL
--------

All API endpoints are available under the base URL:

.. code-block:: text

   http://localhost:8000/api/v1

Interactive Documentation
-------------------------

FastAPI provides interactive API documentation at:

* **Swagger UI**: http://localhost:8000/docs
* **ReDoc**: http://localhost:8000/redoc

Authentication
--------------

Most API endpoints require authentication using JWT tokens:

.. code-block:: python

   # Login to get access token
   response = requests.post("/api/v1/auth/login", data={
       "username": "<EMAIL>",
       "password": "password"
   })
   token = response.json()["access_token"]

   # Use token in requests
   headers = {"Authorization": f"Bearer {token}"}

API Modules
-----------

Cost Calculator API
~~~~~~~~~~~~~~~~~~~

Calculate certification costs and ROI analysis.

.. automodule:: api.v1.cost_calculator
   :members:
   :undoc-members:
   :show-inheritance:

Enhanced Security Taxonomy API
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Comprehensive security taxonomy with skills, certifications, and job titles.

.. automodule:: api.v1.enhanced_security_taxonomy
   :members:
   :undoc-members:
   :show-inheritance:

Security Career Framework API
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Career progression and framework based on Paul Jerimy's security areas.

.. automodule:: api.v1.security_career_framework
   :members:
   :undoc-members:
   :show-inheritance:

Study Timer API
~~~~~~~~~~~~~~~

Study session tracking and productivity management.

.. automodule:: api.v1.study_timer
   :members:
   :undoc-members:
   :show-inheritance:

Integration Hub API
~~~~~~~~~~~~~~~~~~~

Enterprise integrations for SSO, LDAP, LMS, and HR systems.

.. automodule:: api.v1.integration_hub
   :members:
   :undoc-members:
   :show-inheritance:

AI Study Assistant API
~~~~~~~~~~~~~~~~~~~~~~

AI-powered study recommendations and assistance.

.. automodule:: api.v1.ai_assistant
   :members:
   :undoc-members:
   :show-inheritance:

Progress Tracking API
~~~~~~~~~~~~~~~~~~~~~

Learning progress, goals, and achievement tracking.

.. automodule:: api.v1.progress_tracking
   :members:
   :undoc-members:
   :show-inheritance:

Enterprise Dashboard API
~~~~~~~~~~~~~~~~~~~~~~~~

Multi-tenant organization management and analytics.

.. automodule:: api.v1.enterprise
   :members:
   :undoc-members:
   :show-inheritance:

Mobile Enterprise API
~~~~~~~~~~~~~~~~~~~~~

Mobile-optimized endpoints with offline capabilities.

.. automodule:: api.v1.mobile
   :members:
   :undoc-members:
   :show-inheritance:

Career Transition API
~~~~~~~~~~~~~~~~~~~~~

Career change planning and guidance.

.. automodule:: api.v1.career_transition
   :members:
   :undoc-members:
   :show-inheritance:

Common Response Formats
-----------------------

Success Response
~~~~~~~~~~~~~~~~

.. code-block:: json

   {
       "status": "success",
       "data": { ... },
       "message": "Operation completed successfully"
   }

Error Response
~~~~~~~~~~~~~~

.. code-block:: json

   {
       "status": "error",
       "error": {
           "code": "VALIDATION_ERROR",
           "message": "Invalid input data",
           "details": { ... }
       }
   }

Pagination
~~~~~~~~~~

List endpoints support pagination:

.. code-block:: json

   {
       "items": [ ... ],
       "total": 150,
       "page": 1,
       "size": 20,
       "pages": 8
   }

Rate Limiting
-------------

API endpoints are rate limited to ensure fair usage:

* **Authenticated users**: 1000 requests per hour
* **Anonymous users**: 100 requests per hour
* **Enterprise users**: 10000 requests per hour

Rate limit headers are included in responses:

.. code-block:: text

   X-RateLimit-Limit: 1000
   X-RateLimit-Remaining: 999
   X-RateLimit-Reset: 1640995200

Error Codes
-----------

Common HTTP status codes used by the API:

* **200 OK**: Request successful
* **201 Created**: Resource created successfully
* **400 Bad Request**: Invalid request data
* **401 Unauthorized**: Authentication required
* **403 Forbidden**: Insufficient permissions
* **404 Not Found**: Resource not found
* **422 Unprocessable Entity**: Validation error
* **429 Too Many Requests**: Rate limit exceeded
* **500 Internal Server Error**: Server error

SDK and Client Libraries
------------------------

Official client libraries are available for:

* **Python**: ``pip install certpathfinder-client``
* **JavaScript/TypeScript**: ``npm install certpathfinder-client``
* **Go**: ``go get github.com/certpathfinder/go-client``

Example usage with Python client:

.. code-block:: python

   from certpathfinder import CertPathFinderClient

   client = CertPathFinderClient(
       base_url="http://localhost:8000",
       api_key="your-api-key"
   )

   # Get certifications
   certifications = client.certifications.list()

   # Calculate costs
   cost_analysis = client.cost_calculator.calculate(
       certification_id=1,
       location="United States"
   )

Webhooks
--------

CertPathFinder supports webhooks for real-time notifications:

.. code-block:: python

   # Register webhook
   webhook_data = {
       "url": "https://your-app.com/webhooks/certpathfinder",
       "events": ["certification.completed", "goal.achieved"],
       "secret": "your-webhook-secret"
   }

   response = requests.post("/api/v1/webhooks", json=webhook_data)

Supported webhook events:

* ``user.registered``
* ``certification.started``
* ``certification.completed``
* ``goal.created``
* ``goal.achieved``
* ``study_session.completed``
* ``assessment.completed``
