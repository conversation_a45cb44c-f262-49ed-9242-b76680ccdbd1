version: '3.8'

services:
  # Backend API Service
  api:
    build:
      context: .
      dockerfile: Dockerfile.api
    container_name: certpathfinder-api
    environment:
      - DATABASE_URL=**************************************/certpathfinder
      - REDIS_URL=redis://redis:6379
      - ENVIRONMENT=development
      - DEBUG=True
      - SECRET_KEY=dev-secret-key-change-in-production
      - CORS_ORIGINS=https://app.certrats.localhost,https://admin.certrats.localhost
    depends_on:
      - db
      - redis
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.certpathfinder-api.rule=Host(`api.certrats.localhost`)"
      - "traefik.http.routers.certpathfinder-api.tls=true"
      - "traefik.http.services.certpathfinder-api.loadbalancer.server.port=8000"
      - "traefik.docker.network=traefik"
    networks:
      - traefik
      - internal

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: certpathfinder-frontend
    environment:
      - REACT_APP_API_BASE_URL=https://api.certrats.localhost
      - REACT_APP_ENVIRONMENT=development
      - REACT_APP_VERSION=1.0.0
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.certpathfinder-app.rule=Host(`app.certrats.localhost`)"
      - "traefik.http.routers.certpathfinder-app.tls=true"
      - "traefik.http.services.certpathfinder-app.loadbalancer.server.port=80"
      - "traefik.docker.network=traefik"
    networks:
      - traefik

  # Documentation Site (static files for now)
  docs:
    image: nginx:alpine
    container_name: certpathfinder-docs
    volumes:
      - ./docs/_build/html:/usr/share/nginx/html:ro
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.certpathfinder-docs.rule=Host(`docs.certrats.localhost`)"
      - "traefik.http.routers.certpathfinder-docs.tls=true"
      - "traefik.http.services.certpathfinder-docs.loadbalancer.server.port=80"
      - "traefik.docker.network=traefik"
    networks:
      - traefik

  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    container_name: certpathfinder-db
    environment:
      - POSTGRES_DB=certpathfinder
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"  # Exposed for development access
    networks:
      - internal
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: certpathfinder-redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"  # Exposed for development access
    networks:
      - internal
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Background Task Worker (disabled for now)
  # worker:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile.worker
  #   container_name: certpathfinder-worker
  #   environment:
  #     - DATABASE_URL=**************************************/certpathfinder
  #     - REDIS_URL=redis://redis:6379
  #     - ENVIRONMENT=development
  #   depends_on:
  #     - db
  #     - redis
  #   networks:
  #     - internal

  # Monitoring - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: certpathfinder-prometheus
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.certpathfinder-prometheus.rule=Host(`prometheus.certrats.localhost`)"
      - "traefik.http.routers.certpathfinder-prometheus.tls=true"
      - "traefik.http.services.certpathfinder-prometheus.loadbalancer.server.port=9090"
      - "traefik.docker.network=traefik"
    networks:
      - traefik
      - internal

  # Monitoring - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: certpathfinder-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.certpathfinder-grafana.rule=Host(`grafana.certrats.localhost`)"
      - "traefik.http.routers.certpathfinder-grafana.tls=true"
      - "traefik.http.services.certpathfinder-grafana.loadbalancer.server.port=3000"
      - "traefik.docker.network=traefik"
    networks:
      - traefik
      - internal

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  traefik:
    external: true
  internal:
    driver: bridge
