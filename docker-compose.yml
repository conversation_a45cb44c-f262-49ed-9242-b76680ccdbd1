version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: certpathfinder_postgres
    environment:
      POSTGRES_DB: certpathfinder
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and background tasks
  redis:
    image: redis:7-alpine
    container_name: certpathfinder_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # FastAPI Backend
  api:
    build:
      context: .
      dockerfile: Dockerfile.api
    container_name: certpathfinder_api
    environment:
      - DATABASE_URL=********************************************/certpathfinder
      - REDIS_URL=redis://redis:6379
      - ENVIRONMENT=development
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
    command: uvicorn api.app:app --host 0.0.0.0 --port 8000 --reload

  # React Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: ../Dockerfile.frontend
    container_name: certpathfinder_frontend
    environment:
      - REACT_APP_API_BASE_URL=http://localhost:8000
    ports:
      - "3000:3000"
    depends_on:
      - api
    volumes:
      - ./frontend:/app
      - /app/node_modules
    command: npm start

  # Celery Worker for background tasks
  worker:
    build:
      context: .
      dockerfile: Dockerfile.api
    container_name: certpathfinder_worker
    environment:
      - DATABASE_URL=********************************************/certpathfinder
      - REDIS_URL=redis://redis:6379
      - ENVIRONMENT=development
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
    command: celery -A services.celery_app worker --loglevel=info

  # Celery Beat for scheduled tasks
  scheduler:
    build:
      context: .
      dockerfile: Dockerfile.api
    container_name: certpathfinder_scheduler
    environment:
      - DATABASE_URL=********************************************/certpathfinder
      - REDIS_URL=redis://redis:6379
      - ENVIRONMENT=development
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
    command: celery -A services.celery_app beat --loglevel=info

volumes:
  postgres_data:
  redis_data:

networks:
  default:
    name: certpathfinder_network
