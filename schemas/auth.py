"""Authentication schemas for user registration and login"""
from pydantic import BaseModel, EmailStr, Field, field_validator
from typing import Optional, Dict, Any
from datetime import datetime
import re


class UserRegistrationRequest(BaseModel):
    """Schema for user registration request"""
    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., min_length=8, max_length=128, description="User password")
    name: str = Field(..., min_length=1, max_length=200, description="Full name")
    first_name: Optional[str] = Field(None, max_length=100, description="First name")
    last_name: Optional[str] = Field(None, max_length=100, description="Last name")
    
    @field_validator('password')
    @classmethod
    def validate_password(cls, v):
        """Validate password strength"""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one digit')
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError('Password must contain at least one special character')
        return v

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        """Validate name format"""
        if not v.strip():
            raise ValueError('Name cannot be empty')
        return v.strip()
    
    class Config:
        schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "password": "SecurePass123!",
                "name": "John Doe",
                "first_name": "John",
                "last_name": "Doe"
            }
        }


class UserLoginRequest(BaseModel):
    """Schema for user login request"""
    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., description="User password")
    remember_me: bool = Field(False, description="Remember login for extended period")
    device_info: Optional[Dict[str, Any]] = Field(None, description="Device information")
    
    class Config:
        schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "password": "SecurePass123!",
                "remember_me": False,
                "device_info": {
                    "device_type": "desktop",
                    "browser": "Chrome",
                    "os": "Windows"
                }
            }
        }


class TokenRefreshRequest(BaseModel):
    """Schema for token refresh request"""
    refresh_token: str = Field(..., description="Refresh token")
    
    class Config:
        schema_extra = {
            "example": {
                "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
            }
        }


class UserAuthResponse(BaseModel):
    """Schema for authentication response"""
    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="JWT refresh token")
    token_type: str = Field("bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")
    user: 'UserProfileResponse' = Field(..., description="User profile information")
    
    class Config:
        schema_extra = {
            "example": {
                "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                "token_type": "bearer",
                "expires_in": 3600,
                "user": {
                    "user_id": "user_123",
                    "email": "<EMAIL>",
                    "name": "John Doe",
                    "subscription_tier": "free",
                    "is_verified": True
                }
            }
        }


class UserProfileResponse(BaseModel):
    """Schema for user profile response"""
    user_id: str = Field(..., description="User ID")
    email: str = Field(..., description="User email")
    name: str = Field(..., description="Full name")
    first_name: Optional[str] = Field(None, description="First name")
    last_name: Optional[str] = Field(None, description="Last name")
    subscription_tier: str = Field(..., description="Subscription tier")
    is_active: bool = Field(..., description="Account active status")
    is_verified: bool = Field(..., description="Email verification status")
    email_verified_at: Optional[datetime] = Field(None, description="Email verification timestamp")
    last_login: Optional[datetime] = Field(None, description="Last login timestamp")
    created_at: datetime = Field(..., description="Account creation timestamp")
    preferences: Optional[Dict[str, Any]] = Field(None, description="User preferences")
    
    class Config:
        from_attributes = True
        schema_extra = {
            "example": {
                "user_id": "user_123",
                "email": "<EMAIL>",
                "name": "John Doe",
                "first_name": "John",
                "last_name": "Doe",
                "subscription_tier": "free",
                "is_active": True,
                "is_verified": True,
                "email_verified_at": "2025-01-15T10:30:00Z",
                "last_login": "2025-01-15T09:15:00Z",
                "created_at": "2025-01-01T12:00:00Z",
                "preferences": {
                    "theme": "dark",
                    "notifications": True
                }
            }
        }


class PasswordResetRequest(BaseModel):
    """Schema for password reset request"""
    email: EmailStr = Field(..., description="User email address")
    
    class Config:
        schema_extra = {
            "example": {
                "email": "<EMAIL>"
            }
        }


class PasswordResetConfirm(BaseModel):
    """Schema for password reset confirmation"""
    token: str = Field(..., description="Password reset token")
    new_password: str = Field(..., min_length=8, max_length=128, description="New password")
    
    @field_validator('new_password')
    @classmethod
    def validate_password(cls, v):
        """Validate password strength"""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one digit')
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError('Password must contain at least one special character')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "token": "abc123def456",
                "new_password": "NewSecurePass123!"
            }
        }


class EmailVerificationRequest(BaseModel):
    """Schema for email verification"""
    token: str = Field(..., description="Email verification token")
    
    class Config:
        schema_extra = {
            "example": {
                "token": "abc123def456"
            }
        }


class UserPreferencesUpdate(BaseModel):
    """Schema for updating user preferences"""
    preferences: Dict[str, Any] = Field(..., description="User preferences")
    
    class Config:
        schema_extra = {
            "example": {
                "preferences": {
                    "theme": "dark",
                    "notifications": True,
                    "language": "en",
                    "timezone": "UTC"
                }
            }
        }


# Forward reference resolution
UserAuthResponse.model_rebuild()
