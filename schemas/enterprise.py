"""Pydantic schemas for Enterprise Dashboard API endpoints.

This module provides comprehensive request/response schemas for enterprise
organization management, user administration, analytics, and licensing.
"""

from pydantic import BaseModel, Field, validator, EmailStr
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum


class OrganizationType(str, Enum):
    """Organization type enumeration."""
    EDUCATIONAL = "educational"
    CORPORATE = "corporate"
    GOVERNMENT = "government"
    NON_PROFIT = "non_profit"
    TRAINING_PROVIDER = "training_provider"


class SubscriptionTier(str, Enum):
    """Subscription tier enumeration."""
    BASIC = "basic"
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"
    CUSTOM = "custom"


class UserRole(str, Enum):
    """User role enumeration."""
    SUPER_ADMIN = "super_admin"
    ORG_ADMIN = "org_admin"
    DEPARTMENT_ADMIN = "department_admin"
    INSTRUCTOR = "instructor"
    LEARNER = "learner"
    VIEWER = "viewer"


class LicenseStatus(str, Enum):
    """License status enumeration."""
    ACTIVE = "active"
    EXPIRED = "expired"
    SUSPENDED = "suspended"
    PENDING = "pending"


# Organization Schemas

class OrganizationBase(BaseModel):
    """Base organization schema."""
    name: str = Field(..., min_length=1, max_length=200, description="Organization name")
    display_name: Optional[str] = Field(None, max_length=200, description="Display name")
    organization_type: OrganizationType = Field(OrganizationType.EDUCATIONAL, description="Organization type")
    industry: Optional[str] = Field(None, max_length=100, description="Industry")
    size_category: Optional[str] = Field(None, max_length=50, description="Organization size")
    employee_count: Optional[int] = Field(None, ge=1, description="Number of employees")
    
    # Contact information
    primary_contact_name: Optional[str] = Field(None, max_length=200, description="Primary contact name")
    primary_contact_email: Optional[EmailStr] = Field(None, description="Primary contact email")
    primary_contact_phone: Optional[str] = Field(None, max_length=50, description="Primary contact phone")
    
    # Address
    address_line1: Optional[str] = Field(None, max_length=200, description="Address line 1")
    address_line2: Optional[str] = Field(None, max_length=200, description="Address line 2")
    city: Optional[str] = Field(None, max_length=100, description="City")
    state_province: Optional[str] = Field(None, max_length=100, description="State/Province")
    postal_code: Optional[str] = Field(None, max_length=20, description="Postal code")
    country: Optional[str] = Field(None, max_length=100, description="Country")
    
    # Organization details
    domain: Optional[str] = Field(None, max_length=200, description="Email domain")
    website: Optional[str] = Field(None, max_length=500, description="Website URL")
    description: Optional[str] = Field(None, description="Organization description")


class OrganizationCreate(OrganizationBase):
    """Schema for creating an organization."""
    subscription_tier: SubscriptionTier = Field(SubscriptionTier.BASIC, description="Subscription tier")
    license_count: int = Field(10, ge=1, le=10000, description="Number of licenses")
    is_trial: bool = Field(False, description="Is trial organization")
    
    class Config:
        schema_extra = {
            "example": {
                "name": "Acme University",
                "display_name": "Acme University - IT Department",
                "organization_type": "educational",
                "industry": "Education",
                "size_category": "large",
                "employee_count": 5000,
                "primary_contact_name": "John Smith",
                "primary_contact_email": "<EMAIL>",
                "domain": "acme.edu",
                "website": "https://www.acme.edu",
                "description": "Leading university in technology education",
                "subscription_tier": "professional",
                "license_count": 100
            }
        }


class OrganizationUpdate(BaseModel):
    """Schema for updating an organization."""
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    display_name: Optional[str] = Field(None, max_length=200)
    organization_type: Optional[OrganizationType] = None
    industry: Optional[str] = Field(None, max_length=100)
    size_category: Optional[str] = Field(None, max_length=50)
    employee_count: Optional[int] = Field(None, ge=1)
    primary_contact_name: Optional[str] = Field(None, max_length=200)
    primary_contact_email: Optional[EmailStr] = None
    primary_contact_phone: Optional[str] = Field(None, max_length=50)
    domain: Optional[str] = Field(None, max_length=200)
    website: Optional[str] = Field(None, max_length=500)
    description: Optional[str] = None
    subscription_tier: Optional[SubscriptionTier] = None
    license_count: Optional[int] = Field(None, ge=1, le=10000)
    is_active: Optional[bool] = None


class OrganizationResponse(OrganizationBase):
    """Schema for organization response."""
    id: int
    slug: str
    subscription_tier: SubscriptionTier
    license_count: int
    licenses_used: int
    subscription_start_date: Optional[str] = None
    subscription_end_date: Optional[str] = None
    is_active: bool
    is_trial: bool
    trial_end_date: Optional[str] = None
    settings: Dict[str, Any] = Field(default_factory=dict)
    features_enabled: Dict[str, Any] = Field(default_factory=dict)
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    
    class Config:
        from_attributes = True


class OrganizationListResponse(BaseModel):
    """Schema for organization list response."""
    organizations: List[OrganizationResponse]
    total_count: int
    page: int
    page_size: int
    total_pages: int


# User Schemas

class EnterpriseUserBase(BaseModel):
    """Base enterprise user schema."""
    email: EmailStr = Field(..., description="User email address")
    first_name: Optional[str] = Field(None, max_length=100, description="First name")
    last_name: Optional[str] = Field(None, max_length=100, description="Last name")
    display_name: Optional[str] = Field(None, max_length=200, description="Display name")
    role: UserRole = Field(UserRole.LEARNER, description="User role")
    employee_id: Optional[str] = Field(None, max_length=100, description="Employee ID")
    job_title: Optional[str] = Field(None, max_length=200, description="Job title")
    manager_user_id: Optional[str] = Field(None, description="Manager user ID")
    hire_date: Optional[datetime] = Field(None, description="Hire date")


class EnterpriseUserCreate(EnterpriseUserBase):
    """Schema for creating an enterprise user."""
    user_id: str = Field(..., min_length=1, max_length=100, description="Unique user ID")
    department_id: Optional[int] = Field(None, description="Department ID")
    
    class Config:
        schema_extra = {
            "example": {
                "user_id": "john.smith",
                "email": "<EMAIL>",
                "first_name": "John",
                "last_name": "Smith",
                "display_name": "John Smith",
                "role": "learner",
                "employee_id": "EMP001",
                "job_title": "IT Specialist",
                "department_id": 1
            }
        }


class EnterpriseUserUpdate(BaseModel):
    """Schema for updating an enterprise user."""
    email: Optional[EmailStr] = None
    first_name: Optional[str] = Field(None, max_length=100)
    last_name: Optional[str] = Field(None, max_length=100)
    display_name: Optional[str] = Field(None, max_length=200)
    role: Optional[UserRole] = None
    department_id: Optional[int] = None
    employee_id: Optional[str] = Field(None, max_length=100)
    job_title: Optional[str] = Field(None, max_length=200)
    manager_user_id: Optional[str] = None
    hire_date: Optional[datetime] = None
    is_active: Optional[bool] = None


class EnterpriseUserResponse(EnterpriseUserBase):
    """Schema for enterprise user response."""
    id: int
    user_id: str
    organization_id: int
    department_id: Optional[int] = None
    permissions: Dict[str, Any] = Field(default_factory=dict)
    is_active: bool
    last_login: Optional[str] = None
    learning_goals: Dict[str, Any] = Field(default_factory=dict)
    certifications_assigned: List[str] = Field(default_factory=list)
    certifications_completed: List[str] = Field(default_factory=list)
    preferences: Dict[str, Any] = Field(default_factory=dict)
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    
    class Config:
        from_attributes = True


class UserListResponse(BaseModel):
    """Schema for user list response."""
    users: List[EnterpriseUserResponse]
    total_count: int
    page: int
    page_size: int
    total_pages: int


# Department Schemas

class DepartmentBase(BaseModel):
    """Base department schema."""
    name: str = Field(..., min_length=1, max_length=200, description="Department name")
    description: Optional[str] = Field(None, description="Department description")
    code: Optional[str] = Field(None, max_length=50, description="Department code")
    parent_department_id: Optional[int] = Field(None, description="Parent department ID")
    head_user_id: Optional[str] = Field(None, description="Department head user ID")
    budget_allocated: Optional[float] = Field(None, ge=0, description="Allocated budget")


class DepartmentCreate(DepartmentBase):
    """Schema for creating a department."""
    
    class Config:
        schema_extra = {
            "example": {
                "name": "Information Technology",
                "description": "IT department responsible for technology infrastructure",
                "code": "IT",
                "budget_allocated": 50000.0
            }
        }


class DepartmentUpdate(BaseModel):
    """Schema for updating a department."""
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    code: Optional[str] = Field(None, max_length=50)
    parent_department_id: Optional[int] = None
    head_user_id: Optional[str] = None
    budget_allocated: Optional[float] = Field(None, ge=0)
    budget_used: Optional[float] = Field(None, ge=0)
    is_active: Optional[bool] = None


class DepartmentResponse(DepartmentBase):
    """Schema for department response."""
    id: int
    organization_id: int
    budget_used: float
    settings: Dict[str, Any] = Field(default_factory=dict)
    is_active: bool
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    
    class Config:
        from_attributes = True


# License Schemas

class LicenseAssignment(BaseModel):
    """Schema for license assignment."""
    license_type: str = Field(..., description="License type")
    expiration_date: Optional[datetime] = Field(None, description="License expiration date")
    
    class Config:
        schema_extra = {
            "example": {
                "license_type": "professional",
                "expiration_date": "2024-12-31T23:59:59Z"
            }
        }


class LicenseResponse(BaseModel):
    """Schema for license response."""
    id: int
    organization_id: int
    user_id: str
    license_type: str
    status: LicenseStatus
    assigned_date: Optional[str] = None
    activation_date: Optional[str] = None
    expiration_date: Optional[str] = None
    last_used_date: Optional[str] = None
    features_used: Dict[str, Any] = Field(default_factory=dict)
    usage_stats: Dict[str, Any] = Field(default_factory=dict)
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    
    class Config:
        from_attributes = True


# Analytics Schemas

class UserMetrics(BaseModel):
    """User metrics schema."""
    total_users: int
    active_users: int
    new_users: int
    user_retention_rate: float


class LearningMetrics(BaseModel):
    """Learning metrics schema."""
    total_study_hours: float
    average_study_hours_per_user: float
    total_certifications_completed: int
    certification_completion_rate: float


class EngagementMetrics(BaseModel):
    """Engagement metrics schema."""
    total_sessions: int
    average_session_duration: float
    feature_usage: Dict[str, Any]


class PerformanceMetrics(BaseModel):
    """Performance metrics schema."""
    average_test_scores: float
    goal_completion_rate: float
    user_satisfaction_score: float


class OrganizationAnalyticsResponse(BaseModel):
    """Schema for organization analytics response."""
    organization_id: int
    period_type: str
    period_start: str
    period_end: str
    user_metrics: UserMetrics
    learning_metrics: LearningMetrics
    engagement_metrics: EngagementMetrics
    performance_metrics: PerformanceMetrics
    department_breakdown: Dict[str, Any]
    certification_breakdown: Dict[str, Any]
    generated_at: str


# Dashboard Schemas

class LicenseUsageStats(BaseModel):
    """License usage statistics schema."""
    total_licenses: int
    active_licenses: int
    expired_licenses: int
    available_licenses: int
    utilization_rate: float
    license_breakdown: Dict[str, int]


class DepartmentSummary(BaseModel):
    """Department summary schema."""
    total_departments: int
    active_departments: int
    largest_department: str
    largest_department_users: int


class UserSummary(BaseModel):
    """User summary schema."""
    total_users: int
    active_users: int
    recent_logins: int
    role_distribution: Dict[str, int]


class ActivityItem(BaseModel):
    """Activity item schema."""
    type: str
    user_id: str
    description: str
    timestamp: Optional[str] = None


class AlertItem(BaseModel):
    """Alert item schema."""
    type: str
    title: str
    message: str
    priority: str


class DashboardResponse(BaseModel):
    """Schema for dashboard response."""
    organization: OrganizationResponse
    license_usage: LicenseUsageStats
    recent_analytics: OrganizationAnalyticsResponse
    department_summary: DepartmentSummary
    user_summary: UserSummary
    recent_activity: List[ActivityItem]
    alerts: List[AlertItem]


# Health Check Schema

class HealthCheckResponse(BaseModel):
    """Schema for health check response."""
    status: str
    service: str
    version: str
    features: List[str]
    timestamp: str
