"""Pydantic models for certification API"""
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime

class CertificationBase(BaseModel):
    """Base certification fields"""
    name: str
    category: str
    domain: str
    level: str
    focus: str
    difficulty: int
    cost: Optional[float] = None
    description: Optional[str] = None
    prerequisites: Optional[str] = None
    validity_period: Optional[int] = None
    exam_code: Optional[str] = None
    url: Optional[str] = None

class CertificationCreate(CertificationBase):
    """Fields required for creating a certification"""
    pass

class CertificationResponse(CertificationBase):
    """Full certification response including database fields"""
    id: int
    organization_id: Optional[int] = None
    custom_hours: Optional[int] = None
    study_notes: Optional[str] = None
    current_version: int
    last_updated: datetime
    created_at: datetime

    model_config = {"from_attributes": True}

class CertificationList(BaseModel):
    """List of certifications response"""
    certifications: List[CertificationResponse]

class StudyPlanResponse(BaseModel):
    """Study plan response model"""
    status: str
    total_hours_required: int
    weeks_required: float
    suggested_target: Optional[datetime] = None
    message: Optional[str] = None
    weekly_schedule: List[Dict[str, Any]]
    study_tips: List[str]
