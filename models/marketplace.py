"""Marketplace models for vendor management, courses, and partnerships.

This module defines the database models for the Agent 5 Marketplace & Integration Hub,
including vendor management, course marketplace, partnership agreements, and commission tracking.
"""

from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Text, JSON, ForeignKey, Enum as SQLEnum, Decimal, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from enum import Enum
from decimal import Decimal as PyDecimal
from typing import Dict, Any, Optional, List

from models.base import Base, TimestampMixin, SoftDeleteMixin


class VendorStatus(Enum):
    """Vendor status enumeration."""
    PENDING = "pending"
    ACTIVE = "active"
    SUSPENDED = "suspended"
    TERMINATED = "terminated"


class PartnershipTier(Enum):
    """Partnership tier enumeration."""
    TIER_1 = "tier_1"  # Strategic certification bodies
    TIER_2 = "tier_2"  # Training marketplace vendors
    TIER_3 = "tier_3"  # Tool integration partners


class CourseStatus(Enum):
    """Course status enumeration."""
    DRAFT = "draft"
    PENDING_REVIEW = "pending_review"
    APPROVED = "approved"
    PUBLISHED = "published"
    SUSPENDED = "suspended"
    ARCHIVED = "archived"


class CommissionStatus(Enum):
    """Commission payment status enumeration."""
    PENDING = "pending"
    CALCULATED = "calculated"
    APPROVED = "approved"
    PAID = "paid"
    DISPUTED = "disputed"


class MarketplaceVendor(Base, TimestampMixin, SoftDeleteMixin):
    """Marketplace vendor model for training providers and partners."""
    
    __tablename__ = 'marketplace_vendors'
    
    id = Column(Integer, primary_key=True)
    vendor_name = Column(String(200), nullable=False, unique=True)
    vendor_slug = Column(String(100), nullable=False, unique=True)
    
    # Contact information
    contact_email = Column(String(255), nullable=False)
    contact_phone = Column(String(50), nullable=True)
    website_url = Column(String(500), nullable=True)
    
    # Business information
    business_name = Column(String(200), nullable=True)
    tax_id = Column(String(50), nullable=True)
    business_address = Column(JSON, nullable=True)  # Store structured address
    
    # Vendor status and verification
    status = Column(SQLEnum(VendorStatus), nullable=False, default=VendorStatus.PENDING)
    verification_status = Column(String(50), nullable=False, default='unverified')
    verification_date = Column(DateTime, nullable=True)
    
    # Quality metrics
    quality_score = Column(Float, nullable=False, default=0.0)
    average_rating = Column(Float, nullable=True)
    total_reviews = Column(Integer, nullable=False, default=0)
    
    # Financial terms
    commission_rate = Column(Float, nullable=False, default=0.25)  # 25% default
    payment_terms = Column(String(100), nullable=False, default='net_30')
    minimum_payout = Column(Decimal(10, 2), nullable=False, default=PyDecimal('100.00'))
    
    # Content categories and specializations
    content_categories = Column(JSON, nullable=True, default=list)
    specializations = Column(JSON, nullable=True, default=list)
    
    # Performance metrics
    total_courses = Column(Integer, nullable=False, default=0)
    total_enrollments = Column(Integer, nullable=False, default=0)
    total_revenue = Column(Decimal(12, 2), nullable=False, default=PyDecimal('0.00'))
    
    # Configuration
    vendor_config = Column(JSON, nullable=True, default=dict)
    api_credentials = Column(JSON, nullable=True, default=dict)  # Encrypted
    
    # Relationships
    courses = relationship("MarketplaceCourse", back_populates="vendor", cascade="all, delete-orphan")
    partnerships = relationship("PartnershipAgreement", back_populates="vendor", cascade="all, delete-orphan")
    commissions = relationship("CommissionRecord", back_populates="vendor", cascade="all, delete-orphan")
    
    # Indexes
    __table_args__ = (
        Index('idx_vendor_status', 'status'),
        Index('idx_vendor_quality', 'quality_score'),
        Index('idx_vendor_categories', 'content_categories'),
    )
    
    def __repr__(self):
        return f"<MarketplaceVendor(id={self.id}, name='{self.vendor_name}', status='{self.status.value}')>"


class MarketplaceCourse(Base, TimestampMixin, SoftDeleteMixin):
    """Marketplace course model for training content."""
    
    __tablename__ = 'marketplace_courses'
    
    id = Column(Integer, primary_key=True)
    vendor_id = Column(Integer, ForeignKey('marketplace_vendors.id'), nullable=False)
    
    # Course information
    course_title = Column(String(300), nullable=False)
    course_slug = Column(String(150), nullable=False)
    course_description = Column(Text, nullable=True)
    short_description = Column(String(500), nullable=True)
    
    # Course metadata
    course_level = Column(String(50), nullable=False)  # beginner, intermediate, advanced
    duration_hours = Column(Float, nullable=True)
    language = Column(String(10), nullable=False, default='en')
    
    # Pricing
    price = Column(Decimal(10, 2), nullable=False)
    currency = Column(String(3), nullable=False, default='USD')
    discount_price = Column(Decimal(10, 2), nullable=True)
    
    # Course status and approval
    status = Column(SQLEnum(CourseStatus), nullable=False, default=CourseStatus.DRAFT)
    approval_date = Column(DateTime, nullable=True)
    publication_date = Column(DateTime, nullable=True)
    
    # Content and structure
    course_content = Column(JSON, nullable=True, default=dict)  # Modules, lessons, etc.
    learning_objectives = Column(JSON, nullable=True, default=list)
    prerequisites = Column(JSON, nullable=True, default=list)
    
    # Certification mapping
    target_certifications = Column(JSON, nullable=True, default=list)
    certification_domains = Column(JSON, nullable=True, default=list)
    
    # Performance metrics
    enrollment_count = Column(Integer, nullable=False, default=0)
    completion_rate = Column(Float, nullable=True)
    average_rating = Column(Float, nullable=True)
    review_count = Column(Integer, nullable=False, default=0)
    success_rate = Column(Float, nullable=True)  # Certification pass rate
    
    # SEO and marketing
    tags = Column(JSON, nullable=True, default=list)
    featured = Column(Boolean, nullable=False, default=False)
    promotional_text = Column(String(500), nullable=True)
    
    # Media and resources
    thumbnail_url = Column(String(500), nullable=True)
    preview_video_url = Column(String(500), nullable=True)
    course_materials = Column(JSON, nullable=True, default=list)
    
    # Relationships
    vendor = relationship("MarketplaceVendor", back_populates="courses")
    enrollments = relationship("CourseEnrollment", back_populates="course", cascade="all, delete-orphan")
    reviews = relationship("CourseReview", back_populates="course", cascade="all, delete-orphan")
    
    # Indexes
    __table_args__ = (
        Index('idx_course_vendor', 'vendor_id'),
        Index('idx_course_status', 'status'),
        Index('idx_course_level', 'course_level'),
        Index('idx_course_price', 'price'),
        Index('idx_course_rating', 'average_rating'),
        Index('idx_course_featured', 'featured'),
        Index('idx_course_certifications', 'target_certifications'),
    )
    
    def __repr__(self):
        return f"<MarketplaceCourse(id={self.id}, title='{self.course_title}', vendor_id={self.vendor_id})>"


class PartnershipAgreement(Base, TimestampMixin):
    """Partnership agreement model for strategic partnerships."""
    
    __tablename__ = 'partnership_agreements'
    
    id = Column(Integer, primary_key=True)
    vendor_id = Column(Integer, ForeignKey('marketplace_vendors.id'), nullable=False)
    
    # Partnership details
    partner_name = Column(String(200), nullable=False)
    partnership_tier = Column(SQLEnum(PartnershipTier), nullable=False)
    partnership_type = Column(String(100), nullable=False)  # certification_body, training_vendor, tool_integration
    
    # Agreement terms
    revenue_share = Column(Float, nullable=False)  # Percentage as decimal (0.175 = 17.5%)
    minimum_annual_volume = Column(Decimal(12, 2), nullable=True)
    contract_duration_months = Column(Integer, nullable=False)
    
    # Benefits and features
    exclusive_benefits = Column(JSON, nullable=True, default=list)
    co_branding_rights = Column(Boolean, nullable=False, default=False)
    early_access = Column(Boolean, nullable=False, default=False)
    joint_marketing = Column(Boolean, nullable=False, default=False)
    
    # Agreement status
    status = Column(String(50), nullable=False, default='draft')
    signed_date = Column(DateTime, nullable=True)
    effective_date = Column(DateTime, nullable=True)
    expiration_date = Column(DateTime, nullable=True)
    
    # Performance tracking
    current_volume = Column(Decimal(12, 2), nullable=False, default=PyDecimal('0.00'))
    total_commissions_paid = Column(Decimal(12, 2), nullable=False, default=PyDecimal('0.00'))
    
    # Agreement documents and terms
    agreement_terms = Column(JSON, nullable=True, default=dict)
    legal_documents = Column(JSON, nullable=True, default=list)
    
    # Relationships
    vendor = relationship("MarketplaceVendor", back_populates="partnerships")
    
    # Indexes
    __table_args__ = (
        Index('idx_partnership_vendor', 'vendor_id'),
        Index('idx_partnership_tier', 'partnership_tier'),
        Index('idx_partnership_status', 'status'),
        Index('idx_partnership_expiration', 'expiration_date'),
    )
    
    def __repr__(self):
        return f"<PartnershipAgreement(id={self.id}, partner='{self.partner_name}', tier='{self.partnership_tier.value}')>"


class CommissionRecord(Base, TimestampMixin):
    """Commission record model for tracking vendor payments."""
    
    __tablename__ = 'commission_records'
    
    id = Column(Integer, primary_key=True)
    vendor_id = Column(Integer, ForeignKey('marketplace_vendors.id'), nullable=False)
    course_id = Column(Integer, ForeignKey('marketplace_courses.id'), nullable=True)
    
    # Transaction details
    transaction_id = Column(String(100), nullable=False, unique=True)
    transaction_type = Column(String(50), nullable=False)  # course_sale, voucher_sale, subscription
    
    # Financial details
    gross_amount = Column(Decimal(10, 2), nullable=False)
    commission_rate = Column(Float, nullable=False)
    commission_amount = Column(Decimal(10, 2), nullable=False)
    currency = Column(String(3), nullable=False, default='USD')
    
    # Commission calculation details
    base_commission = Column(Float, nullable=False)
    quality_bonus = Column(Float, nullable=False, default=0.0)
    volume_bonus = Column(Float, nullable=False, default=0.0)
    success_bonus = Column(Float, nullable=False, default=0.0)
    
    # Payment tracking
    status = Column(SQLEnum(CommissionStatus), nullable=False, default=CommissionStatus.PENDING)
    calculation_date = Column(DateTime, nullable=True)
    payment_date = Column(DateTime, nullable=True)
    payment_reference = Column(String(100), nullable=True)
    
    # Period tracking
    period_start = Column(DateTime, nullable=False)
    period_end = Column(DateTime, nullable=False)
    
    # Additional metadata
    customer_id = Column(String(100), nullable=True)
    notes = Column(Text, nullable=True)
    
    # Relationships
    vendor = relationship("MarketplaceVendor", back_populates="commissions")
    course = relationship("MarketplaceCourse")
    
    # Indexes
    __table_args__ = (
        Index('idx_commission_vendor', 'vendor_id'),
        Index('idx_commission_status', 'status'),
        Index('idx_commission_period', 'period_start', 'period_end'),
        Index('idx_commission_transaction', 'transaction_id'),
    )
    
    def __repr__(self):
        return f"<CommissionRecord(id={self.id}, vendor_id={self.vendor_id}, amount={self.commission_amount})>"


class CourseEnrollment(Base, TimestampMixin):
    """Course enrollment model for tracking student enrollments."""

    __tablename__ = 'course_enrollments'

    id = Column(Integer, primary_key=True)
    course_id = Column(Integer, ForeignKey('marketplace_courses.id'), nullable=False)
    user_id = Column(String(100), nullable=False)  # Reference to user system

    # Enrollment details
    enrollment_date = Column(DateTime, nullable=False, default=func.now())
    completion_date = Column(DateTime, nullable=True)
    progress_percentage = Column(Float, nullable=False, default=0.0)

    # Payment information
    purchase_price = Column(Decimal(10, 2), nullable=False)
    currency = Column(String(3), nullable=False, default='USD')
    payment_method = Column(String(50), nullable=True)
    transaction_id = Column(String(100), nullable=True)

    # Learning progress
    current_module = Column(String(100), nullable=True)
    time_spent_minutes = Column(Integer, nullable=False, default=0)
    last_accessed = Column(DateTime, nullable=True)

    # Completion and certification
    is_completed = Column(Boolean, nullable=False, default=False)
    completion_certificate_url = Column(String(500), nullable=True)
    final_score = Column(Float, nullable=True)

    # Status
    status = Column(String(50), nullable=False, default='active')  # active, completed, dropped, refunded

    # Relationships
    course = relationship("MarketplaceCourse", back_populates="enrollments")

    # Indexes
    __table_args__ = (
        Index('idx_enrollment_course', 'course_id'),
        Index('idx_enrollment_user', 'user_id'),
        Index('idx_enrollment_status', 'status'),
        Index('idx_enrollment_completion', 'is_completed'),
    )

    def __repr__(self):
        return f"<CourseEnrollment(id={self.id}, course_id={self.course_id}, user_id='{self.user_id}')>"


class CourseReview(Base, TimestampMixin):
    """Course review model for student feedback."""

    __tablename__ = 'course_reviews'

    id = Column(Integer, primary_key=True)
    course_id = Column(Integer, ForeignKey('marketplace_courses.id'), nullable=False)
    user_id = Column(String(100), nullable=False)

    # Review content
    rating = Column(Integer, nullable=False)  # 1-5 stars
    title = Column(String(200), nullable=True)
    review_text = Column(Text, nullable=True)

    # Review metadata
    is_verified_purchase = Column(Boolean, nullable=False, default=False)
    is_featured = Column(Boolean, nullable=False, default=False)
    helpful_votes = Column(Integer, nullable=False, default=0)

    # Moderation
    is_approved = Column(Boolean, nullable=False, default=True)
    moderation_notes = Column(Text, nullable=True)

    # Relationships
    course = relationship("MarketplaceCourse", back_populates="reviews")

    # Indexes
    __table_args__ = (
        Index('idx_review_course', 'course_id'),
        Index('idx_review_user', 'user_id'),
        Index('idx_review_rating', 'rating'),
        Index('idx_review_approved', 'is_approved'),
    )

    def __repr__(self):
        return f"<CourseReview(id={self.id}, course_id={self.course_id}, rating={self.rating})>"


class CurrencyRate(Base, TimestampMixin):
    """Currency exchange rate model for international support."""

    __tablename__ = 'currency_rates'

    id = Column(Integer, primary_key=True)

    # Currency pair
    from_currency = Column(String(3), nullable=False)
    to_currency = Column(String(3), nullable=False)

    # Exchange rate
    rate = Column(Decimal(15, 8), nullable=False)
    rate_date = Column(DateTime, nullable=False, default=func.now())

    # Rate source and metadata
    source = Column(String(100), nullable=False, default='manual')
    is_active = Column(Boolean, nullable=False, default=True)

    # Indexes
    __table_args__ = (
        Index('idx_currency_pair', 'from_currency', 'to_currency'),
        Index('idx_currency_date', 'rate_date'),
        Index('idx_currency_active', 'is_active'),
    )

    def __repr__(self):
        return f"<CurrencyRate(id={self.id}, {self.from_currency}/{self.to_currency}={self.rate})>"


class InternationalMarket(Base, TimestampMixin):
    """International market configuration model."""

    __tablename__ = 'international_markets'

    id = Column(Integer, primary_key=True)

    # Market identification
    country_code = Column(String(2), nullable=False, unique=True)  # ISO 3166-1 alpha-2
    country_name = Column(String(100), nullable=False)
    region = Column(String(100), nullable=True)

    # Market configuration
    default_currency = Column(String(3), nullable=False)
    supported_languages = Column(JSON, nullable=True, default=list)
    tax_rate = Column(Float, nullable=False, default=0.0)

    # Market status
    is_active = Column(Boolean, nullable=False, default=False)
    launch_date = Column(DateTime, nullable=True)

    # Localization settings
    date_format = Column(String(50), nullable=False, default='YYYY-MM-DD')
    number_format = Column(String(50), nullable=False, default='1,234.56')
    timezone = Column(String(50), nullable=True)

    # Legal and compliance
    legal_entity = Column(String(200), nullable=True)
    tax_id = Column(String(100), nullable=True)
    compliance_requirements = Column(JSON, nullable=True, default=dict)

    # Market metrics
    total_users = Column(Integer, nullable=False, default=0)
    total_revenue = Column(Decimal(12, 2), nullable=False, default=PyDecimal('0.00'))

    # Indexes
    __table_args__ = (
        Index('idx_market_country', 'country_code'),
        Index('idx_market_active', 'is_active'),
        Index('idx_market_region', 'region'),
    )

    def __repr__(self):
        return f"<InternationalMarket(id={self.id}, country='{self.country_code}', active={self.is_active})>"
