"""SQLAlchemy models for certifications and organizations"""
from sqlalchemy import Column, Integer, String, Float, Text, Table, ForeignKey, DateTime, Boolean, CheckConstraint
from sqlalchemy.orm import relationship
from datetime import datetime
from database import Base
from .mixins import SoftDeleteMixin

class Organization(Base, SoftDeleteMixin):
    """Organization that issues certifications"""
    __tablename__ = 'organizations'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), unique=True, nullable=False)
    country = Column(String(100), nullable=False)
    url = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    last_updated = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # One-to-many relationship with certifications
    certifications = relationship("Certification", back_populates="organization")

    def to_dict(self):
        """Convert organization to dictionary representation"""
        return {
            'id': self.id,
            'name': self.name,
            'country': self.country,
            'url': self.url,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_updated': self.last_updated.isoformat() if self.last_updated else None,
            'is_deleted': self.is_deleted,
            'deleted_at': self.deleted_at.isoformat() if self.deleted_at else None
        }

class Certification(Base, SoftDeleteMixin):
    """Certification model representing security certifications"""
    __tablename__ = 'certifications'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), unique=True, nullable=False)
    category = Column(String(100), nullable=False)
    domain = Column(String(50), nullable=False)
    level = Column(String(50), nullable=False)
    focus = Column(String(50), nullable=True)  # Made nullable since not all certs have focus
    difficulty = Column(Integer, nullable=False)
    cost = Column(Float, nullable=True)
    currency = Column(String(3), default='USD', nullable=False)
    description = Column(Text, nullable=True)
    prerequisites = Column(Text, nullable=True)
    validity_period = Column(Integer, nullable=True)
    exam_code = Column(String(50), nullable=True)
    organization_id = Column(Integer, ForeignKey('organizations.id', ondelete='SET NULL'), nullable=True)
    url = Column(String(255), nullable=True)
    custom_hours = Column(Integer, nullable=True)
    study_notes = Column(Text, nullable=True)
    course_content = Column(Text, nullable=True)
    current_version = Column(Integer, default=1, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    last_updated = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Define relationships
    organization = relationship("Organization", back_populates="certifications")
    learning_path_items = relationship("LearningPathItem", back_populates="certification")
    cost_history = relationship("CostHistory", back_populates="certification")

    # Database-level constraints for enterprise validation
    __table_args__ = (
        CheckConstraint('difficulty >= 1 AND difficulty <= 5', name='check_difficulty_range'),
        CheckConstraint('cost >= 0', name='check_cost_positive'),
        CheckConstraint('validity_period > 0', name='check_validity_period_positive'),
        CheckConstraint('custom_hours > 0', name='check_custom_hours_positive'),
        CheckConstraint('current_version >= 1', name='check_version_positive'),
        CheckConstraint('LENGTH(name) > 0', name='check_name_not_empty'),
        CheckConstraint('LENGTH(category) > 0', name='check_category_not_empty'),
        CheckConstraint('LENGTH(domain) > 0', name='check_domain_not_empty'),
        CheckConstraint('LENGTH(level) > 0', name='check_level_not_empty'),
    )

    def validate_relationships(self, db_session):
        """Enterprise-grade relationship validation"""
        results = {}

        # Validate organization relationship
        if self.organization_id:
            org = db_session.query(Organization).filter_by(
                id=self.organization_id,
                is_deleted=False
            ).first()
            results['organization'] = org is not None
        else:
            results['organization'] = True  # Nullable field

        # Validate domain relationship
        if self.domain:
            from .security_domain import SecurityDomain
            domain = db_session.query(SecurityDomain).filter_by(
                name=self.domain,
                is_deleted=False
            ).first()
            results['domain'] = domain is not None
        else:
            results['domain'] = False  # Domain is required

        return results

    def validate_version_integrity(self, db_session):
        """Validate version sequence integrity"""
        if not self.id:
            return True  # New record, no versions yet

        versions = db_session.query(CertificationVersion).filter_by(
            certification_id=self.id
        ).order_by(CertificationVersion.version).all()

        if not versions:
            return True  # No versions yet

        # Check for gaps in version sequence
        expected_version = 1
        for version in versions:
            if version.version != expected_version:
                return False
            expected_version += 1

        return True

    def validate_soft_delete_integrity(self, db_session):
        """Validate soft delete consistency across relationships"""
        if self.is_deleted:
            return True  # Already deleted, no integrity issues

        # Check if parent organization is soft deleted
        if self.organization_id:
            org = db_session.query(Organization).filter_by(
                id=self.organization_id
            ).first()
            if org and org.is_deleted:
                return False  # Parent is deleted but child is not

        return True

    def to_dict(self):
        """Convert certification to dictionary representation"""
        return {
            'id': self.id,
            'name': self.name,
            'category': self.category,
            'domain': self.domain,
            'level': self.level,
            'focus': self.focus,
            'difficulty': self.difficulty,
            'cost': self.cost,
            'currency': self.currency,
            'description': self.description,
            'prerequisites': self.prerequisites,
            'validity_period': self.validity_period,
            'exam_code': self.exam_code,
            'organization_id': self.organization_id,
            'url': self.url,
            'custom_hours': self.custom_hours,
            'study_notes': self.study_notes,
            'course_content': self.course_content,
            'current_version': self.current_version,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_updated': self.last_updated.isoformat() if self.last_updated else None,
            'is_deleted': self.is_deleted,
            'deleted_at': self.deleted_at.isoformat() if self.deleted_at else None
        }

# Association table for certification prerequisites
certification_prerequisites = Table(
    'certification_prerequisites',
    Base.metadata,
    Column('certification_id', Integer, ForeignKey('certifications.id'), primary_key=True),
    Column('prerequisite_id', Integer, ForeignKey('certifications.id'), primary_key=True)
)

class CertificationVersion(Base, SoftDeleteMixin):
    """Historical version of a certification"""
    __tablename__ = 'certification_versions'

    id = Column(Integer, primary_key=True)
    certification_id = Column(Integer, ForeignKey('certifications.id'), nullable=False)
    version = Column(Integer, nullable=False)
    name = Column(String(100), nullable=False)
    category = Column(String(100), nullable=False)
    domain = Column(String(50), nullable=False)
    level = Column(String(50), nullable=False)
    focus = Column(String(50), nullable=False)
    difficulty = Column(Integer, nullable=False)
    cost = Column(Float, nullable=True)
    description = Column(Text, nullable=True)
    prerequisites = Column(Text, nullable=True)
    validity_period = Column(Integer, nullable=True)
    exam_code = Column(String(50), nullable=True)
    organization_id = Column(Integer, ForeignKey('organizations.id'), nullable=True)
    url = Column(String(255), nullable=True)
    custom_hours = Column(Integer, nullable=True)
    study_notes = Column(Text, nullable=True)
    course_content = Column(Text, nullable=True)
    changed_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    change_reason = Column(Text, nullable=True)
    change_source = Column(String(100), nullable=True)
    reference_url = Column(String(255), nullable=True)

    def to_dict(self):
        """Convert version to dictionary representation"""
        base_dict = {
            'version': self.version,
            'name': self.name,
            'category': self.category,
            'domain': self.domain,
            'level': self.level,
            'focus': self.focus,
            'difficulty': self.difficulty,
            'cost': self.cost,
            'description': self.description,
            'prerequisites': self.prerequisites,
            'validity_period': self.validity_period,
            'exam_code': self.exam_code,
            'organization_id': self.organization_id,
            'url': self.url,
            'custom_hours': self.custom_hours,
            'study_notes': self.study_notes,
            'course_content': self.course_content,
            'changed_at': self.changed_at.isoformat() if self.changed_at else None,
            'change_reason': self.change_reason,
            'change_source': self.change_source,
            'reference_url': self.reference_url
        }
        base_dict.update({
            'is_deleted': self.is_deleted,
            'deleted_at': self.deleted_at.isoformat() if self.deleted_at else None
        })
        return base_dict