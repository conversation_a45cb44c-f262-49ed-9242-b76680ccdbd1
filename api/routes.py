"""API routes for the certification path creator functionality"""
import logging
import traceback
from fastapi import APIRouter, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from api.v1.job_search import router as job_search_router
from api.v1.domains import router as domains_router
from api.v1.certifications import router as certifications_router
from api.v1.user_profile import router as user_profile_router  # Add new import
from api.v1.cost_calculator import router as cost_calculator_router
from api.v1.career_transition import router as career_transition_router
from api.v1.progress_tracking import router as progress_tracking_router
from api.v1.ai_study_assistant import router as ai_assistant_router
from api.v1.cache import router as cache_router
from api.v1.performance import router as performance_router
from api.v1.enterprise import router as enterprise_router
from api.v1.enterprise_ai import router as enterprise_ai_router
from api.v1.mobile import router as mobile_router
from api.v1.integration_hub import router as integration_hub_router
from api.v1.enhanced_security_taxonomy import router as enhanced_taxonomy_router
from api.v1.security_career_framework import router as security_career_framework_router
# from api.v1.study_timer import router as study_timer_router  # Temporarily disabled
from typing import Dict, Any, Optional, List
from datetime import datetime
# from utils.claude_assistant import generate_career_path, analyze_certification_alignment, init_anthropic_client  # Temporarily disabled
from database import get_db
from models.certification import Certification, Organization
from sqlalchemy.exc import SQLAlchemyError
from pydantic import BaseModel, Field
from sqlalchemy import text, select
from utils.job_management import get_filtered_jobs

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Create main router
router = APIRouter()

# Include routers
router.include_router(job_search_router, prefix="/jobs", tags=["Jobs"])
router.include_router(domains_router, prefix="/domains")
router.include_router(certifications_router, prefix="/certifications")
router.include_router(user_profile_router, prefix="/user", tags=["User Profile"])  # Add new router
router.include_router(cost_calculator_router, tags=["Cost Calculator"])
router.include_router(career_transition_router, tags=["Career Transition"])
router.include_router(progress_tracking_router, tags=["Progress Tracking"])
router.include_router(ai_assistant_router, tags=["AI Study Assistant"])
router.include_router(enterprise_router, tags=["Enterprise Dashboard"])
router.include_router(enterprise_ai_router, tags=["Enterprise AI"])
router.include_router(cache_router, tags=["Cache Management"])
router.include_router(performance_router, tags=["Performance Monitoring"])
router.include_router(mobile_router, tags=["Mobile Enterprise"])
router.include_router(integration_hub_router, tags=["Integration Hub"])
router.include_router(security_career_framework_router, tags=["Security Career Framework"])
router.include_router(enhanced_taxonomy_router, tags=["Enhanced Security Taxonomy"])
# router.include_router(study_timer_router, tags=["Study Timer"])  # Temporarily disabled

# Log registered routes for debugging
logger.debug("Registered routes:")
for route in router.routes:
    logger.debug(f"Route: {route.path}, methods: {route.methods}")

class CareerPathRequest(BaseModel):
    """Request model for career path generation"""
    completed_certifications: Optional[List[str]] = Field(default=[], description="List of completed certification names")
    interests: List[str] = Field(..., min_items=1, description="List of security domains of interest")
    years_experience: int = Field(..., ge=0, le=50, description="Years of experience in security")
    current_role: str = Field(..., min_length=1, description="Current job role")
    target_role: str = Field(..., min_length=1, description="Target career role")
    learning_style: str = Field(
        ...,
        description="Preferred learning style (Visual, Reading, Hands-on, Mixed)"
    )
    study_hours: int = Field(
        ..., 
        ge=1, 
        le=168, 
        description="Available study hours per week"
    )

    class Config:
        schema_extra = {
            "example": {
                "completed_certifications": ["A+", "Network+"],
                "interests": ["Network Security", "Cloud Security"],
                "years_experience": 5,
                "current_role": "Security Analyst",
                "target_role": "Security Engineer",
                "learning_style": "Mixed",
                "study_hours": 10
            }
        }

# Career path endpoint temporarily disabled due to missing dependencies
# @router.post("/career-path", tags=["Career Path"], response_model=Dict[str, Any])
# async def generate_career_path_endpoint(request: CareerPathRequest):
#     """Generate a personalized certification career path based on user profile."""
#     pass

@router.get("/health", tags=["Health"])
async def health_check():
    """Enhanced health check endpoint that includes relationship validation status"""
    try:
        db = next(get_db())

        # Check database connection
        db.execute(text("SELECT 1"))

        # Check relationship integrity
        relationship_status = {"valid": True, "issues": [], "details": []}
        try:
            # Check certifications
            certifications = db.query(Certification).all()
            for cert in certifications:
                validation_results = cert.validate_all_relationships(db)
                relationship_status["details"].append({
                    "type": "certification",
                    "name": cert.name,
                    "valid": all(validation_results.values()),
                    "validation_results": validation_results
                })
                if not all(validation_results.values()):
                    relationship_status["valid"] = False
                    relationship_status["issues"].append({
                        "entity": f"Certification: {cert.name}",
                        "validation_results": validation_results
                    })

            # Check organizations
            organizations = db.query(Organization).all()
            for org in organizations:
                validation_results = org.validate_relationships(db)
                relationship_status["details"].append({
                    "type": "organization",
                    "name": org.name,
                    "valid": all(validation_results.values()),
                    "validation_results": validation_results
                })
                if not all(validation_results.values()):
                    relationship_status["valid"] = False
                    relationship_status["issues"].append({
                        "entity": f"Organization: {org.name}",
                        "validation_results": validation_results
                    })

        except Exception as e:
            logger.error(f"Error validating relationships: {str(e)}")
            logger.error(traceback.format_exc())
            relationship_status["valid"] = False
            relationship_status["error"] = str(e)

        # Check Anthropic client (temporarily disabled)
        anthropic_ready = False  # init_anthropic_client() is not None

        return {
            "status": "healthy",
            "database": "connected",
            "relationships": relationship_status,
            "anthropic_client": "ready" if anthropic_ready else "not_ready",
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=str(e)
        )

@router.get("/jobs/search")
async def search_jobs(
    term: str = "",
    domain: str = "All",
    page: int = 0,
    per_page: int = 10
) -> Dict[str, Any]:
    """
    Search for jobs using parameterized queries
    All database interactions use prepared statements
    """
    try:
        db = next(get_db())
        jobs, total = get_filtered_jobs(
            db=db,
            search_term=term,
            domain=domain,
            page=page,
            per_page=per_page
        )

        # Validate job relationships
        valid_jobs = []
        for job in jobs:
            if hasattr(job, 'validate_relationships'):
                validation_results = job.validate_relationships(db)
                if all(validation_results.values()):
                    valid_jobs.append(job)
            else:
                valid_jobs.append(job)

        return {
            "jobs": [job.to_dict() for job in valid_jobs],
            "total": len(valid_jobs),
            "page": page,
            "per_page": per_page
        }

    except Exception as e:
        logger.error(f"Error searching jobs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error searching jobs"
        )