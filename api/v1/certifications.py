"""API endpoints for certification management"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from database import get_db
from models.certification import Certification
from schemas.certification import (
    CertificationCreate,
    CertificationResponse,
    CertificationList,
    StudyPlanResponse
)
from services.certification import CertificationService
from utils.growth_analyzer import StudyPlanner

# Create router without prefix (prefix is handled by main router)
router = APIRouter(tags=["certifications"])

@router.get("/", response_model=CertificationList)
async def list_certifications(
    domain: Optional[str] = None,
    level: Optional[str] = None,
    focus: Optional[str] = None,
    db: Session = Depends(get_db)
) -> CertificationList:
    """Get all certifications with optional filters"""
    service = CertificationService(db)
    certifications = service.get_certifications(domain=domain, level=level, focus=focus)
    return CertificationList(certifications=certifications)

@router.get("/{cert_id}/study-plan", response_model=StudyPlanResponse)
async def get_study_plan(
    cert_id: int,
    hours_per_week: float = Query(..., gt=0, le=168),
    db: Session = Depends(get_db)
) -> StudyPlanResponse:
    """Generate a study plan for a specific certification"""
    service = CertificationService(db)
    certification = service.get_certification_by_id(cert_id)
    if not certification:
        raise HTTPException(status_code=404, detail="Certification not found")

    planner = StudyPlanner(db)
    study_plan = planner.generate_study_schedule(
        certification_id=cert_id,
        hours_per_week=int(hours_per_week)
    )

    return StudyPlanResponse(**study_plan)