"""FastAPI endpoints for enterprise dashboard and organizational management.

This module provides comprehensive API endpoints for enterprise deployment
including organization management, user administration, analytics, and licensing.
"""

import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session
from datetime import datetime, timed<PERSON>ta

from database import get_db
from services.enterprise_service import EnterpriseService
from schemas.enterprise import (
    OrganizationResponse, OrganizationCreate, OrganizationUpdate,
    EnterpriseUserResponse, EnterpriseUserCreate, EnterpriseUserUpdate,
    DepartmentResponse, DepartmentCreate, DepartmentUpdate,
    LicenseResponse, LicenseAssignment,
    OrganizationAnalyticsResponse, DashboardResponse,
    OrganizationListResponse, UserListResponse
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/enterprise", tags=["Enterprise Dashboard"])


def get_current_admin_user() -> str:
    """Get current admin user ID from authentication context."""
    # TODO: Implement proper admin authentication
    return "admin_user_1"


def require_super_admin():
    """Require super admin privileges."""
    # TODO: Implement proper role-based access control
    pass


def require_org_admin(org_id: int):
    """Require organization admin privileges."""
    # TODO: Implement proper organization-level access control
    pass


# Organization Management Endpoints

@router.post("/organizations", response_model=OrganizationResponse, status_code=status.HTTP_201_CREATED)
async def create_organization(
    organization_data: OrganizationCreate,
    db: Session = Depends(get_db),
    _: str = Depends(get_current_admin_user)
):
    """Create a new enterprise organization."""
    try:
        require_super_admin()
        
        logger.info(f"Creating new organization: {organization_data.name}")
        
        enterprise_service = EnterpriseService(db)
        organization = enterprise_service.create_organization(organization_data.dict())
        
        return OrganizationResponse(**organization.to_dict())
        
    except Exception as e:
        logger.error(f"Error creating organization: {e}")
        raise HTTPException(status_code=500, detail="Error creating organization")


@router.get("/organizations", response_model=OrganizationListResponse)
async def list_organizations(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    organization_type: Optional[str] = Query(None, description="Filter by organization type"),
    subscription_tier: Optional[str] = Query(None, description="Filter by subscription tier"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    search: Optional[str] = Query(None, description="Search organizations"),
    db: Session = Depends(get_db),
    _: str = Depends(get_current_admin_user)
):
    """List all organizations with pagination and filtering."""
    try:
        require_super_admin()
        
        logger.info(f"Listing organizations - page: {page}, size: {page_size}")
        
        enterprise_service = EnterpriseService(db)
        
        filters = {}
        if organization_type:
            filters['organization_type'] = organization_type
        if subscription_tier:
            filters['subscription_tier'] = subscription_tier
        if is_active is not None:
            filters['is_active'] = is_active
        if search:
            filters['search'] = search
        
        organizations, total_count = enterprise_service.list_organizations(
            page=page,
            page_size=page_size,
            filters=filters
        )
        
        return OrganizationListResponse(
            organizations=[OrganizationResponse(**org.to_dict()) for org in organizations],
            total_count=total_count,
            page=page,
            page_size=page_size,
            total_pages=(total_count + page_size - 1) // page_size
        )
        
    except Exception as e:
        logger.error(f"Error listing organizations: {e}")
        raise HTTPException(status_code=500, detail="Error listing organizations")


@router.get("/organizations/{org_id}", response_model=OrganizationResponse)
async def get_organization(
    org_id: int = Path(..., description="Organization ID"),
    db: Session = Depends(get_db),
    _: str = Depends(get_current_admin_user)
):
    """Get organization by ID."""
    try:
        require_org_admin(org_id)
        
        logger.info(f"Getting organization: {org_id}")
        
        enterprise_service = EnterpriseService(db)
        organization = enterprise_service.get_organization(org_id)
        
        if not organization:
            raise HTTPException(status_code=404, detail="Organization not found")
        
        return OrganizationResponse(**organization.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting organization {org_id}: {e}")
        raise HTTPException(status_code=500, detail="Error getting organization")


@router.put("/organizations/{org_id}", response_model=OrganizationResponse)
async def update_organization(
    org_id: int = Path(..., description="Organization ID"),
    update_data: OrganizationUpdate = ...,
    db: Session = Depends(get_db),
    _: str = Depends(get_current_admin_user)
):
    """Update organization."""
    try:
        require_org_admin(org_id)
        
        logger.info(f"Updating organization: {org_id}")
        
        enterprise_service = EnterpriseService(db)
        organization = enterprise_service.update_organization(
            org_id, 
            update_data.dict(exclude_unset=True)
        )
        
        if not organization:
            raise HTTPException(status_code=404, detail="Organization not found")
        
        return OrganizationResponse(**organization.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating organization {org_id}: {e}")
        raise HTTPException(status_code=500, detail="Error updating organization")


@router.delete("/organizations/{org_id}")
async def delete_organization(
    org_id: int = Path(..., description="Organization ID"),
    db: Session = Depends(get_db),
    _: str = Depends(get_current_admin_user)
):
    """Delete organization (soft delete)."""
    try:
        require_super_admin()
        
        logger.info(f"Deleting organization: {org_id}")
        
        enterprise_service = EnterpriseService(db)
        success = enterprise_service.delete_organization(org_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Organization not found")
        
        return {"message": "Organization deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting organization {org_id}: {e}")
        raise HTTPException(status_code=500, detail="Error deleting organization")


# User Management Endpoints

@router.post("/organizations/{org_id}/users", response_model=EnterpriseUserResponse, status_code=status.HTTP_201_CREATED)
async def create_user(
    org_id: int = Path(..., description="Organization ID"),
    user_data: EnterpriseUserCreate = ...,
    db: Session = Depends(get_db),
    _: str = Depends(get_current_admin_user)
):
    """Create a new user in an organization."""
    try:
        require_org_admin(org_id)
        
        logger.info(f"Creating user in organization {org_id}: {user_data.email}")
        
        enterprise_service = EnterpriseService(db)
        
        # Add organization ID to user data
        user_dict = user_data.dict()
        user_dict['organization_id'] = org_id
        
        user = enterprise_service.create_user(user_dict)
        
        return EnterpriseUserResponse(**user.to_dict())
        
    except Exception as e:
        logger.error(f"Error creating user: {e}")
        raise HTTPException(status_code=500, detail="Error creating user")


@router.get("/organizations/{org_id}/users", response_model=UserListResponse)
async def list_organization_users(
    org_id: int = Path(..., description="Organization ID"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=100, description="Items per page"),
    role: Optional[str] = Query(None, description="Filter by user role"),
    department_id: Optional[int] = Query(None, description="Filter by department"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    search: Optional[str] = Query(None, description="Search users"),
    db: Session = Depends(get_db),
    _: str = Depends(get_current_admin_user)
):
    """List users in an organization."""
    try:
        require_org_admin(org_id)
        
        logger.info(f"Listing users for organization {org_id}")
        
        enterprise_service = EnterpriseService(db)
        
        filters = {}
        if role:
            filters['role'] = role
        if department_id:
            filters['department_id'] = department_id
        if is_active is not None:
            filters['is_active'] = is_active
        if search:
            filters['search'] = search
        
        users, total_count = enterprise_service.list_organization_users(
            org_id=org_id,
            page=page,
            page_size=page_size,
            filters=filters
        )
        
        return UserListResponse(
            users=[EnterpriseUserResponse(**user.to_dict()) for user in users],
            total_count=total_count,
            page=page,
            page_size=page_size,
            total_pages=(total_count + page_size - 1) // page_size
        )
        
    except Exception as e:
        logger.error(f"Error listing users for organization {org_id}: {e}")
        raise HTTPException(status_code=500, detail="Error listing users")


@router.get("/users/{user_id}", response_model=EnterpriseUserResponse)
async def get_user(
    user_id: str = Path(..., description="User ID"),
    db: Session = Depends(get_db),
    _: str = Depends(get_current_admin_user)
):
    """Get user by ID."""
    try:
        logger.info(f"Getting user: {user_id}")
        
        enterprise_service = EnterpriseService(db)
        user = enterprise_service.get_user(user_id)
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        require_org_admin(user.organization_id)
        
        return EnterpriseUserResponse(**user.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Error getting user")


@router.put("/users/{user_id}", response_model=EnterpriseUserResponse)
async def update_user(
    user_id: str = Path(..., description="User ID"),
    update_data: EnterpriseUserUpdate = ...,
    db: Session = Depends(get_db),
    _: str = Depends(get_current_admin_user)
):
    """Update user."""
    try:
        logger.info(f"Updating user: {user_id}")
        
        enterprise_service = EnterpriseService(db)
        
        # Check user exists and get org ID for permission check
        existing_user = enterprise_service.get_user(user_id)
        if not existing_user:
            raise HTTPException(status_code=404, detail="User not found")
        
        require_org_admin(existing_user.organization_id)
        
        user = enterprise_service.update_user(
            user_id, 
            update_data.dict(exclude_unset=True)
        )
        
        return EnterpriseUserResponse(**user.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Error updating user")


# Department Management Endpoints

@router.post("/organizations/{org_id}/departments", response_model=DepartmentResponse, status_code=status.HTTP_201_CREATED)
async def create_department(
    org_id: int = Path(..., description="Organization ID"),
    department_data: DepartmentCreate = ...,
    db: Session = Depends(get_db),
    _: str = Depends(get_current_admin_user)
):
    """Create a new department in an organization."""
    try:
        require_org_admin(org_id)
        
        logger.info(f"Creating department in organization {org_id}: {department_data.name}")
        
        enterprise_service = EnterpriseService(db)
        
        # Add organization ID to department data
        dept_dict = department_data.dict()
        dept_dict['organization_id'] = org_id
        
        department = enterprise_service.create_department(dept_dict)
        
        return DepartmentResponse(**department.to_dict())
        
    except Exception as e:
        logger.error(f"Error creating department: {e}")
        raise HTTPException(status_code=500, detail="Error creating department")


@router.get("/organizations/{org_id}/departments", response_model=List[DepartmentResponse])
async def list_organization_departments(
    org_id: int = Path(..., description="Organization ID"),
    db: Session = Depends(get_db),
    _: str = Depends(get_current_admin_user)
):
    """List departments in an organization."""
    try:
        require_org_admin(org_id)
        
        logger.info(f"Listing departments for organization {org_id}")
        
        enterprise_service = EnterpriseService(db)
        departments = enterprise_service.list_organization_departments(org_id)
        
        return [DepartmentResponse(**dept.to_dict()) for dept in departments]
        
    except Exception as e:
        logger.error(f"Error listing departments for organization {org_id}: {e}")
        raise HTTPException(status_code=500, detail="Error listing departments")


# License Management Endpoints

@router.post("/users/{user_id}/license", response_model=LicenseResponse)
async def assign_license(
    user_id: str = Path(..., description="User ID"),
    license_data: LicenseAssignment = ...,
    db: Session = Depends(get_db),
    _: str = Depends(get_current_admin_user)
):
    """Assign a license to a user."""
    try:
        logger.info(f"Assigning license to user: {user_id}")
        
        enterprise_service = EnterpriseService(db)
        
        # Check user exists and get org ID for permission check
        user = enterprise_service.get_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        require_org_admin(user.organization_id)
        
        license = enterprise_service.assign_license(
            user_id=user_id,
            license_type=license_data.license_type,
            expiration_date=license_data.expiration_date
        )
        
        return LicenseResponse(**license.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error assigning license to user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Error assigning license")


@router.get("/organizations/{org_id}/license-usage")
async def get_license_usage(
    org_id: int = Path(..., description="Organization ID"),
    db: Session = Depends(get_db),
    _: str = Depends(get_current_admin_user)
):
    """Get license usage statistics for an organization."""
    try:
        require_org_admin(org_id)
        
        logger.info(f"Getting license usage for organization {org_id}")
        
        enterprise_service = EnterpriseService(db)
        usage_stats = enterprise_service.get_organization_license_usage(org_id)
        
        return usage_stats
        
    except Exception as e:
        logger.error(f"Error getting license usage for organization {org_id}: {e}")
        raise HTTPException(status_code=500, detail="Error getting license usage")


# Analytics and Dashboard Endpoints

@router.get("/organizations/{org_id}/analytics", response_model=OrganizationAnalyticsResponse)
async def get_organization_analytics(
    org_id: int = Path(..., description="Organization ID"),
    period_type: str = Query("monthly", description="Analytics period type"),
    start_date: Optional[str] = Query(None, description="Start date (ISO format)"),
    end_date: Optional[str] = Query(None, description="End date (ISO format)"),
    db: Session = Depends(get_db),
    _: str = Depends(get_current_admin_user)
):
    """Get comprehensive analytics for an organization."""
    try:
        require_org_admin(org_id)
        
        logger.info(f"Getting analytics for organization {org_id}")
        
        enterprise_service = EnterpriseService(db)
        
        # Parse dates if provided
        start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00')) if start_date else None
        end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00')) if end_date else None
        
        analytics = enterprise_service.generate_organization_analytics(
            org_id=org_id,
            period_type=period_type,
            start_date=start_dt,
            end_date=end_dt
        )
        
        return OrganizationAnalyticsResponse(**analytics)
        
    except Exception as e:
        logger.error(f"Error getting analytics for organization {org_id}: {e}")
        raise HTTPException(status_code=500, detail="Error getting analytics")


@router.get("/organizations/{org_id}/dashboard", response_model=DashboardResponse)
async def get_organization_dashboard(
    org_id: int = Path(..., description="Organization ID"),
    db: Session = Depends(get_db),
    _: str = Depends(get_current_admin_user)
):
    """Get comprehensive dashboard data for an organization."""
    try:
        require_org_admin(org_id)
        
        logger.info(f"Getting dashboard data for organization {org_id}")
        
        enterprise_service = EnterpriseService(db)
        dashboard_data = enterprise_service.get_organization_dashboard_data(org_id)
        
        return DashboardResponse(**dashboard_data)
        
    except Exception as e:
        logger.error(f"Error getting dashboard data for organization {org_id}: {e}")
        raise HTTPException(status_code=500, detail="Error getting dashboard data")


@router.get("/health")
async def health_check():
    """Health check endpoint for enterprise service."""
    return {
        'status': 'healthy',
        'service': 'Enterprise Dashboard',
        'version': '1.0.0',
        'features': [
            'organization_management',
            'user_administration',
            'department_management',
            'license_management',
            'analytics_reporting',
            'dashboard_insights'
        ],
        'timestamp': datetime.now().isoformat()
    }
